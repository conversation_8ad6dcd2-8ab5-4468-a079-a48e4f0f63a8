#!/usr/bin/env python3
"""
First Page Linebreak Extractor

This module extracts content from the first page of a PDF while preserving
linebreaks exactly as they appear in the PDF file using multiple approaches.

Author: Anand Jadhav
Date: 2025-01-27
"""

import json
import re
from pathlib import Path
from typing import Dict, Any, List, Tuple

# Import libraries
try:
    from docling.document_converter import DocumentConverter
    DOCLING_AVAILABLE = True
except ImportError:
    DOCLING_AVAILABLE = False
    print("⚠️ Docling not available")

try:
    import pypdfium2 as pdfium
    PYPDFIUM2_AVAILABLE = True
except ImportError:
    PYPDFIUM2_AVAILABLE = False
    print("⚠️ pypdfium2 not available")


class FirstPageLinebreakExtractor:
    """Extract first page content with preserved linebreaks."""

    def __init__(self):
        """Initialize the extractor."""
        self.docling_converter = DocumentConverter() if DOCLING_AVAILABLE else None

    def extract_first_page_with_linebreaks(self, pdf_path: str) -> Dict[str, Any]:
        """
        Extract first page content preserving full JSON structure with linebreaks in text fields.

        Args:
            pdf_path: Path to PDF file

        Returns:
            Full Docling JSON structure with linebreaks preserved in text fields
        """
        print("🔍 Extracting first page with full metadata and linebreak preservation...")

        if not DOCLING_AVAILABLE:
            print("❌ Docling not available - cannot extract full metadata structure")
            return {}

        try:
            # Get full Docling structure
            result = self.docling_converter.convert(pdf_path)
            document = result.document
            full_data = document.export_to_dict()

            print(f"📋 Original structure sections: {list(full_data.keys())}")

            # Get markdown for linebreak reference
            markdown_text = document.export_to_markdown()
            markdown_linebreaks = markdown_text.count('\n')
            print(f"📝 Markdown has {markdown_linebreaks} linebreaks for reference")

            # Enhance the full structure with linebreaks
            enhanced_data = self._enhance_structure_with_linebreaks(full_data, markdown_text, document)

            # Focus on first page only
            first_page_data = self._filter_to_first_page_only(enhanced_data)

            return first_page_data

        except Exception as e:
            print(f"❌ Error extracting with full structure: {e}")
            return {
                'error': str(e),
                'pdf_file': pdf_path,
                'success': False
            }

    def _extract_with_docling_positioning(self, pdf_path: str) -> Dict[str, Any]:
        """Extract using Docling with element positioning to reconstruct linebreaks."""
        try:
            result = self.docling_converter.convert(pdf_path)
            document = result.document

            # Get text elements for first page
            first_page_elements = []

            if hasattr(document, 'texts') and document.texts:
                for text_item in document.texts:
                    if (hasattr(text_item, 'prov') and text_item.prov and
                        len(text_item.prov) > 0 and text_item.prov[0].page_no == 1):

                        if hasattr(text_item, 'text') and text_item.text:
                            bbox = text_item.prov[0].bbox if hasattr(text_item.prov[0], 'bbox') else {}
                            first_page_elements.append({
                                'text': text_item.text.strip(),
                                'top': bbox.get('t', 0) if bbox else 0,
                                'bottom': bbox.get('b', 0) if bbox else 0,
                                'left': bbox.get('l', 0) if bbox else 0,
                                'right': bbox.get('r', 0) if bbox else 0
                            })

            # Reconstruct text with linebreaks based on positioning
            reconstructed_text = self._reconstruct_text_from_positions(first_page_elements)

            return {
                'method': 'docling_positioning',
                'text': reconstructed_text,
                'linebreaks': reconstructed_text.count('\n'),
                'elements_processed': len(first_page_elements),
                'success': True
            }

        except Exception as e:
            return {
                'method': 'docling_positioning',
                'text': '',
                'linebreaks': 0,
                'error': str(e),
                'success': False
            }

    def _extract_with_docling_markdown(self, pdf_path: str) -> Dict[str, Any]:
        """Extract using Docling markdown export."""
        try:
            result = self.docling_converter.convert(pdf_path)
            markdown_text = result.document.export_to_markdown()

            # Extract first page content from markdown
            first_page_text = self._extract_first_page_from_markdown(markdown_text)

            # Clean markdown while preserving linebreaks
            clean_text = self._clean_markdown_preserve_linebreaks(first_page_text)

            return {
                'method': 'docling_markdown',
                'text': clean_text,
                'linebreaks': clean_text.count('\n'),
                'raw_markdown_length': len(markdown_text),
                'success': True
            }

        except Exception as e:
            return {
                'method': 'docling_markdown',
                'text': '',
                'linebreaks': 0,
                'error': str(e),
                'success': False
            }

    def _extract_with_pypdfium2(self, pdf_path: str) -> Dict[str, Any]:
        """Extract using pypdfium2."""
        try:
            pdf = pdfium.PdfDocument(pdf_path)
            page = pdf.get_page(0)  # First page

            # Get text objects
            textpage = page.get_textpage()

            # Try different extraction methods
            methods_tried = []

            # Method 1: Get all text
            try:
                all_text = textpage.get_text_range()
                methods_tried.append(f"get_text_range: {all_text.count(chr(10))} linebreaks")
                if all_text.count('\n') > 0:
                    return {
                        'method': 'pypdfium2_text_range',
                        'text': all_text,
                        'linebreaks': all_text.count('\n'),
                        'methods_tried': methods_tried,
                        'success': True
                    }
            except Exception as e:
                methods_tried.append(f"get_text_range failed: {e}")

            # Method 2: Get text by bounds
            try:
                page_size = page.get_size()
                bounded_text = textpage.get_text_bounded(0, 0, page_size[0], page_size[1])
                methods_tried.append(f"get_text_bounded: {bounded_text.count(chr(10))} linebreaks")
                if bounded_text.count('\n') > 0:
                    return {
                        'method': 'pypdfium2_bounded',
                        'text': bounded_text,
                        'linebreaks': bounded_text.count('\n'),
                        'methods_tried': methods_tried,
                        'success': True
                    }
            except Exception as e:
                methods_tried.append(f"get_text_bounded failed: {e}")

            # Method 3: Character by character with positioning
            try:
                char_text = self._extract_chars_with_positioning(textpage)
                methods_tried.append(f"char_positioning: {char_text.count(chr(10))} linebreaks")
                return {
                    'method': 'pypdfium2_char_positioning',
                    'text': char_text,
                    'linebreaks': char_text.count('\n'),
                    'methods_tried': methods_tried,
                    'success': True
                }
            except Exception as e:
                methods_tried.append(f"char_positioning failed: {e}")

            return {
                'method': 'pypdfium2',
                'text': '',
                'linebreaks': 0,
                'methods_tried': methods_tried,
                'success': False
            }

        except Exception as e:
            return {
                'method': 'pypdfium2',
                'text': '',
                'linebreaks': 0,
                'error': str(e),
                'success': False
            }

    def _extract_with_hybrid_approach(self, pdf_path: str) -> Dict[str, Any]:
        """Combine multiple approaches for best linebreak preservation."""
        try:
            # Get results from both libraries
            docling_text = ""
            pypdfium_text = ""

            if DOCLING_AVAILABLE:
                result = self.docling_converter.convert(pdf_path)
                docling_text = result.document.export_to_markdown()

            if PYPDFIUM2_AVAILABLE:
                pdf = pdfium.PdfDocument(pdf_path)
                page = pdf.get_page(0)
                textpage = page.get_textpage()
                pypdfium_text = textpage.get_text_range()

            # Combine and analyze
            combined_text = self._combine_text_sources(docling_text, pypdfium_text)

            return {
                'method': 'hybrid',
                'text': combined_text,
                'linebreaks': combined_text.count('\n'),
                'docling_available': DOCLING_AVAILABLE,
                'pypdfium2_available': PYPDFIUM2_AVAILABLE,
                'success': True
            }

        except Exception as e:
            return {
                'method': 'hybrid',
                'text': '',
                'linebreaks': 0,
                'error': str(e),
                'success': False
            }

    def _reconstruct_text_from_positions(self, elements: List[Dict]) -> str:
        """Reconstruct text with linebreaks based on element positions."""
        if not elements:
            return ""

        # Sort elements by position (top to bottom, left to right)
        sorted_elements = sorted(elements, key=lambda x: (x['top'], x['left']))

        text_parts = []
        last_bottom = None

        for element in sorted_elements:
            text = element['text']
            if not text:
                continue

            current_top = element['top']

            # Add linebreak if there's a vertical gap
            if last_bottom is not None:
                vertical_gap = current_top - last_bottom
                if vertical_gap > 5:  # Threshold for line break
                    if vertical_gap > 15:  # Larger gap = paragraph break
                        text_parts.append('\n\n')
                    else:
                        text_parts.append('\n')

            text_parts.append(text)
            last_bottom = element['bottom']

        return ''.join(text_parts)

    def _extract_first_page_from_markdown(self, markdown_text: str) -> str:
        """Extract first page content from markdown text."""
        # Simple approach: take first portion of markdown
        # You can enhance this based on your specific needs
        lines = markdown_text.split('\n')
        first_page_lines = lines[:50]  # Adjust based on your PDF
        return '\n'.join(first_page_lines)

    def _clean_markdown_preserve_linebreaks(self, text: str) -> str:
        """Clean markdown while preserving all linebreaks."""
        if not text:
            return ""

        # Remove markdown formatting but keep linebreaks
        clean_text = text
        clean_text = re.sub(r'^#{1,6}\s+', '', clean_text, flags=re.MULTILINE)
        clean_text = re.sub(r'\*\*([^*]+)\*\*', r'\1', clean_text)
        clean_text = re.sub(r'\*([^*]+)\*', r'\1', clean_text)
        clean_text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', clean_text)
        clean_text = re.sub(r'```[^`]*```', '', clean_text, flags=re.DOTALL)
        clean_text = re.sub(r'`([^`]+)`', r'\1', clean_text)
        clean_text = re.sub(r'[ \t]+', ' ', clean_text)

        return clean_text.strip()

    def _extract_chars_with_positioning(self, textpage) -> str:
        """Extract characters with positioning to reconstruct linebreaks."""
        try:
            char_count = textpage.count_chars()
            chars_with_pos = []

            for i in range(min(char_count, 1000)):  # Limit for first page
                char = textpage.get_char(i)
                bbox = textpage.get_char_box(i)
                chars_with_pos.append({
                    'char': char,
                    'x': bbox[0],
                    'y': bbox[1]
                })

            # Reconstruct text with linebreaks based on Y position changes
            result_text = ""
            last_y = None

            for char_info in chars_with_pos:
                char = char_info['char']
                y = char_info['y']

                if last_y is not None and abs(y - last_y) > 5:  # Y position changed significantly
                    result_text += '\n'

                result_text += char
                last_y = y

            return result_text

        except Exception:
            return ""

    def _enhance_structure_with_linebreaks(self, full_data: Dict[str, Any], markdown_text: str, document) -> Dict[str, Any]:
        """
        Enhance the full Docling structure with linebreaks in text fields.

        Args:
            full_data: Original Docling export_to_dict() result
            markdown_text: Markdown text with linebreaks
            document: Docling document object

        Returns:
            Enhanced structure with linebreaks in text fields
        """
        print("🔧 Enhancing structure with linebreaks...")

        enhanced_data = full_data.copy()

        # Method 1: Try to use markdown linebreaks for page text
        if 'pages' in enhanced_data:
            print(f"   📄 Processing {len(enhanced_data['pages'])} pages...")
            for page_num, page_data in enhanced_data['pages'].items():
                if 'text' in page_data:
                    # Get linebreak-enhanced text for this page
                    enhanced_text = self._get_enhanced_page_text(page_num, markdown_text, document)
                    if enhanced_text and enhanced_text.count('\n') > 0:
                        page_data['text'] = enhanced_text
                        page_data['linebreaks_enhanced'] = True
                        page_data['linebreak_count'] = enhanced_text.count('\n')
                        print(f"   ✅ Page {page_num}: {enhanced_text.count('\n')} linebreaks added")

        # Method 2: Enhance individual text elements
        if 'texts' in enhanced_data:
            print(f"   📝 Processing {len(enhanced_data['texts'])} text elements...")
            enhanced_count = 0
            for text_item in enhanced_data['texts']:
                if 'text' in text_item:
                    enhanced_text = self._enhance_text_element(text_item, document)
                    if enhanced_text != text_item['text']:
                        text_item['text'] = enhanced_text
                        text_item['linebreaks_enhanced'] = True
                        enhanced_count += 1
            print(f"   ✅ Enhanced {enhanced_count} text elements")

        # Method 3: Enhance table cells
        if 'tables' in enhanced_data:
            print(f"   📊 Processing {len(enhanced_data['tables'])} tables...")
            for table in enhanced_data['tables']:
                if 'cells' in table:
                    for cell in table['cells']:
                        if 'text' in cell:
                            enhanced_text = self._enhance_text_element(cell, document)
                            if enhanced_text != cell['text']:
                                cell['text'] = enhanced_text
                                cell['linebreaks_enhanced'] = True

        return enhanced_data

    def _get_enhanced_page_text(self, page_num: str, markdown_text: str, document) -> str:
        """Get enhanced text for a specific page with linebreaks."""
        try:
            # Method 1: Use markdown text (clean it)
            if markdown_text:
                clean_markdown = self._clean_markdown_preserve_linebreaks(markdown_text)
                if clean_markdown.count('\n') > 0:
                    return clean_markdown

            # Method 2: Reconstruct from text elements with positioning
            if hasattr(document, 'texts') and document.texts:
                page_elements = []
                for text_item in document.texts:
                    if (hasattr(text_item, 'prov') and text_item.prov and
                        len(text_item.prov) > 0 and str(text_item.prov[0].page_no) == str(page_num)):

                        if hasattr(text_item, 'text') and text_item.text:
                            bbox = text_item.prov[0].bbox if hasattr(text_item.prov[0], 'bbox') else {}
                            page_elements.append({
                                'text': text_item.text.strip(),
                                'top': bbox.get('t', 0) if bbox else 0,
                                'bottom': bbox.get('b', 0) if bbox else 0,
                                'left': bbox.get('l', 0) if bbox else 0,
                                'right': bbox.get('r', 0) if bbox else 0
                            })

                if page_elements:
                    reconstructed = self._reconstruct_text_from_positions(page_elements)
                    if reconstructed.count('\n') > 0:
                        return reconstructed

            return ""

        except Exception as e:
            print(f"   ⚠️ Error enhancing page {page_num}: {e}")
            return ""

    def _enhance_text_element(self, text_item: Dict, document) -> str:
        """Enhance individual text element with linebreaks if needed."""
        original_text = text_item.get('text', '')

        # If text already has linebreaks, keep it
        if '\n' in original_text:
            return original_text

        # Try to add linebreaks based on content patterns
        enhanced_text = self._add_contextual_linebreaks(original_text)

        return enhanced_text

    def _add_contextual_linebreaks(self, text: str) -> str:
        """Add linebreaks based on text content patterns."""
        if not text or len(text) < 20:
            return text

        # Pattern 1: Break at sentence endings if text is long
        if len(text) > 80:
            sentences = re.split(r'(?<=[.!?])\s+(?=[A-Z])', text)
            if len(sentences) > 1:
                return '\n'.join(sentences)

        # Pattern 2: Break at specific keywords that often start new lines
        line_break_keywords = [
            r'\s+(Abstract[:\s])',
            r'\s+(Introduction[:\s])',
            r'\s+(Methods[:\s])',
            r'\s+(Results[:\s])',
            r'\s+(Conclusion[:\s])',
            r'\s+(Keywords[:\s])',
            r'\s+(Background[:\s])',
        ]

        for pattern in line_break_keywords:
            text = re.sub(pattern, r'\n\1', text, flags=re.IGNORECASE)

        return text

    def _filter_to_first_page_only(self, enhanced_data: Dict[str, Any]) -> Dict[str, Any]:
        """Filter the enhanced data to include only first page content."""
        print("🔍 Filtering to first page only...")

        first_page_data = enhanced_data.copy()

        # Filter pages to first page only
        if 'pages' in first_page_data:
            first_page_key = '1'  # Usually first page is key '1'
            if first_page_key in first_page_data['pages']:
                first_page_data['pages'] = {first_page_key: first_page_data['pages'][first_page_key]}
            else:
                # Take the first available page
                first_key = list(first_page_data['pages'].keys())[0]
                first_page_data['pages'] = {first_key: first_page_data['pages'][first_key]}

        # Filter texts to first page only
        if 'texts' in first_page_data:
            first_page_texts = []
            for text_item in first_page_data['texts']:
                if (text_item.get('prov') and len(text_item['prov']) > 0 and
                    text_item['prov'][0].get('page_no') == 1):
                    first_page_texts.append(text_item)
            first_page_data['texts'] = first_page_texts
            print(f"   📝 Filtered to {len(first_page_texts)} text elements from first page")

        # Filter tables to first page only
        if 'tables' in first_page_data:
            first_page_tables = []
            for table in first_page_data['tables']:
                if (table.get('prov') and len(table['prov']) > 0 and
                    table['prov'][0].get('page_no') == 1):
                    first_page_tables.append(table)
            first_page_data['tables'] = first_page_tables
            print(f"   📊 Filtered to {len(first_page_tables)} tables from first page")

        # Filter other elements similarly
        for section_name in ['pictures', 'key_value_items', 'form_items']:
            if section_name in first_page_data:
                first_page_items = []
                for item in first_page_data[section_name]:
                    if (item.get('prov') and len(item['prov']) > 0 and
                        item['prov'][0].get('page_no') == 1):
                        first_page_items.append(item)
                first_page_data[section_name] = first_page_items

        return first_page_data

    def _combine_text_sources(self, docling_text: str, pypdfium_text: str) -> str:
        """Combine text from different sources to get best linebreak preservation."""
        # Use the source with more linebreaks
        docling_linebreaks = docling_text.count('\n')
        pypdfium_linebreaks = pypdfium_text.count('\n')

        if docling_linebreaks > pypdfium_linebreaks:
            return self._clean_markdown_preserve_linebreaks(docling_text)
        elif pypdfium_linebreaks > 0:
            return pypdfium_text
        else:
            return self._clean_markdown_preserve_linebreaks(docling_text)


def main():
    """Main function to extract first page with linebreaks."""

    # Configuration
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    output_folder = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder"

    # Check if PDF exists
    if not Path(pdf_file).exists():
        print(f"❌ PDF file not found: {pdf_file}")
        return

    # Create output directory
    Path(output_folder).mkdir(parents=True, exist_ok=True)

    # Extract first page with linebreaks
    extractor = FirstPageLinebreakExtractor()
    result = extractor.extract_first_page_with_linebreaks(pdf_file)

    # Save result
    output_file = Path(output_folder) / f"{Path(pdf_file).stem}_first_page_linebreaks.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)

        print(f"\n✅ Extraction completed: {output_file}")

    # Show results for the enhanced structure
    if result and not result.get('error'):
        print(f"\n📊 Enhanced Structure Results:")

        # Show structure sections
        sections = list(result.keys())
        print(f"   📋 JSON sections: {sections}")

        # Analyze pages
        if 'pages' in result and result['pages']:
            page_count = len(result['pages'])
            print(f"   � Pages: {page_count}")

            # Check first page for linebreaks
            first_page = list(result['pages'].values())[0]
            if 'text' in first_page:
                page_text = first_page['text']
                linebreak_count = page_text.count('\n')

                print(f"   📝 First page text length: {len(page_text)} characters")
                print(f"   📝 First page linebreaks: {linebreak_count}")

                if linebreak_count > 0:
                    print(f"   ✅ SUCCESS: Page text has linebreaks!")

                    # Show sample with visible linebreaks
                    sample = page_text[:300]
                    visible_sample = sample.replace('\n', '\\n\n      ')
                    print(f"\n📝 Sample page text (first 300 chars):")
                    print(f"      '{visible_sample}...'")

                    # Show first few lines
                    lines = page_text.split('\n')
                    print(f"\n📋 First 5 lines:")
                    for i, line in enumerate(lines[:5], 1):
                        if line.strip():
                            print(f"      Line {i}: '{line.strip()}'")
                        else:
                            print(f"      Line {i}: (empty line)")
                else:
                    print(f"   ❌ No linebreaks in page text")

                # Check enhancement flags
                if first_page.get('linebreaks_enhanced'):
                    print(f"   ✅ Page text was enhanced with linebreaks")
                    print(f"   📊 Linebreak count: {first_page.get('linebreak_count', 0)}")

        # Analyze text elements
        if 'texts' in result and result['texts']:
            text_count = len(result['texts'])
            enhanced_texts = sum(1 for item in result['texts'] if item.get('linebreaks_enhanced'))
            total_text_linebreaks = sum(item.get('text', '').count('\n') for item in result['texts'])

            print(f"   � Text elements: {text_count}")
            print(f"   📝 Enhanced text elements: {enhanced_texts}")
            print(f"   📝 Total linebreaks in text elements: {total_text_linebreaks}")

            # Show sample enhanced text element
            for text_item in result['texts']:
                if text_item.get('linebreaks_enhanced') and '\n' in text_item.get('text', ''):
                    sample_text = text_item['text'][:100]
                    print(f"   📝 Sample enhanced text: '{sample_text}...'")
                    break

        # Analyze tables
        if 'tables' in result and result['tables']:
            table_count = len(result['tables'])
            print(f"   📊 Tables: {table_count}")

            # Check for enhanced table cells
            enhanced_cells = 0
            for table in result['tables']:
                if 'cells' in table:
                    enhanced_cells += sum(1 for cell in table['cells'] if cell.get('linebreaks_enhanced'))

            if enhanced_cells > 0:
                print(f"   📊 Enhanced table cells: {enhanced_cells}")

        # Show other sections
        other_sections = ['pictures', 'key_value_items', 'form_items']
        for section in other_sections:
            if section in result and result[section]:
                count = len(result[section])
                print(f"   📋 {section}: {count}")

        print(f"\n🎯 Structure Summary:")
        print(f"   ✅ Full Docling metadata preserved")
        print(f"   ✅ First page content only")
        print(f"   ✅ Linebreaks enhanced in text fields")
        print(f"   ✅ All element types maintained (texts, tables, etc.)")

    else:
        print(f"\n❌ Extraction failed")
        if result.get('error'):
            print(f"   Error: {result['error']}")


if __name__ == "__main__":
    main()
