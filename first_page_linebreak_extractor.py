#!/usr/bin/env python3
"""
First Page Linebreak Extractor

This module extracts content from the first page of a PDF while preserving
linebreaks exactly as they appear in the PDF file using multiple approaches.

Author: Anand Jadhav
Date: 2025-01-27
"""

import json
import re
from pathlib import Path
from typing import Dict, Any, List, Tuple

# Import libraries
try:
    from docling.document_converter import DocumentConverter
    DOCLING_AVAILABLE = True
except ImportError:
    DOCLING_AVAILABLE = False
    print("⚠️ Docling not available")

try:
    import pypdfium2 as pdfium
    PYPDFIUM2_AVAILABLE = True
except ImportError:
    PYPDFIUM2_AVAILABLE = False
    print("⚠️ pypdfium2 not available")


class FirstPageLinebreakExtractor:
    """Extract first page content with preserved linebreaks."""
    
    def __init__(self):
        """Initialize the extractor."""
        self.docling_converter = DocumentConverter() if DOCLING_AVAILABLE else None
    
    def extract_first_page_with_linebreaks(self, pdf_path: str) -> Dict[str, Any]:
        """
        Extract first page content with linebreaks preserved.
        
        Args:
            pdf_path: Path to PDF file
            
        Returns:
            Dictionary with extracted content and linebreaks
        """
        print("🔍 Extracting first page with linebreak preservation...")
        
        results = {
            'pdf_file': pdf_path,
            'page_number': 1,
            'extraction_methods': {},
            'best_result': None
        }
        
        # Method 1: Docling with element positioning
        if DOCLING_AVAILABLE:
            print("\n1. Trying Docling with element positioning...")
            docling_result = self._extract_with_docling_positioning(pdf_path)
            results['extraction_methods']['docling_positioning'] = docling_result
            
            if docling_result['linebreaks'] > 0:
                print(f"   ✅ Docling positioning: {docling_result['linebreaks']} linebreaks")
                results['best_result'] = docling_result
                return results
        
        # Method 2: Docling markdown approach
        if DOCLING_AVAILABLE:
            print("\n2. Trying Docling markdown approach...")
            markdown_result = self._extract_with_docling_markdown(pdf_path)
            results['extraction_methods']['docling_markdown'] = markdown_result
            
            if markdown_result['linebreaks'] > 0:
                print(f"   ✅ Docling markdown: {markdown_result['linebreaks']} linebreaks")
                if not results['best_result'] or markdown_result['linebreaks'] > results['best_result']['linebreaks']:
                    results['best_result'] = markdown_result
                    return results
        
        # Method 3: pypdfium2 approach
        if PYPDFIUM2_AVAILABLE:
            print("\n3. Trying pypdfium2 approach...")
            pypdfium_result = self._extract_with_pypdfium2(pdf_path)
            results['extraction_methods']['pypdfium2'] = pypdfium_result
            
            if pypdfium_result['linebreaks'] > 0:
                print(f"   ✅ pypdfium2: {pypdfium_result['linebreaks']} linebreaks")
                if not results['best_result'] or pypdfium_result['linebreaks'] > results['best_result']['linebreaks']:
                    results['best_result'] = pypdfium_result
                    return results
        
        # Method 4: Hybrid approach
        print("\n4. Trying hybrid approach...")
        hybrid_result = self._extract_with_hybrid_approach(pdf_path)
        results['extraction_methods']['hybrid'] = hybrid_result
        
        if hybrid_result['linebreaks'] > 0:
            print(f"   ✅ Hybrid approach: {hybrid_result['linebreaks']} linebreaks")
            if not results['best_result'] or hybrid_result['linebreaks'] > results['best_result']['linebreaks']:
                results['best_result'] = hybrid_result
        
        # Use best result or fallback
        if not results['best_result']:
            print("   ⚠️ No method found linebreaks, using first available result")
            for method_name, method_result in results['extraction_methods'].items():
                if method_result['text']:
                    results['best_result'] = method_result
                    break
        
        return results
    
    def _extract_with_docling_positioning(self, pdf_path: str) -> Dict[str, Any]:
        """Extract using Docling with element positioning to reconstruct linebreaks."""
        try:
            result = self.docling_converter.convert(pdf_path)
            document = result.document
            
            # Get text elements for first page
            first_page_elements = []
            
            if hasattr(document, 'texts') and document.texts:
                for text_item in document.texts:
                    if (hasattr(text_item, 'prov') and text_item.prov and 
                        len(text_item.prov) > 0 and text_item.prov[0].page_no == 1):
                        
                        if hasattr(text_item, 'text') and text_item.text:
                            bbox = text_item.prov[0].bbox if hasattr(text_item.prov[0], 'bbox') else {}
                            first_page_elements.append({
                                'text': text_item.text.strip(),
                                'top': bbox.get('t', 0) if bbox else 0,
                                'bottom': bbox.get('b', 0) if bbox else 0,
                                'left': bbox.get('l', 0) if bbox else 0,
                                'right': bbox.get('r', 0) if bbox else 0
                            })
            
            # Reconstruct text with linebreaks based on positioning
            reconstructed_text = self._reconstruct_text_from_positions(first_page_elements)
            
            return {
                'method': 'docling_positioning',
                'text': reconstructed_text,
                'linebreaks': reconstructed_text.count('\n'),
                'elements_processed': len(first_page_elements),
                'success': True
            }
            
        except Exception as e:
            return {
                'method': 'docling_positioning',
                'text': '',
                'linebreaks': 0,
                'error': str(e),
                'success': False
            }
    
    def _extract_with_docling_markdown(self, pdf_path: str) -> Dict[str, Any]:
        """Extract using Docling markdown export."""
        try:
            result = self.docling_converter.convert(pdf_path)
            markdown_text = result.document.export_to_markdown()
            
            # Extract first page content from markdown
            first_page_text = self._extract_first_page_from_markdown(markdown_text)
            
            # Clean markdown while preserving linebreaks
            clean_text = self._clean_markdown_preserve_linebreaks(first_page_text)
            
            return {
                'method': 'docling_markdown',
                'text': clean_text,
                'linebreaks': clean_text.count('\n'),
                'raw_markdown_length': len(markdown_text),
                'success': True
            }
            
        except Exception as e:
            return {
                'method': 'docling_markdown',
                'text': '',
                'linebreaks': 0,
                'error': str(e),
                'success': False
            }
    
    def _extract_with_pypdfium2(self, pdf_path: str) -> Dict[str, Any]:
        """Extract using pypdfium2."""
        try:
            pdf = pdfium.PdfDocument(pdf_path)
            page = pdf.get_page(0)  # First page
            
            # Get text objects
            textpage = page.get_textpage()
            
            # Try different extraction methods
            methods_tried = []
            
            # Method 1: Get all text
            try:
                all_text = textpage.get_text_range()
                methods_tried.append(f"get_text_range: {all_text.count(chr(10))} linebreaks")
                if all_text.count('\n') > 0:
                    return {
                        'method': 'pypdfium2_text_range',
                        'text': all_text,
                        'linebreaks': all_text.count('\n'),
                        'methods_tried': methods_tried,
                        'success': True
                    }
            except Exception as e:
                methods_tried.append(f"get_text_range failed: {e}")
            
            # Method 2: Get text by bounds
            try:
                page_size = page.get_size()
                bounded_text = textpage.get_text_bounded(0, 0, page_size[0], page_size[1])
                methods_tried.append(f"get_text_bounded: {bounded_text.count(chr(10))} linebreaks")
                if bounded_text.count('\n') > 0:
                    return {
                        'method': 'pypdfium2_bounded',
                        'text': bounded_text,
                        'linebreaks': bounded_text.count('\n'),
                        'methods_tried': methods_tried,
                        'success': True
                    }
            except Exception as e:
                methods_tried.append(f"get_text_bounded failed: {e}")
            
            # Method 3: Character by character with positioning
            try:
                char_text = self._extract_chars_with_positioning(textpage)
                methods_tried.append(f"char_positioning: {char_text.count(chr(10))} linebreaks")
                return {
                    'method': 'pypdfium2_char_positioning',
                    'text': char_text,
                    'linebreaks': char_text.count('\n'),
                    'methods_tried': methods_tried,
                    'success': True
                }
            except Exception as e:
                methods_tried.append(f"char_positioning failed: {e}")
            
            return {
                'method': 'pypdfium2',
                'text': '',
                'linebreaks': 0,
                'methods_tried': methods_tried,
                'success': False
            }
            
        except Exception as e:
            return {
                'method': 'pypdfium2',
                'text': '',
                'linebreaks': 0,
                'error': str(e),
                'success': False
            }
    
    def _extract_with_hybrid_approach(self, pdf_path: str) -> Dict[str, Any]:
        """Combine multiple approaches for best linebreak preservation."""
        try:
            # Get results from both libraries
            docling_text = ""
            pypdfium_text = ""
            
            if DOCLING_AVAILABLE:
                result = self.docling_converter.convert(pdf_path)
                docling_text = result.document.export_to_markdown()
            
            if PYPDFIUM2_AVAILABLE:
                pdf = pdfium.PdfDocument(pdf_path)
                page = pdf.get_page(0)
                textpage = page.get_textpage()
                pypdfium_text = textpage.get_text_range()
            
            # Combine and analyze
            combined_text = self._combine_text_sources(docling_text, pypdfium_text)
            
            return {
                'method': 'hybrid',
                'text': combined_text,
                'linebreaks': combined_text.count('\n'),
                'docling_available': DOCLING_AVAILABLE,
                'pypdfium2_available': PYPDFIUM2_AVAILABLE,
                'success': True
            }
            
        except Exception as e:
            return {
                'method': 'hybrid',
                'text': '',
                'linebreaks': 0,
                'error': str(e),
                'success': False
            }
    
    def _reconstruct_text_from_positions(self, elements: List[Dict]) -> str:
        """Reconstruct text with linebreaks based on element positions."""
        if not elements:
            return ""
        
        # Sort elements by position (top to bottom, left to right)
        sorted_elements = sorted(elements, key=lambda x: (x['top'], x['left']))
        
        text_parts = []
        last_bottom = None
        
        for element in sorted_elements:
            text = element['text']
            if not text:
                continue
            
            current_top = element['top']
            
            # Add linebreak if there's a vertical gap
            if last_bottom is not None:
                vertical_gap = current_top - last_bottom
                if vertical_gap > 5:  # Threshold for line break
                    if vertical_gap > 15:  # Larger gap = paragraph break
                        text_parts.append('\n\n')
                    else:
                        text_parts.append('\n')
            
            text_parts.append(text)
            last_bottom = element['bottom']
        
        return ''.join(text_parts)
    
    def _extract_first_page_from_markdown(self, markdown_text: str) -> str:
        """Extract first page content from markdown text."""
        # Simple approach: take first portion of markdown
        # You can enhance this based on your specific needs
        lines = markdown_text.split('\n')
        first_page_lines = lines[:50]  # Adjust based on your PDF
        return '\n'.join(first_page_lines)
    
    def _clean_markdown_preserve_linebreaks(self, text: str) -> str:
        """Clean markdown while preserving all linebreaks."""
        if not text:
            return ""
        
        # Remove markdown formatting but keep linebreaks
        clean_text = text
        clean_text = re.sub(r'^#{1,6}\s+', '', clean_text, flags=re.MULTILINE)
        clean_text = re.sub(r'\*\*([^*]+)\*\*', r'\1', clean_text)
        clean_text = re.sub(r'\*([^*]+)\*', r'\1', clean_text)
        clean_text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', clean_text)
        clean_text = re.sub(r'```[^`]*```', '', clean_text, flags=re.DOTALL)
        clean_text = re.sub(r'`([^`]+)`', r'\1', clean_text)
        clean_text = re.sub(r'[ \t]+', ' ', clean_text)
        
        return clean_text.strip()
    
    def _extract_chars_with_positioning(self, textpage) -> str:
        """Extract characters with positioning to reconstruct linebreaks."""
        try:
            char_count = textpage.count_chars()
            chars_with_pos = []
            
            for i in range(min(char_count, 1000)):  # Limit for first page
                char = textpage.get_char(i)
                bbox = textpage.get_char_box(i)
                chars_with_pos.append({
                    'char': char,
                    'x': bbox[0],
                    'y': bbox[1]
                })
            
            # Reconstruct text with linebreaks based on Y position changes
            result_text = ""
            last_y = None
            
            for char_info in chars_with_pos:
                char = char_info['char']
                y = char_info['y']
                
                if last_y is not None and abs(y - last_y) > 5:  # Y position changed significantly
                    result_text += '\n'
                
                result_text += char
                last_y = y
            
            return result_text
            
        except Exception:
            return ""
    
    def _combine_text_sources(self, docling_text: str, pypdfium_text: str) -> str:
        """Combine text from different sources to get best linebreak preservation."""
        # Use the source with more linebreaks
        docling_linebreaks = docling_text.count('\n')
        pypdfium_linebreaks = pypdfium_text.count('\n')
        
        if docling_linebreaks > pypdfium_linebreaks:
            return self._clean_markdown_preserve_linebreaks(docling_text)
        elif pypdfium_linebreaks > 0:
            return pypdfium_text
        else:
            return self._clean_markdown_preserve_linebreaks(docling_text)


def main():
    """Main function to extract first page with linebreaks."""
    
    # Configuration
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    output_folder = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder"
    
    # Check if PDF exists
    if not Path(pdf_file).exists():
        print(f"❌ PDF file not found: {pdf_file}")
        return
    
    # Create output directory
    Path(output_folder).mkdir(parents=True, exist_ok=True)
    
    # Extract first page with linebreaks
    extractor = FirstPageLinebreakExtractor()
    result = extractor.extract_first_page_with_linebreaks(pdf_file)
    
    # Save result
    output_file = Path(output_folder) / f"{Path(pdf_file).stem}_first_page_linebreaks.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
        print(f"\n✅ Extraction completed: {output_file}")
    
    # Show results
    if result['best_result']:
        best = result['best_result']
        print(f"\n📊 Best Result:")
        print(f"   Method: {best['method']}")
        print(f"   Text length: {len(best['text'])} characters")
        print(f"   Linebreaks: {best['linebreaks']}")
        
        if best['linebreaks'] > 0:
            print(f"   ✅ SUCCESS: Linebreaks preserved!")
            
            # Show sample
            sample = best['text'][:300]
            print(f"\n📝 Sample text (first 300 chars):")
            print(f"'{sample}...'")
            
            # Show lines
            lines = best['text'].split('\n')
            print(f"\n📋 First 5 lines:")
            for i, line in enumerate(lines[:5], 1):
                print(f"   Line {i}: '{line.strip()}'")
        else:
            print(f"   ❌ No linebreaks found")
    else:
        print(f"\n❌ No successful extraction method found")
    
    # Show all methods tried
    print(f"\n🔍 Methods tried:")
    for method_name, method_result in result['extraction_methods'].items():
        status = "✅" if method_result['success'] else "❌"
        linebreaks = method_result.get('linebreaks', 0)
        print(f"   {status} {method_name}: {linebreaks} linebreaks")


if __name__ == "__main__":
    main()
