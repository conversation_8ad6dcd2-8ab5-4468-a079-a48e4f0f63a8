"""
Highly optimized command-line interface for the MetaParse package.

This module provides a production-ready implementation with aggressive optimizations
for maximum document processing speed, especially with large batches of files.
"""

import os
import time
import argparse
import asyncio
import glob
import pandas as pd
import multiprocessing
import psutil
import concurrent.futures
import tempfile
import subprocess
import platform
from typing import List, Dict, Any, Optional, Tuple

from metaparse.core.pipeline import DocExtractionPipeline
HF_HUB_DISABLE_SYMLINKS_WARNING = False

def set_process_priority(high_priority=True):
    """Set the priority of the current process for better performance."""
    try:
        p = psutil.Process(os.getpid())
        if high_priority:
            # Set high priority based on platform
            if os.name == 'nt':  # Windows
                p.nice(psutil.HIGH_PRIORITY_CLASS)
            else:  # Unix-like systems
                p.nice(-10)  # Lower values mean higher priority
            print("Process priority set to HIGH")
        else:
            # Set normal priority based on platform
            if os.name == 'nt':  # Windows
                p.nice(psutil.NORMAL_PRIORITY_CLASS)
            else:  # Unix-like systems
                p.nice(0)
            print("Process priority set to NORMAL")
    except Exception as e:
        print(f"Failed to set process priority: {e}")


def optimize_system():
    """Optimize system settings for maximum performance."""
    # Set process priority
    set_process_priority(high_priority=True)

    # Disable garbage collection during processing
    try:
        import gc
        gc.disable()
        print("Garbage collection disabled for better performance")
    except Exception as e:
        print(f"Failed to disable garbage collection: {e}")


def restore_system():
    """Restore system settings after processing."""
    # Set process priority back to normal
    set_process_priority(high_priority=False)

    # Re-enable garbage collection
    try:
        import gc
        gc.enable()
        gc.collect()
        print("Garbage collection re-enabled")
    except Exception as e:
        print(f"Failed to re-enable garbage collection: {e}")


def find_document_files_recursive(folder_path: str) -> List[str]:
    """
    Recursively find all PDF and DOCX files in a folder and its subfolders.

    Args:
        folder_path: Path to the folder to search

    Returns:
        List of file paths to PDF and DOCX files
    """
    document_files = []

    # Check if the folder exists
    if not os.path.isdir(folder_path):
        print(f"Warning: {folder_path} is not a valid directory")
        return document_files

    # Walk through the directory tree
    for root, _, files in os.walk(folder_path):
        for file in files:
            # Check if the file has a supported extension
            if file.lower().endswith(('.pdf', '.docx')):
                # Get the full path to the file
                file_path = os.path.join(root, file)
                document_files.append(file_path)

    return document_files


def create_output_folders(doc_files: List[str], output_base_folder: str, input_folder: str) -> List[str]:
    """
    Create output folders for each document with proper naming based on subfolder structure.

    Args:
        doc_files: List of document file paths
        output_base_folder: Base folder for output
        input_folder: Input folder containing the documents

    Returns:
        List of output folder paths
    """
    save_paths = []
    for doc_file in doc_files:
        # Extract filename without extension
        doc_filename = os.path.basename(doc_file)
        doc_name = os.path.splitext(doc_filename)[0]

        # Create a unique output folder name
        # If the file is in a subfolder, include the subfolder structure in the output folder name
        if os.path.dirname(doc_file) != os.path.normpath(input_folder):
            # Get the relative path from the input folder to the file's directory
            rel_path = os.path.relpath(os.path.dirname(doc_file), input_folder)
            # Replace path separators with underscores to create a valid folder name
            subfolder_part = rel_path.replace(os.path.sep, '_')
            # Combine the subfolder part with the document name
            output_folder_name = f"{subfolder_part}_{doc_name}"
        else:
            output_folder_name = doc_name

        output_folder = os.path.join(output_base_folder, output_folder_name)
        save_paths.append(output_folder)

    return save_paths


def process_document_optimized(doc_path: str, output_folder: str, optimization_level: str = 'speed', use_cache: bool = True) -> Dict[str, Any]:
    """
    Process a single document with optimized settings.

    Args:
        doc_path: Path to the document file
        output_folder: Path to the output folder
        optimization_level: Optimization level ('speed', 'balanced', or 'quality')
        use_cache: Whether to use caching

    Returns:
        Dictionary with extraction results
    """
    try:
        # Create a temporary directory for processing
        with tempfile.TemporaryDirectory() as temp_dir:
            # If it's a DOCX file, convert to PDF first for faster processing
            if doc_path.lower().endswith('.docx'):
                try:
                    # Use LibreOffice for fast conversion (if available)
                    if platform.system() == 'Windows':
                        soffice_path = r"C:\Program Files\LibreOffice\program\soffice.exe"
                    else:  # Linux/Mac
                        soffice_path = "soffice"

                    if os.path.exists(soffice_path) or platform.system() != 'Windows':
                        pdf_filename = os.path.splitext(os.path.basename(doc_path))[0] + ".pdf"
                        pdf_path = os.path.join(temp_dir, pdf_filename)

                        # Convert DOCX to PDF
                        subprocess.run([
                            soffice_path,
                            "--headless",
                            "--convert-to", "pdf",
                            "--outdir", temp_dir,
                            doc_path
                        ], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=60)

                        if os.path.exists(pdf_path):
                            # Use the converted PDF instead
                            doc_path = pdf_path
                            print(f"Converted DOCX to PDF for faster processing: {os.path.basename(doc_path)}")
                except Exception as e:
                    print(f"DOCX conversion failed, using original file: {e}")

            # Process the document with optimized settings
            pipeline = DocExtractionPipeline(
                doc_path,
                output_folder,
                optimization_level=optimization_level,
                use_cache=use_cache
            )
            pipeline.run_pipeline()

            return {
                'doc_path': doc_path,
                'output_folder': output_folder,
                'success': True
            }
    except Exception as e:
        print(f"❌ Error processing {os.path.basename(doc_path)}: {e}")
        return {
            'doc_path': doc_path,
            'output_folder': output_folder,
            'success': False,
            'error': str(e)
        }


def process_batch_optimized(doc_files: List[str], output_folders: List[str],
                           optimization_level: str = 'speed',
                           max_workers: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    Process a batch of documents with optimized parallel execution.

    Args:
        doc_files: List of document file paths
        output_folders: List of output folder paths
        optimization_level: Optimization level ('speed', 'balanced', or 'quality')
        max_workers: Maximum number of worker processes

    Returns:
        List of dictionaries with extraction results
    """
    # Determine optimal number of workers
    if max_workers is None:
        # Use CPU count minus 1 to leave one core for system operations
        max_workers = max(1, multiprocessing.cpu_count() - 1)

    print(f"Processing with {max_workers} workers")

    # Optimize system settings
    optimize_system()

    # Create a process pool with optimized settings
    results = []
    with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_doc = {
            executor.submit(
                process_document_optimized,
                doc_path,
                output_folder,
                optimization_level
            ): (i, doc_path)
            for i, (doc_path, output_folder) in enumerate(zip(doc_files, output_folders))
        }

        # Process results as they complete
        for future in concurrent.futures.as_completed(future_to_doc):
            i, doc_path = future_to_doc[future]
            try:
                result = future.result()
                results.append(result)

                # Print progress
                print(f"Completed {len(results)}/{len(doc_files)}: {os.path.basename(doc_path)}")
                successful = sum(1 for r in results if r.get('success', False))
                print(f"Progress: {len(results)}/{len(doc_files)} files processed, {successful} successful")
            except Exception as e:
                print(f"❌ Error processing {os.path.basename(doc_path)}: {e}")
                results.append({
                    'doc_path': doc_path,
                    'output_folder': output_folders[i],
                    'success': False,
                    'error': str(e)
                })

    # Restore system settings
    restore_system()

    return results


def generate_summary_sheet(processed_docs: List[Dict[str, Any]], output_base_folder: str, input_path: str, is_batch_mode: bool = False) -> str:
    """
    Generate a summary Excel sheet with details about each processed document.

    Args:
        processed_docs: List of dictionaries with processing results
        output_base_folder: Base folder where output is stored
        input_path: Path to the input file or folder
        is_batch_mode: Whether processing was done in batch mode

    Returns:
        Path to the generated summary sheet
    """
    summary_data = []

    for doc_result in processed_docs:
        doc_path = doc_result.get('doc_path', '')
        output_folder = doc_result.get('output_folder', '')
        success = doc_result.get('success', False)

        if not success or not os.path.exists(output_folder):
            # Skip failed documents
            continue

        doc_name = os.path.basename(doc_path)

        # Count JSON files
        json_files = glob.glob(os.path.join(output_folder, "*.json"))
        num_json_files = len(json_files)

        # Count table Excel files
        table_files = glob.glob(os.path.join(output_folder, "table_*.xlsx"))
        num_tables = len(table_files)

        # Count image files
        images_folder = os.path.join(output_folder, "images")
        if os.path.exists(images_folder):
            image_files = glob.glob(os.path.join(images_folder, "*.png"))
            num_images = len(image_files)
        else:
            # Fallback to check for images in the main output folder
            image_files = glob.glob(os.path.join(output_folder, "*.png"))
            num_images = len(image_files)

        # Add to summary data
        summary_data.append({
            'document_name': doc_name,
            'number_of_json_data_files': num_json_files,
            'number_of_tables': num_tables,
            'number_of_figures': num_images
        })

    # Check if we have any data to include in the summary
    if not summary_data:
        print("⚠️ No valid document data found for summary sheet.")
        return None

    # Create DataFrame
    df = pd.DataFrame(summary_data)

    # Determine summary sheet name
    if is_batch_mode:
        # Use input folder name for batch mode
        input_folder_name = os.path.basename(os.path.normpath(input_path))
        summary_file_name = f"{input_folder_name}_summary.xlsx"
    else:
        # Use input document name for single file mode
        input_doc_name = os.path.basename(input_path)
        input_doc_name_without_ext = os.path.splitext(input_doc_name)[0]
        summary_file_name = f"{input_doc_name_without_ext}_summary.xlsx"

    # Create summary file path (always in the output folder)
    summary_file_path = os.path.join(output_base_folder, summary_file_name)

    # Save to Excel
    df.to_excel(summary_file_path, index=False)

    print(f"✅ Summary sheet generated at: {summary_file_path}")
    return summary_file_path


async def main_async():
    """Main entry point for the highly optimized asynchronous CLI."""
    print("MetaParse High-Performance Extraction Process Started...")

    start = time.time()
    parser = argparse.ArgumentParser(description="High-Performance Document Data Extraction Tool")
    parser.add_argument("--input", "-i", required=True, help="Input document file or folder")
    parser.add_argument("--output", "-o", required=True, help="Output base folder")
    parser.add_argument("--workers", "-w", type=int, default=None, help="Number of concurrent workers")
    parser.add_argument("--optimization", "-opt", choices=["speed", "balanced", "quality"],
                        default="speed", help="Optimization level")
    parser.add_argument("--no-cache", action="store_true", help="Disable caching")

    args = parser.parse_args()

    # Create output directory if it doesn't exist
    os.makedirs(args.output, exist_ok=True)
    total_files = 0
    # Determine if input is a file or directory
    if os.path.isdir(args.input):
        # Batch processing mode
        input_folder = args.input
        output_base_folder = args.output

        # Recursively collect all document files from the folder and its subfolders
        doc_files = find_document_files_recursive(input_folder)

        if not doc_files:
            print("No PDF or DOCX files found in the specified folder or its subfolders.")
            return 1

        print(f"Found {len(doc_files)} document files to process.")
        total_files = len(doc_files)
        # Print the list of files that will be processed
        print("Files to be processed:")
        for i, file_path in enumerate(doc_files, 1):
            print(f"  {i}. {file_path}")

        # Create output folders
        output_folders = create_output_folders(doc_files, output_base_folder, input_folder)

        # Process documents with optimized batch processing
        # Run in a separate thread to not block the event loop
        loop = asyncio.get_running_loop()
        results = await loop.run_in_executor(
            None,
            process_batch_optimized,
            doc_files,
            output_folders,
            args.optimization,
            args.workers
        )

        # Summary
        successful = sum(1 for r in results if r.get('success', False))
        print(f"\nSuccessfully processed: {successful}/{len(results)} files")

        # Generate summary sheet
        generate_summary_sheet(results, output_base_folder, args.input, is_batch_mode=True)

    else:
        # Single file processing mode
        doc_file = args.input

        if not os.path.exists(doc_file):
            print(f"Document file not found: {doc_file}")
            return 1

        # Extract filename without extension for any supported document type
        doc_filename = os.path.basename(doc_file)
        doc_name = os.path.splitext(doc_filename)[0]
        output_folder = os.path.join(args.output, doc_name)

        # Process single document with optimized settings
        print(f"Processing single document: {doc_file}")
        result = process_document_optimized(
            doc_file,
            output_folder,
            optimization_level=args.optimization,
            use_cache=not args.no_cache
        )

        # Generate summary sheet for single file
        if result.get('success', False):
            generate_summary_sheet([result], args.output, args.input, is_batch_mode=False)
            print(f"✅ Document processed successfully: {doc_file}")
        else:
            print(f"❌ Failed to process document: {doc_file}")
            return 1
    total_time = round((time.time() - start), 2)
    print(f"✅✅ Complete Extraction Time = {total_time} Seconds. ✅✅")
    print(f"Average Extraction Time {round((total_time/total_files),1)} Seconds/doc.")

    return 0


def main():
    """Entry point for the command-line interface.""" 
    return asyncio.run(main_async())


if __name__ == "__main__":
    import sys
    sys.exit(main())


### 
# python cli_optimized_new.py --input "C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder" --output "C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder" --optimization speed --workers 4
