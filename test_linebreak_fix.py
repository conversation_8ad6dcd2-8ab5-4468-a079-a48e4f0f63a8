#!/usr/bin/env python3
"""
Test script to verify that linebreak preservation is working correctly.

This script tests the enhanced linebreak preservation functionality
and shows the difference in output.

Author: Anand Jadhav
Date: 2025-01-27
"""

import os
import json
import sys
from pathlib import Path

def test_linebreak_preservation():
    """Test the enhanced linebreak preservation functionality."""
    
    print("🧪 Testing Enhanced Linebreak Preservation")
    print("=" * 50)
    
    # Your PDF file
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    output_folder = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder"
    
    if not Path(pdf_file).exists():
        print(f"❌ PDF file not found: {pdf_file}")
        print("💡 Please ensure the PDF file exists at the specified location")
        return False
    
    print(f"📄 Testing with: {Path(pdf_file).name}")
    print(f"📁 Output folder: {output_folder}")
    
    try:
        # Import the enhanced extractor
        sys.path.insert(0, '.')
        from metaparse.extractors.doc_extractor_linebreak import DocExtractor
        
        # Test with linebreak preservation enabled
        print("\n🔄 Testing with enhanced linebreak preservation...")
        
        custom_settings = {
            'text_processing': {
                'preserve_linebreaks': True,
                'linebreak_character': '\n',
                'paragraph_separator': '\n\n'
            }
        }
        
        extractor = DocExtractor(
            pdf_file,
            optimization_level='quality',
            use_cache=False,
            custom_settings=custom_settings
        )
        
        result = extractor.extract_text_optimized()
        
        # Save result
        Path(output_folder).mkdir(parents=True, exist_ok=True)
        output_file = Path(output_folder) / "test_linebreak_enhanced.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Enhanced extraction completed")
        print(f"📁 Output saved to: {output_file}")
        
        # Analyze the results
        analyze_linebreak_results(result, "Enhanced")
        
        # Test with linebreak preservation disabled for comparison
        print("\n🔄 Testing with linebreak preservation disabled...")
        
        custom_settings_disabled = {
            'text_processing': {
                'preserve_linebreaks': False
            }
        }
        
        extractor_disabled = DocExtractor(
            pdf_file,
            optimization_level='quality',
            use_cache=False,
            custom_settings=custom_settings_disabled
        )
        
        result_disabled = extractor_disabled.extract_text_optimized()
        
        # Save result
        output_file_disabled = Path(output_folder) / "test_linebreak_disabled.json"
        
        with open(output_file_disabled, 'w', encoding='utf-8') as f:
            json.dump(result_disabled, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Disabled extraction completed")
        print(f"📁 Output saved to: {output_file_disabled}")
        
        # Analyze the results
        analyze_linebreak_results(result_disabled, "Disabled")
        
        # Compare results
        compare_results(result, result_disabled)
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_linebreak_results(result: dict, mode: str):
    """Analyze the linebreak preservation results."""
    
    print(f"\n📊 Analysis - {mode} Mode:")
    print("-" * 30)
    
    if 'pages' in result and result['pages']:
        total_pages = len(result['pages'])
        print(f"📄 Total pages: {total_pages}")
        
        # Analyze first page
        first_page_key = list(result['pages'].keys())[0]
        first_page = result['pages'][first_page_key]
        
        if 'text' in first_page:
            text = first_page['text']
            
            # Count linebreaks
            linebreak_count = text.count('\n')
            total_chars = len(text)
            
            print(f"📝 First page text length: {total_chars:,} characters")
            print(f"🔍 Linebreaks found: {linebreak_count}")
            
            if linebreak_count > 0:
                print(f"✅ Linebreaks are present in the text")
            else:
                print(f"❌ No linebreaks found in the text")
            
            # Show sample text
            sample_text = text[:300]
            print(f"\n📖 Sample text (first 300 chars):")
            print(f"'{sample_text}...'")
            
            # Check for linebreak preservation indicators
            if first_page.get('linebreaks_preserved'):
                print(f"✅ Linebreak preservation flag: True")
                if 'elements_processed' in first_page:
                    print(f"🔧 Elements processed: {first_page['elements_processed']}")
                if 'paragraph_count' in first_page:
                    print(f"📄 Paragraphs detected: {first_page['paragraph_count']}")
            else:
                print(f"❌ Linebreak preservation flag: False or missing")
        
        else:
            print("⚠️ No text found in first page")
    
    else:
        print("❌ No pages found in result")

def compare_results(result_with: dict, result_without: dict):
    """Compare results with and without linebreak preservation."""
    
    print(f"\n🔍 Comparison Results:")
    print("=" * 40)
    
    if ('pages' in result_with and result_with['pages'] and 
        'pages' in result_without and result_without['pages']):
        
        # Get first page from both results
        first_page_key = list(result_with['pages'].keys())[0]
        page_with = result_with['pages'][first_page_key]
        page_without = result_without['pages'][first_page_key]
        
        text_with = page_with.get('text', '')
        text_without = page_without.get('text', '')
        
        linebreaks_with = text_with.count('\n')
        linebreaks_without = text_without.count('\n')
        
        print(f"📊 Linebreak Comparison:")
        print(f"   With preservation: {linebreaks_with} linebreaks")
        print(f"   Without preservation: {linebreaks_without} linebreaks")
        print(f"   Difference: {linebreaks_with - linebreaks_without} linebreaks")
        
        if linebreaks_with > linebreaks_without:
            print(f"✅ Enhanced preservation is working! More linebreaks detected.")
        elif linebreaks_with == linebreaks_without:
            print(f"⚠️ Same number of linebreaks - may need further enhancement")
        else:
            print(f"❌ Unexpected result - fewer linebreaks with preservation enabled")
        
        # Show side-by-side comparison of sample text
        print(f"\n📖 Sample Text Comparison (first 200 chars):")
        print(f"\n✅ WITH linebreak preservation:")
        print(f"'{text_with[:200]}...'")
        print(f"\n❌ WITHOUT linebreak preservation:")
        print(f"'{text_without[:200]}...'")
        
    else:
        print("❌ Cannot compare - missing page data")

def main():
    """Main function to run the linebreak preservation test."""
    
    print("🚀 Enhanced Linebreak Preservation Test")
    print("=" * 60)
    
    print("\n💡 This test verifies that the enhanced linebreak preservation")
    print("   functionality is working correctly with your PDF file.")
    
    # Check if we're in the right directory
    if not Path("metaparse/extractors/doc_extractor_linebreak.py").exists():
        print("\n❌ Error: Please run this script from the MetaParse project root directory")
        print("💡 Current directory should contain the 'metaparse' folder")
        return
    
    # Run the test
    success = test_linebreak_preservation()
    
    if success:
        print("\n🎯 Test Summary:")
        print("✅ Enhanced linebreak preservation tested")
        print("✅ Comparison with disabled mode completed")
        print("✅ Results saved to extracted_data_folder")
        
        print("\n📚 Next Steps:")
        print("1. Check the generated JSON files in extracted_data_folder:")
        print("   - test_linebreak_enhanced.json")
        print("   - test_linebreak_disabled.json")
        print("2. Compare the 'text' fields to see the difference")
        print("3. Look for \\n characters in the enhanced version")
        
    else:
        print("\n❌ Test failed - please check the error messages above")
    
    print("\n✨ Test completed!")

if __name__ == "__main__":
    main()
