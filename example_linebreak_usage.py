#!/usr/bin/env python3
"""
Example script demonstrating linebreak preservation in Docling text extraction.

This script shows practical examples of how to use the enhanced DocExtractor
to preserve linebreaks when extracting text from PDF documents.

Author: Anand Jadhav
Date: 2025-01-27
"""

import os
import json
from metaparse.extractors.doc_extractor_linebreak import DocExtractor, DocProcessor

def example_basic_usage():
    """Basic example of using linebreak preservation."""
    
    print("📖 Example 1: Basic Linebreak Preservation")
    print("-" * 50)
    
    # Replace with your actual PDF file path
    pdf_path = "sample_document.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"⚠️ Please provide a valid PDF file at: {pdf_path}")
        return
    
    # Create extractor with default settings (linebreak preservation enabled)
    extractor = DocExtractor(
        doc_path=pdf_path,
        optimization_level='quality',
        use_cache=False
    )
    
    # Extract text
    result = extractor.extract_text_optimized()
    
    # Save to file
    output_file = "extracted_with_linebreaks.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Text extracted with linebreaks preserved")
    print(f"📁 Output saved to: {output_file}")
    
    # Show sample
    if result.get('pages'):
        first_page = list(result['pages'].values())[0]
        sample_text = first_page.get('text', '')[:200]
        print(f"\n📝 Sample text:")
        print(f"'{sample_text}...'")
        
        # Count linebreaks
        linebreak_count = sample_text.count('\n')
        print(f"🔍 Linebreaks found: {linebreak_count}")

def example_custom_settings():
    """Example with custom linebreak settings."""
    
    print("\n📖 Example 2: Custom Linebreak Settings")
    print("-" * 50)
    
    pdf_path = "sample_document.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"⚠️ Please provide a valid PDF file at: {pdf_path}")
        return
    
    # Custom settings with pipe separators
    custom_settings = {
        'text_processing': {
            'preserve_linebreaks': True,
            'linebreak_character': ' | ',      # Use pipe instead of newline
            'paragraph_separator': ' || '      # Double pipe for paragraphs
        }
    }
    
    extractor = DocExtractor(
        doc_path=pdf_path,
        optimization_level='quality',
        use_cache=False,
        custom_settings=custom_settings
    )
    
    result = extractor.extract_text_optimized()
    
    # Save to file
    output_file = "extracted_with_pipes.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Text extracted with pipe separators")
    print(f"📁 Output saved to: {output_file}")
    
    # Show sample
    if result.get('pages'):
        first_page = list(result['pages'].values())[0]
        sample_text = first_page.get('text', '')[:200]
        print(f"\n📝 Sample text with pipes:")
        print(f"'{sample_text}...'")

def example_comparison():
    """Example comparing with and without linebreak preservation."""
    
    print("\n📖 Example 3: Comparison - With vs Without Linebreaks")
    print("-" * 50)
    
    pdf_path = "sample_document.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"⚠️ Please provide a valid PDF file at: {pdf_path}")
        return
    
    # Extract WITH linebreak preservation
    print("🔄 Extracting WITH linebreak preservation...")
    extractor_with = DocExtractor(
        doc_path=pdf_path,
        optimization_level='quality',
        use_cache=False,
        custom_settings={
            'text_processing': {
                'preserve_linebreaks': True,
                'linebreak_character': '\n',
                'paragraph_separator': '\n\n'
            }
        }
    )
    
    result_with = extractor_with.extract_text_optimized()
    
    # Extract WITHOUT linebreak preservation
    print("🔄 Extracting WITHOUT linebreak preservation...")
    extractor_without = DocExtractor(
        doc_path=pdf_path,
        optimization_level='quality',
        use_cache=False,
        custom_settings={
            'text_processing': {
                'preserve_linebreaks': False
            }
        }
    )
    
    result_without = extractor_without.extract_text_optimized()
    
    # Compare results
    if result_with.get('pages') and result_without.get('pages'):
        page_with = list(result_with['pages'].values())[0]
        page_without = list(result_without['pages'].values())[0]
        
        text_with = page_with.get('text', '')[:300]
        text_without = page_without.get('text', '')[:300]
        
        print("\n📊 Comparison Results:")
        print("\n✅ WITH linebreak preservation:")
        print(f"'{text_with}...'")
        print(f"Linebreaks: {text_with.count(chr(10))}")
        
        print("\n❌ WITHOUT linebreak preservation:")
        print(f"'{text_without}...'")
        print(f"Linebreaks: {text_without.count(chr(10))}")
        
        # Save both results
        with open("comparison_with_linebreaks.json", 'w', encoding='utf-8') as f:
            json.dump(result_with, f, indent=2, ensure_ascii=False)
        
        with open("comparison_without_linebreaks.json", 'w', encoding='utf-8') as f:
            json.dump(result_without, f, indent=2, ensure_ascii=False)
        
        print("\n📁 Comparison files saved:")
        print("- comparison_with_linebreaks.json")
        print("- comparison_without_linebreaks.json")

def example_batch_processing():
    """Example of batch processing with linebreak preservation."""
    
    print("\n📖 Example 4: Batch Processing with Linebreak Preservation")
    print("-" * 50)
    
    # List of PDF files (replace with your actual files)
    pdf_files = [
        "document1.pdf",
        "document2.pdf",
        "document3.pdf"
    ]
    
    # Filter existing files
    existing_files = [f for f in pdf_files if os.path.exists(f)]
    
    if not existing_files:
        print("⚠️ No PDF files found. Please provide valid PDF files.")
        print("Expected files:", pdf_files)
        return
    
    print(f"📚 Processing {len(existing_files)} documents...")
    
    # Create processor with linebreak preservation
    processor = DocProcessor(
        optimization_level='quality',
        use_cache=False,
        output_dir='batch_output_with_linebreaks'
    )
    
    # Process documents sequentially
    results = processor.process_docs_sequential(existing_files)
    
    # Summary
    successful = sum(1 for r in results if r['success'])
    failed = len(results) - successful
    
    print(f"\n📊 Batch Processing Summary:")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📁 Output directory: batch_output_with_linebreaks/")
    
    # Show details for successful extractions
    for result in results:
        if result['success']:
            print(f"  ✅ {os.path.basename(result['doc_path'])}: {result['pages']} pages, {result['time']}s")
        else:
            print(f"  ❌ {os.path.basename(result['doc_path'])}: {result['error']}")

def main():
    """Run all examples."""
    
    print("🚀 Docling Linebreak Preservation Examples")
    print("=" * 60)
    
    print("\n💡 These examples demonstrate how to preserve linebreaks")
    print("   when extracting text from PDF documents using Docling.")
    print("\n📋 Make sure you have PDF files available for testing.")
    
    # Run examples
    example_basic_usage()
    example_custom_settings()
    example_comparison()
    example_batch_processing()
    
    print("\n✨ Examples completed!")
    print("\n📚 For more information, see:")
    print("   - LINEBREAK_PRESERVATION_GUIDE.md")
    print("   - test_linebreak_preservation.py")

if __name__ == "__main__":
    main()
