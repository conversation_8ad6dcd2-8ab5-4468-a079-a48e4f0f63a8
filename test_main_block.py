#!/usr/bin/env python3
"""
Test script for the main block functionality of doc_extractor_linebreak.py

This script tests the command-line interface and main block functionality
without requiring actual PDF files.

Author: <PERSON> Jadhav
Date: 2025-01-27
"""

import sys
import subprocess
import os
from pathlib import Path

def test_demo_mode():
    """Test the demo mode functionality."""
    print("🧪 Testing Demo Mode")
    print("-" * 30)
    
    try:
        # Test demo mode
        result = subprocess.run([
            sys.executable, 
            "metaparse/extractors/doc_extractor_linebreak.py", 
            "--demo"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Demo mode executed successfully")
            print("📋 Demo output preview:")
            print(result.stdout[:300] + "..." if len(result.stdout) > 300 else result.stdout)
        else:
            print("❌ Demo mode failed")
            print("Error:", result.stderr)
            
    except subprocess.TimeoutExpired:
        print("⏰ Demo mode timed out")
    except Exception as e:
        print(f"❌ Error running demo mode: {e}")

def test_help_functionality():
    """Test the help functionality."""
    print("\n🧪 Testing Help Functionality")
    print("-" * 30)
    
    try:
        # Test help
        result = subprocess.run([
            sys.executable, 
            "metaparse/extractors/doc_extractor_linebreak.py", 
            "--help"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Help functionality works")
            print("📋 Help output preview:")
            # Show first few lines of help
            help_lines = result.stdout.split('\n')[:10]
            for line in help_lines:
                print(f"   {line}")
        else:
            print("❌ Help functionality failed")
            print("Error:", result.stderr)
            
    except subprocess.TimeoutExpired:
        print("⏰ Help command timed out")
    except Exception as e:
        print(f"❌ Error running help: {e}")

def test_import_functionality():
    """Test that the module can be imported correctly."""
    print("\n🧪 Testing Import Functionality")
    print("-" * 30)
    
    try:
        # Test import
        import sys
        sys.path.insert(0, '.')
        
        from metaparse.extractors.doc_extractor_linebreak import (
            DocExtractor, 
            DocExtractorConfig, 
            DocProcessor,
            main,
            run_demo
        )
        
        print("✅ All classes and functions imported successfully")
        
        # Test configuration creation
        config = DocExtractorConfig()
        print("✅ DocExtractorConfig created successfully")
        
        # Check linebreak settings
        text_settings = config.settings.get('text_processing', {})
        preserve_linebreaks = text_settings.get('preserve_linebreaks', False)
        linebreak_char = text_settings.get('linebreak_character', '')
        
        print(f"📋 Default settings:")
        print(f"   preserve_linebreaks: {preserve_linebreaks}")
        print(f"   linebreak_character: '{repr(linebreak_char)}'")
        
        if preserve_linebreaks and linebreak_char == '\n':
            print("✅ Linebreak preservation is enabled by default")
        else:
            print("⚠️ Linebreak preservation settings may need verification")
            
    except ImportError as e:
        print(f"❌ Import failed: {e}")
    except Exception as e:
        print(f"❌ Error testing imports: {e}")

def test_configuration_options():
    """Test different configuration options."""
    print("\n🧪 Testing Configuration Options")
    print("-" * 30)
    
    try:
        from metaparse.extractors.doc_extractor_linebreak import DocExtractorConfig
        
        # Test default configuration
        default_config = DocExtractorConfig()
        print("✅ Default configuration created")
        
        # Test custom configuration
        custom_settings = {
            'text_processing': {
                'preserve_linebreaks': True,
                'linebreak_character': '|',
                'paragraph_separator': '||'
            }
        }
        
        custom_config = DocExtractorConfig(custom_settings=custom_settings)
        print("✅ Custom configuration created")
        
        # Verify custom settings
        text_settings = custom_config.settings.get('text_processing', {})
        if (text_settings.get('linebreak_character') == '|' and 
            text_settings.get('paragraph_separator') == '||'):
            print("✅ Custom settings applied correctly")
        else:
            print("⚠️ Custom settings may not be applied correctly")
            
    except Exception as e:
        print(f"❌ Error testing configurations: {e}")

def test_file_validation():
    """Test file validation without actual PDF files."""
    print("\n🧪 Testing File Validation")
    print("-" * 30)
    
    try:
        # Test with non-existent file
        result = subprocess.run([
            sys.executable, 
            "metaparse/extractors/doc_extractor_linebreak.py", 
            "non_existent_file.pdf"
        ], capture_output=True, text=True, timeout=10)
        
        if "No valid PDF files found!" in result.stdout:
            print("✅ File validation works correctly")
            print("📋 Expected error message displayed")
        else:
            print("⚠️ File validation behavior may need verification")
            print("Output:", result.stdout[:200])
            
    except subprocess.TimeoutExpired:
        print("⏰ File validation test timed out")
    except Exception as e:
        print(f"❌ Error testing file validation: {e}")

def main():
    """Run all tests for the main block functionality."""
    print("🚀 Testing DocExtractor Main Block Functionality")
    print("=" * 60)
    
    print("\n💡 This script tests the main block and CLI functionality")
    print("   without requiring actual PDF files.")
    
    # Check if the module file exists
    module_path = Path("metaparse/extractors/doc_extractor_linebreak.py")
    if not module_path.exists():
        print(f"\n❌ Module file not found: {module_path}")
        print("   Please run this script from the project root directory.")
        return
    
    print(f"\n✅ Module file found: {module_path}")
    
    # Run tests
    test_import_functionality()
    test_configuration_options()
    test_help_functionality()
    test_demo_mode()
    test_file_validation()
    
    print("\n🎯 Test Summary")
    print("-" * 30)
    print("✅ Import functionality: Tested")
    print("✅ Configuration options: Tested")
    print("✅ Help functionality: Tested")
    print("✅ Demo mode: Tested")
    print("✅ File validation: Tested")
    
    print("\n📚 Next Steps:")
    print("1. Test with actual PDF files:")
    print("   python metaparse/extractors/doc_extractor_linebreak.py your_document.pdf")
    print("2. Run the demo mode:")
    print("   python metaparse/extractors/doc_extractor_linebreak.py --demo")
    print("3. Check the comprehensive guide:")
    print("   See LINEBREAK_PRESERVATION_GUIDE.md")
    
    print("\n✨ Main block testing completed!")

if __name__ == "__main__":
    main()
