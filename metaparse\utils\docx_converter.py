#!/usr/bin/env python
"""
DOCX to PDF Converter Module

This module provides functions to convert DOCX files to PDF format.
It supports multiple conversion methods including LibreOffice and Microsoft Word.
Author : Anand Jadhav
Date : 2025-05-20
"""

import os
import sys
import time
import platform
import subprocess
import tempfile
import logging 
from typing import Optional, Tuple

# Set up logging
logger = logging.getLogger(__name__)


def find_libreoffice_path() -> Optional[str]:
    """
    Find the LibreOffice executable path based on the operating system.
    
    Returns:
        Path to the LibreOffice executable, or None if not found
    """
    system = platform.system()
    
    if system == "Windows":
        # Check common installation paths on Windows
        possible_paths = [
            r"C:\Program Files\LibreOffice\program\soffice.exe",
            r"C:\Program Files (x86)\LibreOffice\program\soffice.exe",
            r"C:\Program Files\LibreOffice 7\program\soffice.exe"
        ]
        for path in possible_paths:
            if os.path.exists(path):
                return path
                
        # Try to find it in the PATH
        try:
            result = subprocess.run(["where", "soffice"], capture_output=True, text=True, check=True)
            if result.stdout.strip():
                return result.stdout.strip().split("\n")[0]
        except (subprocess.SubprocessError, FileNotFoundError):
            pass
            
    elif system == "Darwin":  # macOS
        # Check common installation paths on macOS
        possible_paths = [
            "/Applications/LibreOffice.app/Contents/MacOS/soffice",
            "/Applications/LibreOffice.app/Contents/MacOS/soffice.bin"
        ]
        for path in possible_paths:
            if os.path.exists(path):
                return path
                
    else:  # Linux and other Unix-like systems
        # Try to find it in the PATH
        try:
            result = subprocess.run(["which", "soffice"], capture_output=True, text=True, check=True)
            if result.stdout.strip():
                return result.stdout.strip()
        except (subprocess.SubprocessError, FileNotFoundError):
            pass
            
    return None


def convert_with_libreoffice(input_path: str, output_dir: Optional[str] = None) -> Tuple[bool, Optional[str]]:
    """
    Convert DOCX to PDF using LibreOffice.
    
    Args:
        input_path: Path to the input DOCX file
        output_dir: Directory to save the PDF file (optional)
        
    Returns:
        Tuple of (success, pdf_path)
    """
    libreoffice_path = find_libreoffice_path()
    if not libreoffice_path:
        logger.warning("LibreOffice not found. Please install LibreOffice or specify its path.")
        return False, None
    
    # Create a temporary directory if output_dir is not specified
    if output_dir is None:
        output_dir = tempfile.mkdtemp(prefix="docx_to_pdf_")
    else:
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
    
    # Get the base filename without extension
    base_name = os.path.splitext(os.path.basename(input_path))[0]
    pdf_path = os.path.join(output_dir, f"{base_name}.pdf")
    
    try:
        # Run LibreOffice in headless mode to convert the file
        cmd = [
            libreoffice_path,
            "--headless",
            "--convert-to", "pdf",
            "--outdir", output_dir,
            input_path
        ]
        
        logger.info(f"Running conversion command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        # Check if the PDF was created
        if os.path.exists(pdf_path):
            logger.info(f"Successfully converted to PDF: {pdf_path}")
            return True, pdf_path
        else:
            logger.error(f"Conversion failed. LibreOffice output: {result.stdout}")
            logger.error(f"Error: {result.stderr}")
            return False, None
            
    except subprocess.SubprocessError as e:
        logger.error(f"Error running LibreOffice: {e}")
        return False, None


def convert_with_comtypes(input_path: str, output_dir: Optional[str] = None) -> Tuple[bool, Optional[str]]:
    """
    Convert DOCX to PDF using Microsoft Word via COM interface (Windows only).
    
    Args:
        input_path: Path to the input DOCX file
        output_dir: Directory to save the PDF file (optional)
        
    Returns:
        Tuple of (success, pdf_path)
    """
    if platform.system() != "Windows":
        logger.warning("COM interface is only available on Windows.")
        return False, None
        
    try:
        import comtypes.client
    except ImportError:
        logger.warning("comtypes package not found. Install it with: pip install comtypes")
        return False, None
    
    # Create a temporary directory if output_dir is not specified
    if output_dir is None:
        output_dir = tempfile.mkdtemp(prefix="docx_to_pdf_")
    else:
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
    
    # Get absolute paths
    input_path_abs = os.path.abspath(input_path)
    
    # Get the base filename without extension
    base_name = os.path.splitext(os.path.basename(input_path))[0]
    pdf_path = os.path.join(output_dir, f"{base_name}.pdf")
    pdf_path_abs = os.path.abspath(pdf_path)
    
    word = None
    try:
        # Create Word application object
        word = comtypes.client.CreateObject("Word.Application")
        word.Visible = False
        
        # Open the document
        doc = word.Documents.Open(input_path_abs)
        
        # Save as PDF
        doc.SaveAs(pdf_path_abs, FileFormat=17)  # 17 = PDF format
        
        # Close the document
        doc.Close()
        
        if os.path.exists(pdf_path):
            logger.info(f"Successfully converted to PDF: {pdf_path}")
            return True, pdf_path
        else:
            logger.error("Conversion failed. PDF file not created.")
            return False, None
            
    except Exception as e:
        logger.error(f"Error using Microsoft Word COM interface: {e}")
        return False, None
    finally:
        # Ensure Word is closed
        if word:
            try:
                word.Quit()
            except:
                pass


def convert_docx_to_pdf(input_path: str, output_dir: Optional[str] = None) -> Optional[str]:
    """
    Convert DOCX to PDF using available methods.
    
    Args:
        input_path: Path to the input DOCX file
        output_dir: Directory to save the PDF file (optional)
        
    Returns:
        Path to the converted PDF file, or None if conversion failed
    """
    logger.info(f"Converting DOCX to PDF: {input_path}")
    
    # Validate input file
    if not os.path.exists(input_path):
        logger.error(f"Input file not found: {input_path}")
        return None
        
    # Check file extension
    if not input_path.lower().endswith('.docx'):
        logger.warning(f"Input file does not have a .docx extension: {input_path}")
    
    # Create a temporary directory if output_dir is not specified
    if output_dir is None:
        output_dir = tempfile.mkdtemp(prefix="docx_to_pdf_")
        logger.info(f"Created temporary directory: {output_dir}")
    
    # Try conversion methods in order
    methods = [
        ("LibreOffice", convert_with_libreoffice),
        ("Microsoft Word", convert_with_comtypes)
    ]
    
    for method_name, method_func in methods:
        logger.info(f"Trying conversion with {method_name}...")
        success, pdf_path = method_func(input_path, output_dir)
        if success and pdf_path:
            return pdf_path
    
    logger.error("All conversion methods failed.")
    return None


if __name__ == "__main__":
    # Set up logging for command-line usage
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    # Simple command-line interface
    if len(sys.argv) < 2:
        print("Usage: python docx_converter.py input.docx [output_dir]")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else None
    
    pdf_path = convert_docx_to_pdf(input_file, output_dir)
    if pdf_path:
        print(f"Successfully converted to PDF: {pdf_path}")
        sys.exit(0)
    else:
        print("Conversion failed.")
        sys.exit(1)
