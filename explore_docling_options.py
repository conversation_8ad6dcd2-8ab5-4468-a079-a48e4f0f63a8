#!/usr/bin/env python3
"""
Explore different Docling export options to find linebreaks.

This script tests various Docling export methods and configurations
to see which ones preserve or provide linebreaks.

Author: <PERSON>adhav
Date: 2025-01-27
"""

import json
from pathlib import Path
from docling.document_converter import DocumentConverter


def explore_docling_exports(pdf_path: str):
    """
    Explore different Docling export options.
    
    Args:
        pdf_path: Path to PDF file
    """
    print("Exploring Docling Export Options")
    print("=" * 35)
    
    # Convert PDF using Docling
    converter = DocumentConverter()
    result = converter.convert(pdf_path)
    
    # Method 1: Standard dictionary export
    print("\n1. Standard export_to_dict():")
    try:
        dict_data = result.document.export_to_dict()
        analyze_export_method(dict_data, "dict")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Method 2: Markdown export
    print("\n2. Markdown export:")
    try:
        markdown_text = result.document.export_to_markdown()
        print(f"   Length: {len(markdown_text)} characters")
        print(f"   Linebreaks: {markdown_text.count(chr(10))}")
        print(f"   Sample: '{markdown_text[:100]}...'")
        
        if markdown_text.count('\n') > 0:
            print(f"   ✅ Markdown export HAS linebreaks!")
            
            # Save markdown for inspection
            with open("docling_markdown_output.md", 'w', encoding='utf-8') as f:
                f.write(markdown_text)
            print(f"   Saved to: docling_markdown_output.md")
        else:
            print(f"   ❌ Markdown export has no linebreaks")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Method 3: Check if there are other export methods
    print("\n3. Available document methods:")
    doc_methods = [method for method in dir(result.document) if not method.startswith('_')]
    export_methods = [method for method in doc_methods if 'export' in method.lower()]
    print(f"   Export methods: {export_methods}")
    
    # Method 4: Check document structure
    print("\n4. Document structure analysis:")
    try:
        # Check if document has pages with different structure
        if hasattr(result.document, 'pages'):
            print(f"   Document has pages attribute")
            pages = result.document.pages
            if pages:
                first_page = pages[0] if isinstance(pages, list) else list(pages.values())[0]
                print(f"   First page type: {type(first_page)}")
                print(f"   First page attributes: {[attr for attr in dir(first_page) if not attr.startswith('_')]}")
        
        # Check if document has elements
        if hasattr(result.document, 'elements'):
            print(f"   Document has elements attribute")
            elements = result.document.elements
            print(f"   Elements count: {len(elements) if elements else 0}")
            
        # Check if document has texts
        if hasattr(result.document, 'texts'):
            print(f"   Document has texts attribute")
            
    except Exception as e:
        print(f"   Error exploring structure: {e}")
    
    # Method 5: Try different converter configurations
    print("\n5. Testing different converter configurations:")
    try:
        # Test with different settings if available
        print("   Testing default converter settings...")
        
        # Check converter attributes
        converter_attrs = [attr for attr in dir(converter) if not attr.startswith('_')]
        print(f"   Converter attributes: {converter_attrs}")
        
    except Exception as e:
        print(f"   Error: {e}")


def analyze_export_method(data, method_name):
    """Analyze an export method's output for linebreaks."""
    
    print(f"   {method_name.upper()} Analysis:")
    
    # Check pages
    if 'pages' in data and data['pages']:
        first_page = list(data['pages'].values())[0]
        if 'text' in first_page:
            text = first_page['text']
            linebreaks = text.count('\n')
            print(f"     Page text linebreaks: {linebreaks}")
            if linebreaks > 0:
                print(f"     ✅ {method_name} page text HAS linebreaks!")
            else:
                print(f"     ❌ {method_name} page text has NO linebreaks")
    
    # Check text elements
    if 'texts' in data and data['texts']:
        element_linebreaks = sum(1 for item in data['texts'] 
                               if 'text' in item and '\n' in item['text'])
        print(f"     Text elements with linebreaks: {element_linebreaks}/{len(data['texts'])}")
        
        if element_linebreaks > 0:
            print(f"     ✅ {method_name} text elements HAVE linebreaks!")
            # Show example
            for item in data['texts'][:3]:
                if 'text' in item and '\n' in item['text']:
                    sample = item['text'].replace('\n', '\\n')
                    print(f"       Example: '{sample[:50]}...'")
                    break
        else:
            print(f"     ❌ {method_name} text elements have NO linebreaks")


def main():
    """Main function."""
    
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    
    if not Path(pdf_file).exists():
        print("PDF file not found!")
        return
    
    explore_docling_exports(pdf_file)
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    print("- Check the output above to see which export method provides linebreaks")
    print("- If markdown export has linebreaks, we can use that")
    print("- If text elements have linebreaks, we can reconstruct from those")
    print("- If no method has linebreaks, Docling might not preserve them")


if __name__ == "__main__":
    main()
