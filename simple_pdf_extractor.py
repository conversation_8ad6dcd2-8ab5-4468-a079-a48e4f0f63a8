#!/usr/bin/env python3
"""
Simple PDF text extractor with linebreaks preservation.

This module uses Docling to extract text from PDF files and ensures
that the JSON output contains linebreaks (\n) in the text fields.

Author: Anand Jadhav
Date: 2025-05-27
"""

import json
from pathlib import Path
from typing import Dict, Any
from docling.document_converter import DocumentConverter


class SimplePDFExtractor:
    """Simple PDF extractor that preserves linebreaks in text."""

    def __init__(self):
        """Initialize the PDF extractor."""
        self.converter = DocumentConverter()

    def extract_with_linebreaks(self, pdf_path: str) -> Dict[str, Any]:
        """
        Extract text from PDF with linebreaks preserved.

        Args:
            pdf_path: Path to the PDF file

        Returns:
            Dictionary with extracted text containing linebreaks
        """
        # Convert PDF using Docling
        result = self.converter.convert(pdf_path)
        data = result.document.export_to_dict()
        # print(f"{data =}")
        # Add linebreaks to text content
        self._add_linebreaks_to_text(data)

        return data

    def _add_linebreaks_to_text(self, data: Dict[str, Any]) -> None:
        """
        Add linebreaks to text content in the data dictionary.

        Args:
            data: Document data dictionary to modify
        """
        # Process page text - preserve original linebreaks from Docling
        if 'pages' in data:
            for page_data in data['pages'].values():
                if 'text' in page_data:
                    page_data['text'] = self._preserve_original_linebreaks(page_data['text'])

        # Process individual text items - preserve original linebreaks from Docling
        if 'texts' in data:
            for text_item in data['texts']:
                if 'text' in text_item:
                    text_item['text'] = self._preserve_original_linebreaks(text_item['text'])

        # Process table cell text - preserve original linebreaks from Docling
        if 'tables' in data:
            for table in data['tables']:
                if 'cells' in table:
                    for cell in table['cells']:
                        if 'text' in cell:
                            cell['text'] = self._preserve_original_linebreaks(cell['text'])

    def _preserve_original_linebreaks(self, text: str) -> str:
        """
        Preserve original linebreaks that already exist in the text.

        Args:
            text: Original text string from Docling

        Returns:
            Text with original linebreaks preserved
        """
        if not text or not isinstance(text, str):
            return text

        # Simply return the text as-is - Docling should provide the linebreaks
        # If Docling has linebreaks, they will be preserved
        # If not, we don't artificially create them
        return text


def main():
    """Main function to extract PDF with linebreaks."""

    # Configuration
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    output_folder = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder"

    # Check if PDF exists
    if not Path(pdf_file).exists():
        print(f"Error: PDF file not found: {pdf_file}")
        return

    # Create output directory
    Path(output_folder).mkdir(parents=True, exist_ok=True)

    # Extract text with linebreaks
    extractor = SimplePDFExtractor()
    result = extractor.extract_with_linebreaks(pdf_file)

    # Save result
    output_file = Path(output_folder) / f"{Path(pdf_file).stem}_simple_extracted.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)

    # Show results
    print(f"Extraction completed: {output_file}")

    # Quick analysis
    if 'pages' in result and result['pages']:
        first_page = list(result['pages'].values())[0]
        if 'text' in first_page:
            text = first_page['text']
            linebreak_count = text.count('\n')
            print(f"First page linebreaks: {linebreak_count}")
            print(f"Sample text: {text[:100]}...")


if __name__ == "__main__":
    main()
