#!/usr/bin/env python3
"""
Simple PDF text extractor with linebreaks preservation.

This module uses Docling to extract text from PDF files and ensures
that the JSON output contains linebreaks (\n) in the text fields.

Author: Anand Jadhav
Date: 2025-05-27
"""

import json
import re
from pathlib import Path
from typing import Dict, Any
from docling.document_converter import DocumentConverter


class SimplePDFExtractor:
    """Simple PDF extractor that preserves linebreaks in text."""

    def __init__(self):
        """Initialize the PDF extractor."""
        self.converter = DocumentConverter()

    def extract_with_linebreaks(self, pdf_path: str) -> Dict[str, Any]:
        """
        Extract text from PDF with linebreaks preserved using various Docling methods.

        Args:
            pdf_path: Path to the PDF file

        Returns:
            Dictionary with extracted text containing linebreaks
        """
        # Convert PDF using Docling
        result = self.converter.convert(pdf_path)
        document = result.document

        # Try different Docling export methods to find the best linebreak preservation
        data = self._try_different_export_methods(document)

        return data

    def _try_different_export_methods(self, document) -> Dict[str, Any]:
        """
        Try different Docling export methods to find the best linebreak preservation.

        Args:
            document: Docling document object

        Returns:
            Dictionary with the best linebreak preservation found
        """
        print("🔍 Testing different Docling export methods for linebreak preservation...")

        # Method 1: Standard export_to_dict
        print("\n1. Testing export_to_dict():")
        dict_data = document.export_to_dict()
        dict_linebreaks = self._count_linebreaks_in_data(dict_data)
        print(f"   Linebreaks found: {dict_linebreaks}")

        # Method 2: export_to_text
        print("\n2. Testing export_to_text():")
        try:
            text_data = document.export_to_text()
            text_linebreaks = text_data.count('\n')
            print(f"   Text length: {len(text_data)} chars")
            print(f"   Linebreaks found: {text_linebreaks}")

            # Show sample with visible linebreaks
            sample = text_data[:200]
            visible_sample = sample.replace('\n', '\\n\n      ')
            print(f"   Sample with visible linebreaks:\n      '{visible_sample}...'")

            if text_linebreaks > 0:
                print("   ✅ export_to_text() HAS linebreaks!")
                # Clean the text while preserving linebreaks
                cleaned_text = self._clean_export_text(text_data)
                cleaned_linebreaks = cleaned_text.count('\n')
                print(f"   After cleaning: {cleaned_linebreaks} linebreaks")
                print(f"   Clean sample: '{cleaned_text[:150]}...'")

                # Use this cleaned text in the dictionary structure
                dict_data = self._update_dict_with_text(dict_data, cleaned_text, 'export_to_text_cleaned')
                return dict_data
            else:
                print("   ❌ export_to_text() has NO linebreaks")
        except Exception as e:
            print(f"   ❌ export_to_text() failed: {e}")

        # Method 3: export_to_markdown
        print("\n3. Testing export_to_markdown():")
        try:
            markdown_data = document.export_to_markdown()
            markdown_linebreaks = markdown_data.count('\n')
            print(f"   Markdown length: {len(markdown_data)} chars")
            print(f"   Linebreaks found: {markdown_linebreaks}")

            # Show sample with visible linebreaks
            sample = markdown_data[:200]
            visible_sample = sample.replace('\n', '\\n\n      ')
            print(f"   Sample with visible linebreaks:\n      '{visible_sample}...'")

            if markdown_linebreaks > 0:
                print("   ✅ export_to_markdown() HAS linebreaks!")
                # Clean markdown and use in dictionary
                clean_text = self._clean_markdown_text(markdown_data)
                clean_linebreaks = clean_text.count('\n')
                print(f"   After cleaning: {clean_linebreaks} linebreaks")
                print(f"   Clean sample: '{clean_text[:100]}...'")

                dict_data = self._update_dict_with_text(dict_data, clean_text, 'export_to_markdown')
                return dict_data
            else:
                print("   ❌ export_to_markdown() has NO linebreaks")
        except Exception as e:
            print(f"   ❌ export_to_markdown() failed: {e}")

        # Method 4: Direct access to document.texts (first page only)
        print("\n4. Testing direct document.texts access (first page only):")
        try:
            if hasattr(document, 'texts') and document.texts:
                first_page_texts = []
                texts_linebreaks = 0

                # Get only first page text items
                for text_item in document.texts:
                    if hasattr(text_item, 'prov') and text_item.prov:
                        page_no = text_item.prov[0].page_no if text_item.prov else None
                        if page_no == 1:  # First page only
                            first_page_texts.append(text_item)
                            if hasattr(text_item, 'text') and text_item.text:
                                item_linebreaks = text_item.text.count('\n')
                                texts_linebreaks += item_linebreaks
                                if item_linebreaks > 0:
                                    print(f"   Text item with linebreaks: '{text_item.text[:50]}...' ({item_linebreaks} linebreaks)")

                print(f"   First page text items: {len(first_page_texts)}")
                print(f"   Total linebreaks in first page texts: {texts_linebreaks}")

                if texts_linebreaks > 0:
                    print("   ✅ document.texts (first page) HAS linebreaks!")
                    # Reconstruct from first page texts with linebreaks
                    reconstructed_text = self._reconstruct_from_texts(first_page_texts)
                    dict_data = self._update_dict_with_text(dict_data, reconstructed_text, 'document_texts_page1')
                    return dict_data
                else:
                    print("   ❌ document.texts (first page) has NO linebreaks")
        except Exception as e:
            print(f"   ❌ document.texts access failed: {e}")

        # Method 5: Direct access to document.pages (first page only)
        print("\n5. Testing direct document.pages access (first page only):")
        try:
            if hasattr(document, 'pages') and document.pages:
                first_page = document.pages[0] if document.pages else None
                if first_page and hasattr(first_page, 'text') and first_page.text:
                    first_page_linebreaks = first_page.text.count('\n')
                    print(f"   First page text length: {len(first_page.text)} chars")
                    print(f"   First page linebreaks: {first_page_linebreaks}")

                    # Show sample with visible linebreaks
                    sample = first_page.text[:200]
                    visible_sample = sample.replace('\n', '\\n\n      ')
                    print(f"   Sample with visible linebreaks:\n      '{visible_sample}...'")

                    if first_page_linebreaks > 0:
                        print("   ✅ document.pages[0] HAS linebreaks!")
                        dict_data = self._update_dict_with_text(dict_data, first_page.text, 'document_pages_first')
                        return dict_data
                    else:
                        print("   ❌ document.pages[0] has NO linebreaks")
                else:
                    print("   ❌ No text found in first page")
        except Exception as e:
            print(f"   ❌ document.pages access failed: {e}")

        # Method 6: Raw text inspection
        print("\n6. Raw text inspection from export_to_dict():")
        try:
            if 'pages' in dict_data and dict_data['pages']:
                first_page_key = list(dict_data['pages'].keys())[0]
                raw_text = dict_data['pages'][first_page_key].get('text', '')
                print(f"   Raw dict text length: {len(raw_text)} chars")
                print(f"   Raw dict linebreaks: {raw_text.count(chr(10))}")
                print(f"   Raw dict sample: '{raw_text[:100]}...'")

                # Check for different line ending types
                print(f"   Carriage returns (\\r): {raw_text.count(chr(13))}")
                print(f"   Tab characters (\\t): {raw_text.count(chr(9))}")
                print(f"   Form feeds (\\f): {raw_text.count(chr(12))}")
        except Exception as e:
            print(f"   ❌ Raw text inspection failed: {e}")

        print("\n❌ No export method found linebreaks - returning standard dict")
        print("💡 This suggests that Docling may not preserve linebreaks for this PDF type")
        return dict_data

    def _count_linebreaks_in_data(self, data: Dict[str, Any]) -> int:
        """Count total linebreaks in dictionary data."""
        total = 0

        if 'pages' in data:
            for page in data['pages'].values():
                if 'text' in page:
                    total += page['text'].count('\n')

        if 'texts' in data:
            for item in data['texts']:
                if 'text' in item:
                    total += item['text'].count('\n')

        return total

    def _update_dict_with_text(self, data: Dict[str, Any], text: str, source: str) -> Dict[str, Any]:
        """Update dictionary with text that has linebreaks."""
        if 'pages' in data and data['pages']:
            first_page_key = list(data['pages'].keys())[0]
            data['pages'][first_page_key]['text'] = text
            data['pages'][first_page_key]['linebreaks_source'] = source
        return data

    def _reconstruct_from_texts(self, texts) -> str:
        """Reconstruct text from document.texts with linebreaks."""
        text_parts = []
        for text_item in texts:
            if hasattr(text_item, 'text') and text_item.text:
                text_parts.append(text_item.text)
        return '\n'.join(text_parts)

    def _clean_export_text(self, text: str) -> str:
        """
        Clean export_to_text() output while preserving linebreaks.

        Args:
            text: Raw text from export_to_text()

        Returns:
            Cleaned text with linebreaks preserved
        """
        if not text:
            return ""

        # Clean the text while preserving linebreaks
        cleaned_text = text

        # Remove markdown headers (## ### etc.) but keep the text
        cleaned_text = re.sub(r'^#{1,6}\s+', '', cleaned_text, flags=re.MULTILINE)

        # Clean up multiple spaces but preserve linebreaks
        cleaned_text = re.sub(r' {2,}', ' ', cleaned_text)  # Multiple spaces to single space

        # Clean up multiple consecutive linebreaks (keep max 2)
        cleaned_text = re.sub(r'\n{3,}', '\n\n', cleaned_text)

        # Remove leading/trailing whitespace from each line but keep linebreaks
        lines = cleaned_text.split('\n')
        cleaned_lines = [line.strip() for line in lines]
        cleaned_text = '\n'.join(cleaned_lines)

        return cleaned_text.strip()

    def _clean_markdown_text(self, markdown_text: str) -> str:
        """Clean markdown text while preserving linebreaks."""
        if not markdown_text:
            return ""

        text = markdown_text

        # # Remove markdown formatting but keep ALL linebreaks
        # text = re.sub(r'^#{1,6}\s+', '', text, flags=re.MULTILINE)
        # text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)
        # text = re.sub(r'\*([^*]+)\*', r'\1', text)
        # text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)
        # text = re.sub(r'```[^`]*```', '', text, flags=re.DOTALL)
        # text = re.sub(r'`([^`]+)`', r'\1', text)
        # text = re.sub(r' +', ' ', text)

        return text.strip()


def main():
    """Main function to extract PDF with linebreaks."""

    # Configuration
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    output_folder = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder"

    # Check if PDF exists
    if not Path(pdf_file).exists():
        print(f"Error: PDF file not found: {pdf_file}")
        return

    # Create output directory
    Path(output_folder).mkdir(parents=True, exist_ok=True)

    # Extract text with linebreaks
    extractor = SimplePDFExtractor()
    result = extractor.extract_with_linebreaks(pdf_file)

    # Save result
    output_file = Path(output_folder) / f"{Path(pdf_file).stem}_simple_extracted.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)

    # Show results
    print(f"Extraction completed: {output_file}")

    # Detailed analysis of linebreaks
    if 'pages' in result and result['pages']:
        first_page = list(result['pages'].values())[0]
        if 'text' in first_page:
            text = first_page['text']
            linebreak_count = text.count('\n')

            print(f"\nLinebreak Analysis:")
            print(f"  Text length: {len(text)} characters")
            print(f"  Linebreaks found: {linebreak_count}")
            print(f"  Source: {first_page.get('linebreaks_source', 'unknown')}")

            if linebreak_count > 0:
                print(f"  ✅ SUCCESS: Linebreaks preserved from {first_page.get('linebreaks_source', 'unknown')}!")

                # Show sample with visible linebreaks
                sample = text[:300]
                print(f"\nSample text (first 300 chars):")
                print(f"'{sample}...'")

                # Show first few lines
                lines = text.split('\n')
                print(f"\nFirst 5 lines:")
                for i, line in enumerate(lines[:5], 1):
                    if line.strip():  # Only show non-empty lines
                        print(f"  Line {i}: '{line.strip()}'")
                    else:
                        print(f"  Line {i}: (empty line)")

                # Check if this matches the expected format
                print(f"\n🎯 Format Check:")
                if "prostate" in text.lower() and "cancer" in text.lower():
                    print(f"  ✅ Contains expected 'prostate cancer' text")

                    # Look for the specific pattern from your example
                    if "prostate\ncancer" in text.lower():
                        print(f"  ✅ Found 'prostate\\ncancer' pattern - matches your requirement!")
                    elif "prostate cancer" in text.lower():
                        print(f"  ⚠️ Found 'prostate cancer' but not with linebreak between them")
                else:
                    print(f"  ⚠️ Expected text pattern not found")
            else:
                print(f"  ❌ No linebreaks found")
                print(f"Sample text: {text[:100]}...")
        else:
            print("No text found in first page")
    else:
        print("No pages found in result")


if __name__ == "__main__":
    main()
