#!/usr/bin/env python3
"""
Simple PDF text extractor with linebreaks preservation.

This module uses Docling to extract text from PDF files and ensures
that the JSON output contains linebreaks (\n) in the text fields.

Author: Anand Jadhav
Date: 2025-05-27
"""

import json
import re
from pathlib import Path
from typing import Dict, Any
from docling.document_converter import DocumentConverter


class SimplePDFExtractor:
    """Simple PDF extractor that preserves linebreaks in text."""

    def __init__(self):
        """Initialize the PDF extractor."""
        self.converter = DocumentConverter()

    def extract_with_linebreaks(self, pdf_path: str) -> Dict[str, Any]:
        """
        Extract text from PDF with linebreaks preserved using markdown.

        Args:
            pdf_path: Path to the PDF file

        Returns:
            Dictionary with extracted text containing linebreaks from markdown
        """
        # Convert PDF using Docling
        result = self.converter.convert(pdf_path)

        # Get both dictionary structure and markdown text (which has linebreaks)
        data = result.document.export_to_dict()
        markdown_text = result.document.export_to_markdown()

        # Update the dictionary with markdown text that preserves linebreaks
        self._update_with_markdown_linebreaks(data, markdown_text)

        return data

    def _update_with_markdown_linebreaks(self, data: Dict[str, Any], markdown_text: str) -> None:
        """
        Update the dictionary with markdown text that preserves linebreaks.

        Args:
            data: Document data dictionary to modify
            markdown_text: Markdown text with linebreaks from Docling
        """
        # Clean markdown text to plain text while preserving linebreaks
        clean_text = self._clean_markdown_text(markdown_text)

        # Update page text with the cleaned markdown text that has linebreaks
        if 'pages' in data and data['pages']:
            # Put the markdown text (with linebreaks) in the first page
            first_page_key = list(data['pages'].keys())[0]
            data['pages'][first_page_key]['text'] = clean_text

            # Mark that we used markdown for linebreaks
            data['pages'][first_page_key]['linebreaks_source'] = 'markdown'

    def _clean_markdown_text(self, markdown_text: str) -> str:
        """
        Clean markdown text to plain text while preserving all linebreaks.

        Args:
            markdown_text: Raw markdown text from Docling

        Returns:
            Clean text with all linebreaks preserved
        """
        if not markdown_text:
            return ""

        text = markdown_text

        # Remove markdown formatting but keep ALL linebreaks
        text = re.sub(r'^#{1,6}\s+', '', text, flags=re.MULTILINE)  # Remove headers
        text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)  # Remove bold
        text = re.sub(r'\*([^*]+)\*', r'\1', text)  # Remove italic
        text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)  # Remove links
        text = re.sub(r'```[^`]*```', '', text, flags=re.DOTALL)  # Remove code blocks
        text = re.sub(r'`([^`]+)`', r'\1', text)  # Remove inline code

        # Clean up multiple spaces but preserve ALL linebreaks
        text = re.sub(r' +', ' ', text)  # Multiple spaces to single space

        # DO NOT remove multiple linebreaks - preserve exactly as in markdown
        # text = re.sub(r'\n{3,}', '\n\n', text)  # REMOVED - keep all linebreaks

        return text.strip()


def main():
    """Main function to extract PDF with linebreaks."""

    # Configuration
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    output_folder = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder"

    # Check if PDF exists
    if not Path(pdf_file).exists():
        print(f"Error: PDF file not found: {pdf_file}")
        return

    # Create output directory
    Path(output_folder).mkdir(parents=True, exist_ok=True)

    # Extract text with linebreaks
    extractor = SimplePDFExtractor()
    result = extractor.extract_with_linebreaks(pdf_file)

    # Save result
    output_file = Path(output_folder) / f"{Path(pdf_file).stem}_simple_extracted.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)

    # Show results
    print(f"Extraction completed: {output_file}")

    # Detailed analysis of linebreaks
    if 'pages' in result and result['pages']:
        first_page = list(result['pages'].values())[0]
        if 'text' in first_page:
            text = first_page['text']
            linebreak_count = text.count('\n')

            print(f"\nLinebreak Analysis:")
            print(f"  Text length: {len(text)} characters")
            print(f"  Linebreaks found: {linebreak_count}")
            print(f"  Source: {first_page.get('linebreaks_source', 'unknown')}")

            if linebreak_count > 0:
                print(f"  ✅ SUCCESS: Linebreaks preserved from markdown!")

                # Show sample with visible linebreaks
                sample = text[:200]
                print(f"\nSample text (first 200 chars):")
                print(f"'{sample}...'")

                # Show first few lines
                lines = text.split('\n')
                print(f"\nFirst 3 lines:")
                for i, line in enumerate(lines[:3], 1):
                    print(f"  Line {i}: '{line.strip()}'")
            else:
                print(f"  ❌ No linebreaks found")
                print(f"Sample text: {text[:100]}...")
        else:
            print("No text found in first page")
    else:
        print("No pages found in result")


if __name__ == "__main__":
    main()
