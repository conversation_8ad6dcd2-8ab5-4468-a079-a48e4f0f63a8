{"schema_name": "MetaParseDocument", "version": "1.3.0", "name": "34605781K.article.001", "origin": {"mimetype": "application/pdf", "binary_hash": 12846914625499464286, "filename": "34605781K.article.001.pdf"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/texts/0"}, {"$ref": "#/texts/1"}, {"$ref": "#/texts/2"}, {"$ref": "#/texts/3"}, {"$ref": "#/texts/4"}, {"$ref": "#/texts/5"}, {"$ref": "#/texts/6"}, {"$ref": "#/groups/0"}, {"$ref": "#/texts/7"}, {"$ref": "#/texts/8"}, {"$ref": "#/texts/9"}, {"$ref": "#/texts/10"}, {"$ref": "#/pictures/0"}, {"$ref": "#/texts/63"}, {"$ref": "#/texts/64"}, {"$ref": "#/texts/65"}, {"$ref": "#/texts/66"}, {"$ref": "#/texts/67"}, {"$ref": "#/texts/68"}, {"$ref": "#/tables/0"}, {"$ref": "#/groups/1"}, {"$ref": "#/pictures/1"}, {"$ref": "#/texts/99"}, {"$ref": "#/texts/100"}, {"$ref": "#/texts/101"}, {"$ref": "#/texts/102"}, {"$ref": "#/texts/103"}, {"$ref": "#/pictures/2"}, {"$ref": "#/texts/127"}, {"$ref": "#/texts/128"}, {"$ref": "#/texts/129"}, {"$ref": "#/pictures/3"}, {"$ref": "#/tables/1"}, {"$ref": "#/texts/140"}, {"$ref": "#/texts/141"}, {"$ref": "#/texts/142"}, {"$ref": "#/texts/143"}, {"$ref": "#/pictures/4"}, {"$ref": "#/pictures/5"}, {"$ref": "#/texts/157"}, {"$ref": "#/texts/158"}, {"$ref": "#/texts/159"}, {"$ref": "#/texts/160"}, {"$ref": "#/texts/161"}, {"$ref": "#/texts/162"}, {"$ref": "#/texts/163"}, {"$ref": "#/texts/164"}, {"$ref": "#/texts/165"}, {"$ref": "#/texts/166"}, {"$ref": "#/texts/167"}, {"$ref": "#/texts/168"}, {"$ref": "#/texts/169"}, {"$ref": "#/texts/170"}, {"$ref": "#/texts/171"}, {"$ref": "#/texts/172"}, {"$ref": "#/texts/173"}, {"$ref": "#/texts/174"}, {"$ref": "#/texts/175"}, {"$ref": "#/texts/176"}, {"$ref": "#/texts/177"}, {"$ref": "#/texts/178"}, {"$ref": "#/texts/179"}, {"$ref": "#/texts/180"}, {"$ref": "#/groups/2"}, {"$ref": "#/texts/190"}, {"$ref": "#/texts/191"}, {"$ref": "#/groups/3"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "name": "group", "label": "key_value_area"}, {"self_ref": "#/groups/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/70"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/2", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/181"}, {"$ref": "#/texts/182"}, {"$ref": "#/texts/183"}, {"$ref": "#/texts/184"}, {"$ref": "#/texts/185"}, {"$ref": "#/texts/186"}, {"$ref": "#/texts/187"}, {"$ref": "#/texts/188"}, {"$ref": "#/texts/189"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/3", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/192"}, {"$ref": "#/texts/193"}, {"$ref": "#/texts/194"}, {"$ref": "#/texts/195"}, {"$ref": "#/texts/196"}, {"$ref": "#/texts/197"}, {"$ref": "#/texts/198"}, {"$ref": "#/texts/199"}, {"$ref": "#/texts/200"}, {"$ref": "#/texts/201"}], "content_layer": "body", "name": "list", "label": "list"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 1, "bbox": {"l": 26.0, "t": 751.0, "r": 51.333333333333336, "b": 740.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "Note", "text": "Note"}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 1, "bbox": {"l": 222.66666666666666, "t": 768.3333333333334, "r": 357.3333333333333, "b": 758.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 36]}], "orig": "Chem. Pharm. Bull 63, 305-310 (2015)", "text": "Chem. Pharm. Bull 63, 305-310 (2015)"}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 1, "bbox": {"l": 516.6666666666666, "t": 768.3333333333334, "r": 532.0, "b": 759.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "305", "text": "305"}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 24.0, "t": 713.6666666666666, "r": 353.3333333333333, "b": 692.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 50]}], "orig": "Limonoids from the Stem Bark of Khaya senegalensis", "text": "Limonoids from the Stem Bark of Khaya senegalensis", "level": 1}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 25.666666666666668, "t": 685.3333333333334, "r": 418.3333333333333, "b": 655.3751148452747, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 59]}], "orig": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>,b and *b <PERSON><PERSON>, Kong", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>,b and *b <PERSON><PERSON>, Kong"}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 25.333333333333332, "t": 653.3333333333334, "r": 469.3333333333333, "b": 608.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 340]}], "orig": "Testing & Analysis Center; Nanjing Normal University: Nanjing 210023, People's Republic of China: and * State Key Laboratory of Natural Medicines, Department of Natural Medicinal Chemistry; China Pharmaceutical University; 24 <PERSON><PERSON>,  Nanjing 210009, People's Republic of China. Received November 10, 2014; accepted January 24, 2015 Tong", "text": "Testing & Analysis Center; Nanjing Normal University: Nanjing 210023, People's Republic of China: and * State Key Laboratory of Natural Medicines, Department of Natural Medicinal Chemistry; China Pharmaceutical University; 24 <PERSON><PERSON>,  Nanjing 210009, People's Republic of China. Received November 10, 2014; accepted January 24, 2015 Tong"}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 60.666666666666664, "t": 599.9615239476408, "r": 496.3333333333333, "b": 513.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 805]}], "orig": "Six new limonoids with modified furan khaysenelide A-F (1-6) together with six known limonoids (7-12) were isolated from the stem bark of Khaya senegalensis. The hasic skeletons of these new limonoids belong to mexicanolide (1, 2) and rearrangement phragmalin (3-6), which were elucidated on the basis of spectroscopic methods including high resolution-electrospray ionization (HR-ESI)-MS, one and two dimensional (ID and 2D)-NMR and confirmed by single-crystal X-ray crystallography using CuKa radiation of 1 and 3. Their absolute configurations were determined by the X-ray crystallography data and comparison of their clectronic circular dichroism spectra. The inhibitory effect on nitric oxide (NO) production in lipopolysaccaride-activated RAW264.7 macrophages of new compounds was also tested. ring;", "text": "Six new limonoids with modified furan khaysenelide A-F (1-6) together with six known limonoids (7-12) were isolated from the stem bark of Khaya senegalensis. The hasic skeletons of these new limonoids belong to mexicanolide (1, 2) and rearrangement phragmalin (3-6), which were elucidated on the basis of spectroscopic methods including high resolution-electrospray ionization (HR-ESI)-MS, one and two dimensional (ID and 2D)-NMR and confirmed by single-crystal X-ray crystallography using CuKa radiation of 1 and 3. Their absolute configurations were determined by the X-ray crystallography data and comparison of their clectronic circular dichroism spectra. The inhibitory effect on nitric oxide (NO) production in lipopolysaccaride-activated RAW264.7 macrophages of new compounds was also tested. ring;"}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 79.33333333333333, "t": 510.6666666666667, "r": 356.6666666666667, "b": 497.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 67]}], "orig": "Key words Khaya senegalensis; Meliaceae; rearranged limonoid; X-ray", "text": "Key words Khaya senegalensis; Meliaceae; rearranged limonoid; X-ray"}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 24.0, "t": 485.3333333333333, "r": 275.0, "b": 253.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1145]}, {"page_no": 1, "bbox": {"l": 24.0, "t": 485.3333333333333, "r": 275.0, "b": 253.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [1146, 1504]}], "orig": "Meliaceae plants are attracting considerable interest because of the abundance and structural diversity of the limonoids in its plant members.',2) Khaya senegalensis (DESR:) A Juss. (Meliaceae), belonging to the genus Khaya; was used traditionally in Africa for treatment of malaria and fever the crude ex tracts of whose bark showed the antifungal, anti-inflammatory and hypertensive effects.'-7 Previous chemical investigations on it  had  afforded series of B D-seco limonoids such as mexicanolide, phragmalin, and rearrangement novel   structure and significant   biological   activities; six new limonoids;   khaysenelide A-F (1-6) (Fig. 1), along with six known limonoids were isolated from the stem bark of K. senegalensis. The basic skeleton of these limonoids were belong to mexicanolide (1, 2) and rearranged phragmalin (3-12), which were elucidated on the basis of spectroscopic methods including high resolution-electrospray ionization (HR-ESI)-MS, one and two dimensional (ID and 2D)-NMR and confirmed by the single-crystal X-ray crystallography CuKa radiation of 1 and 3, which also was used to determine their absolute rings using configuration. The mechanism of modified furan rings in 1 6 was proposed, which also explained why the NMR signal of modified furan of compounds 1 3 4 6 shrunk severely. The inhibitory effect on nitric oxide (NO) production in lipopolysaccaride-activated RAW264.7 macrophages of new compounds and structural elucidation of these compounds are presented. ring", "text": "Meliaceae plants are attracting considerable interest because of the abundance and structural diversity of the limonoids in its plant members.',2) Khaya senegalensis (DESR:) A Juss. (Meliaceae), belonging to the genus Khaya; was used traditionally in Africa for treatment of malaria and fever the crude ex tracts of whose bark showed the antifungal, anti-inflammatory and hypertensive effects.'-7 Previous chemical investigations on it  had  afforded series of B D-seco limonoids such as mexicanolide, phragmalin, and rearrangement novel   structure and significant   biological   activities; six new limonoids;   khaysenelide A-F (1-6) (Fig. 1), along with six known limonoids were isolated from the stem bark of K. senegalensis. The basic skeleton of these limonoids were belong to mexicanolide (1, 2) and rearranged phragmalin (3-12), which were elucidated on the basis of spectroscopic methods including high resolution-electrospray ionization (HR-ESI)-MS, one and two dimensional (ID and 2D)-NMR and confirmed by the single-crystal X-ray crystallography CuKa radiation of 1 and 3, which also was used to determine their absolute rings using configuration. The mechanism of modified furan rings in 1 6 was proposed, which also explained why the NMR signal of modified furan of compounds 1 3 4 6 shrunk severely. The inhibitory effect on nitric oxide (NO) production in lipopolysaccaride-activated RAW264.7 macrophages of new compounds and structural elucidation of these compounds are presented. ring"}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 284.0, "t": 390.3333333333333, "r": 385.3333333333333, "b": 379.6666666666667, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 22]}], "orig": "Results and Discussion", "text": "Results and Discussion", "level": 1}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 283.0, "t": 380.3333333333333, "r": 532.0, "b": 253.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 378]}], "orig": "Khaysenelide A (1) was obtained as colorless   crystal methanol water (MeOH H,O), and had the molecular formula [M+Na] The 'H-NMR 1.16. 1.07, 0.90, each 3H, s) a methoxyl heteronuclear multiple   bond   connectivity (HMBC)  spectra, same correlations to C-3 and C-5, which indicated that these two  methyls nected to a same carbon (Fig. 2) Company with other correla tions. such", "text": "Khaysenelide A (1) was obtained as colorless   crystal methanol water (MeOH H,O), and had the molecular formula [M+Na] The 'H-NMR 1.16. 1.07, 0.90, each 3H, s) a methoxyl heteronuclear multiple   bond   connectivity (HMBC)  spectra, same correlations to C-3 and C-5, which indicated that these two  methyls nected to a same carbon (Fig. 2) Company with other correla tions. such"}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 1, "bbox": {"l": 25.333333333333332, "t": 80.0, "r": 174.66666666666666, "b": 69.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 35]}], "orig": "Fig. The Structures of Compounds 16", "text": "Fig. The Structures of Compounds 16"}, {"self_ref": "#/texts/12", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 198.66666666666666, "t": 228.33333333333337, "r": 213.33333333333334, "b": 218.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "OR", "text": "OR"}, {"self_ref": "#/texts/13", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 315.6666666666667, "t": 226.66666666666663, "r": 329.6666666666667, "b": 214.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "23/", "text": "23/"}, {"self_ref": "#/texts/14", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 452.6666666666667, "t": 225.0, "r": 467.3333333333333, "b": 215.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "OR", "text": "OR"}, {"self_ref": "#/texts/15", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 305.0, "t": 220.0, "r": 311.6666666666667, "b": 212.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "0", "text": "0"}, {"self_ref": "#/texts/16", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 442.6666666666667, "t": 220.33333333333337, "r": 455.3333333333333, "b": 211.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "23/", "text": "23/"}, {"self_ref": "#/texts/17", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 199.33333333333334, "t": 216.33333333333337, "r": 208.66666666666666, "b": 207.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "23", "text": "23"}, {"self_ref": "#/texts/18", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 434.0, "t": 215.66666666666663, "r": 442.0, "b": 207.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "0", "text": "0"}, {"self_ref": "#/texts/19", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 326.0, "t": 208.33333333333337, "r": 335.3333333333333, "b": 200.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "22", "text": "22"}, {"self_ref": "#/texts/20", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 165.0, "t": 202.0, "r": 172.33333333333334, "b": 195.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "0", "text": "0"}, {"self_ref": "#/texts/21", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 201.33333333333334, "t": 205.0, "r": 210.66666666666666, "b": 197.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "22", "text": "22"}, {"self_ref": "#/texts/22", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 285.3333333333333, "t": 204.33333333333337, "r": 300.0, "b": 195.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "HO", "text": "HO"}, {"self_ref": "#/texts/23", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 418.6666666666667, "t": 200.33333333333337, "r": 427.3333333333333, "b": 192.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "0", "text": "0"}, {"self_ref": "#/texts/24", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 425.3333333333333, "t": 208.33333333333337, "r": 434.6666666666667, "b": 195.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "24", "text": "24"}, {"self_ref": "#/texts/25", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 455.0, "t": 200.0, "r": 463.6666666666667, "b": 193.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "22", "text": "22"}, {"self_ref": "#/texts/26", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 174.66666666666666, "t": 197.66666666666663, "r": 184.0, "b": 189.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "18", "text": "18"}, {"self_ref": "#/texts/27", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 191.33333333333334, "t": 198.33333333333337, "r": 201.33333333333334, "b": 190.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "20", "text": "20"}, {"self_ref": "#/texts/28", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 318.0, "t": 198.33333333333337, "r": 327.3333333333333, "b": 190.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "20", "text": "20"}, {"self_ref": "#/texts/29", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 164.33333333333334, "t": 192.0, "r": 173.0, "b": 185.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "12", "text": "12"}, {"self_ref": "#/texts/30", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 446.0, "t": 193.0, "r": 455.3333333333333, "b": 185.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "20", "text": "20"}, {"self_ref": "#/texts/31", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 191.0, "t": 188.66666666666663, "r": 200.33333333333334, "b": 182.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "17", "text": "17"}, {"self_ref": "#/texts/32", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 164.66666666666666, "t": 181.0, "r": 172.66666666666666, "b": 173.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "C", "text": "C"}, {"self_ref": "#/texts/33", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 178.66666666666666, "t": 178.33333333333337, "r": 188.66666666666666, "b": 170.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "[13", "text": "[13"}, {"self_ref": "#/texts/34", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 196.0, "t": 181.0, "r": 204.0, "b": 173.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "O", "text": "O"}, {"self_ref": "#/texts/35", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 251.66666666666666, "t": 178.0, "r": 258.3333333333333, "b": 170.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "H", "text": "H"}, {"self_ref": "#/texts/36", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 122.0, "t": 175.0, "r": 130.0, "b": 167.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "H", "text": "H"}, {"self_ref": "#/texts/37", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 197.33333333333334, "t": 172.33333333333337, "r": 210.66666666666666, "b": 164.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "116", "text": "116"}, {"self_ref": "#/texts/38", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 220.0, "t": 172.33333333333337, "r": 249.33333333333334, "b": 162.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "MeOC", "text": "MeOC"}, {"self_ref": "#/texts/39", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 383.0, "t": 174.66666666666663, "r": 389.6666666666667, "b": 166.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "H", "text": "H"}, {"self_ref": "#/texts/40", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 420.6666666666667, "t": 175.0, "r": 434.6666666666667, "b": 165.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "OH", "text": "OH"}, {"self_ref": "#/texts/41", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 90.33333333333333, "t": 169.33333333333337, "r": 121.0, "b": 157.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "MeOzC", "text": "MeOzC"}, {"self_ref": "#/texts/42", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 144.66666666666666, "t": 169.0, "r": 154.66666666666666, "b": 161.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "10", "text": "10"}, {"self_ref": "#/texts/43", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 106.0, "t": 157.0, "r": 121.33333333333333, "b": 147.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "HO", "text": "HO"}, {"self_ref": "#/texts/44", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 235.33333333333334, "t": 161.0, "r": 250.0, "b": 152.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "HO", "text": "HO"}, {"self_ref": "#/texts/45", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 295.3333333333333, "t": 158.33333333333337, "r": 314.0, "b": 149.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "30 O", "text": "30 O"}, {"self_ref": "#/texts/46", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 366.6666666666667, "t": 159.0, "r": 381.3333333333333, "b": 150.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "HO", "text": "HO"}, {"self_ref": "#/texts/47", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 118.33333333333333, "t": 150.66666666666663, "r": 153.0, "b": 138.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "284A 07", "text": "284A 07"}, {"self_ref": "#/texts/48", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 168.0, "t": 145.0, "r": 178.0, "b": 137.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "30", "text": "30"}, {"self_ref": "#/texts/49", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 282.0, "t": 147.0, "r": 293.3333333333333, "b": 138.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "R,", "text": "R,"}, {"self_ref": "#/texts/50", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 410.0, "t": 146.33333333333337, "r": 428.6666666666667, "b": 137.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "OAc", "text": "OAc"}, {"self_ref": "#/texts/51", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 249.0, "t": 138.66666666666663, "r": 258.3333333333333, "b": 132.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "28", "text": "28"}, {"self_ref": "#/texts/52", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 377.3333333333333, "t": 139.0, "r": 387.3333333333333, "b": 131.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "28", "text": "28"}, {"self_ref": "#/texts/53", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 402.0, "t": 141.66666666666663, "r": 412.0, "b": 133.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "29", "text": "29"}, {"self_ref": "#/texts/54", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 133.33333333333334, "t": 129.0, "r": 142.66666666666666, "b": 121.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "29", "text": "29"}, {"self_ref": "#/texts/55", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 402.0, "t": 129.0, "r": 413.3333333333333, "b": 120.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "[ 3", "text": "[ 3"}, {"self_ref": "#/texts/56", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 270.0, "t": 121.66666666666663, "r": 281.3333333333333, "b": 111.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "Rz", "text": "Rz"}, {"self_ref": "#/texts/57", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 400.0, "t": 121.0, "r": 414.0, "b": 113.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "OH", "text": "OH"}, {"self_ref": "#/texts/58", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 130.66666666666666, "t": 107.66666666666663, "r": 169.33333333333334, "b": 99.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "KS-1 R=H", "text": "KS-1 R=H"}, {"self_ref": "#/texts/59", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 396.6666666666667, "t": 103.0, "r": 445.3333333333333, "b": 92.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 10]}], "orig": "KS-5 R-CH;", "text": "KS-5 R-CH;"}, {"self_ref": "#/texts/60", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 130.66666666666666, "t": 97.66666666666663, "r": 179.33333333333334, "b": 87.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 10]}], "orig": "KS-2 R=CH;", "text": "KS-2 R=CH;"}, {"self_ref": "#/texts/61", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 251.66666666666666, "t": 96.66666666666663, "r": 327.6666666666667, "b": 84.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "KS-4 R,=OAc R2=0", "text": "KS-4 R,=OAc R2=0"}, {"self_ref": "#/texts/62", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 269.6740093166806, "t": 145.9453729816263, "r": 281.9926573499861, "b": 134.38796035170708, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "29A", "text": "29A"}, {"self_ref": "#/texts/63", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 1, "bbox": {"l": 29.333333333333332, "t": 56.33333333333337, "r": 186.0, "b": 47.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 47]}], "orig": "Thesc authors contributed equally to this work.", "text": "Thesc authors contributed equally to this work."}, {"self_ref": "#/texts/64", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 1, "bbox": {"l": 31.333333333333332, "t": 38.33333333333337, "r": 293.6666666666667, "b": 26.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 70]}], "orig": "To whom correspondence should be addressed. e-mail: <EMAIL>", "text": "To whom correspondence should be addressed. e-mail: <EMAIL>"}, {"self_ref": "#/texts/65", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 1, "bbox": {"l": 389.3333333333333, "t": 26.0, "r": 530.6593240166527, "b": 14.387960351707079, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "2015 The Pharmaceutical Society of Japan", "text": "2015 The Pharmaceutical Society of Japan"}, {"self_ref": "#/texts/66", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 2, "bbox": {"l": 39.333333333333336, "t": 771.3333333333334, "r": 54.0, "b": 762.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "306", "text": "306"}, {"self_ref": "#/texts/67", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 2, "bbox": {"l": 258.6666666666667, "t": 771.3333333333334, "r": 324.6666666666667, "b": 762.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 17]}], "orig": "Chem. Pharm  Bull", "text": "Chem. Pharm  Bull"}, {"self_ref": "#/texts/68", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 2, "bbox": {"l": 470.0, "t": 771.3333333333334, "r": 545.3333333333334, "b": 761.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 20]}], "orig": "Vol. 63 No. 4 (2015)", "text": "Vol. 63 No. 4 (2015)"}, {"self_ref": "#/texts/69", "parent": {"$ref": "#/tables/0"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 2, "bbox": {"l": 39.0, "t": 754.0, "r": 334.0, "b": 740.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 72]}], "orig": "Table The 'H-NMR (SOOMHz) Spectroscopic Data of Compounds 1 6 in DMSO-d,", "text": "Table The 'H-NMR (SOOMHz) Spectroscopic Data of Compounds 1 6 in DMSO-d,"}, {"self_ref": "#/texts/70", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 2, "bbox": {"l": 48.0, "t": 369.3333333333333, "r": 255.33333333333334, "b": 359.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 70]}], "orig": "a) The signal of overlapped, b) The signal observed from HISQC partial", "text": "a) The signal of overlapped, b) The signal observed from HISQC partial", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/71", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 2, "bbox": {"l": 40.03505834019739, "t": 162.62114664470175, "r": 231.33333333333334, "b": 150.045520021965, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 45]}], "orig": "2. The HMBC and ROESY of the Compound Fig Key", "text": "2. The HMBC and ROESY of the Compound Fig Key"}, {"self_ref": "#/texts/72", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 257.0, "t": 344.3333333333333, "r": 281.6666666666667, "b": 331.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "23 OH", "text": "23 OH"}, {"self_ref": "#/texts/73", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 440.6666666666667, "t": 341.3333333333333, "r": 454.6666666666667, "b": 333.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "OH", "text": "OH"}, {"self_ref": "#/texts/74", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 233.0, "t": 321.0, "r": 240.33333333333334, "b": 314.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "21", "text": "21"}, {"self_ref": "#/texts/75", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 272.3333333333333, "t": 314.3333333333333, "r": 279.6666666666667, "b": 309.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "22", "text": "22"}, {"self_ref": "#/texts/76", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 417.6666666666667, "t": 315.0, "r": 425.6666666666667, "b": 309.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "21", "text": "21"}, {"self_ref": "#/texts/77", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 442.6666666666667, "t": 315.3333333333333, "r": 452.0, "b": 307.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "22", "text": "22"}, {"self_ref": "#/texts/78", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 222.0, "t": 311.3333333333333, "r": 230.0, "b": 302.6666666666667, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "0", "text": "0"}, {"self_ref": "#/texts/79", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 395.6666666666667, "t": 308.3333333333333, "r": 403.0, "b": 301.6666666666667, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "0", "text": "0"}, {"self_ref": "#/texts/80", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 245.0, "t": 305.0, "r": 253.0, "b": 298.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "20", "text": "20"}, {"self_ref": "#/texts/81", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 427.6666666666667, "t": 301.6666666666667, "r": 436.3333333333333, "b": 295.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "20", "text": "20"}, {"self_ref": "#/texts/82", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 397.0, "t": 289.0, "r": 404.3333333333333, "b": 283.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "12", "text": "12"}, {"self_ref": "#/texts/83", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 221.33333333333334, "t": 271.33333333333337, "r": 235.33333333333334, "b": 262.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "OH", "text": "OH"}, {"self_ref": "#/texts/84", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 387.3333333333333, "t": 269.33333333333337, "r": 402.0, "b": 261.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "HO", "text": "HO"}, {"self_ref": "#/texts/85", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 360.6666666666667, "t": 266.0, "r": 381.3333333333333, "b": 256.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "19 H,", "text": "19 H,"}, {"self_ref": "#/texts/86", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 441.3333333333333, "t": 265.33333333333337, "r": 450.0, "b": 257.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "h6", "text": "h6"}, {"self_ref": "#/texts/87", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 296.0, "t": 260.66666666666663, "r": 327.3333333333333, "b": 252.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "MeOOC", "text": "MeOOC"}, {"self_ref": "#/texts/88", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 424.6666666666667, "t": 260.0, "r": 434.0, "b": 252.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "15", "text": "15"}, {"self_ref": "#/texts/89", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 365.6666666666667, "t": 255.66666666666663, "r": 375.0, "b": 249.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "10", "text": "10"}, {"self_ref": "#/texts/90", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 140.66666666666666, "t": 245.33333333333337, "r": 154.66666666666666, "b": 236.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "HO", "text": "HO"}, {"self_ref": "#/texts/91", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 314.0, "t": 243.33333333333337, "r": 328.6666666666667, "b": 234.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "HO", "text": "HO"}, {"self_ref": "#/texts/92", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 409.6666666666667, "t": 243.66666666666663, "r": 416.3333333333333, "b": 235.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "H", "text": "H"}, {"self_ref": "#/texts/93", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 160.33333333333334, "t": 231.0, "r": 168.33333333333334, "b": 224.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "28", "text": "28"}, {"self_ref": "#/texts/94", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 335.6666666666667, "t": 231.0, "r": 344.3333333333333, "b": 224.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "28", "text": "28"}, {"self_ref": "#/texts/95", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 365.3333333333333, "t": 234.0, "r": 374.0, "b": 226.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "0", "text": "0"}, {"self_ref": "#/texts/96", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 398.6666666666667, "t": 235.33333333333337, "r": 407.3333333333333, "b": 227.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "30", "text": "30"}, {"self_ref": "#/texts/97", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 265.0, "t": 181.66666666666663, "r": 284.3333333333333, "b": 175.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "HMBC", "text": "HMBC"}, {"self_ref": "#/texts/98", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 264.6666666666667, "t": 176.0, "r": 288.0, "b": 168.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "ROESY", "text": "ROESY"}, {"self_ref": "#/texts/99", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 39.0, "t": 138.6180238795066, "r": 288.6666666666667, "b": 44.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 427]}], "orig": "and C-9, the characteristic [3,3,1] A/B system  could be deduced Meanwhile, in the HMBC, the strong correlations H-14 to C-12 C-16 constructed the C and D in 1. The aforementioned information and typical   structural moieties indicated that 1 was a mexicanolide-type limonoid, except for a series of weak NMR signals for 23-hydroxybutenolide moiety   instead of the typical signals for B-substituted  furan ring from rings ring", "text": "and C-9, the characteristic [3,3,1] A/B system  could be deduced Meanwhile, in the HMBC, the strong correlations H-14 to C-12 C-16 constructed the C and D in 1. The aforementioned information and typical   structural moieties indicated that 1 was a mexicanolide-type limonoid, except for a series of weak NMR signals for 23-hydroxybutenolide moiety   instead of the typical signals for B-substituted  furan ring from rings ring"}, {"self_ref": "#/texts/100", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 297.0, "t": 139.66666666666663, "r": 546.0, "b": 79.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 313]}], "orig": "13,14) Fortunately; the good high quality single-crystal of 1 was obtained successfully, and the   X-ray   crystallography data (Fig. 3) confirmed that 1 was mexicanolide-type limonoid with a 23-hydroxybutenolide moiety at C-17 as our deduction; and the absolute   configuration of was also   finally   deterring:", "text": "13,14) Fortunately; the good high quality single-crystal of 1 was obtained successfully, and the   X-ray   crystallography data (Fig. 3) confirmed that 1 was mexicanolide-type limonoid with a 23-hydroxybutenolide moiety at C-17 as our deduction; and the absolute   configuration of was also   finally   deterring:"}, {"self_ref": "#/texts/101", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 306.0, "t": 69.66666666666663, "r": 545.3333333333334, "b": 43.02847966121681, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 92]}], "orig": "Khaysenelide B (2) white amorphous   powder; gave molecular formula (Calcd for   CzsHoNOn Ou", "text": "Khaysenelide B (2) white amorphous   powder; gave molecular formula (Calcd for   CzsHoNOn Ou"}, {"self_ref": "#/texts/102", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 3, "bbox": {"l": 27.333333333333332, "t": 769.6666666666666, "r": 102.66666666666667, "b": 759.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "Vol. 63, No. 4 (2015)", "text": "Vol. 63, No. 4 (2015)"}, {"self_ref": "#/texts/103", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 3, "bbox": {"l": 517.3333333333334, "t": 770.3333333333334, "r": 532.0, "b": 762.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "307", "text": "307"}, {"self_ref": "#/texts/104", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 3, "bbox": {"l": 27.333333333333332, "t": 596.0, "r": 214.0, "b": 585.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 48]}], "orig": "Fig 3 Single-Crystal X-Ray Structure of Compound", "text": "Fig 3 Single-Crystal X-Ray Structure of Compound"}, {"self_ref": "#/texts/105", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 191.33333333333334, "t": 728.3333333333334, "r": 206.66666666666666, "b": 720.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "C21", "text": "C21"}, {"self_ref": "#/texts/106", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 103.33333333333333, "t": 724.3333333333334, "r": 118.0, "b": 716.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "C28", "text": "C28"}, {"self_ref": "#/texts/107", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 63.333333333333336, "t": 715.0, "r": 78.0, "b": 706.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "C29", "text": "C29"}, {"self_ref": "#/texts/108", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 130.66666666666666, "t": 707.0, "r": 142.0, "b": 698.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "C3", "text": "C3"}, {"self_ref": "#/texts/109", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 192.66666666666666, "t": 703.6666666666666, "r": 208.0, "b": 695.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "C17", "text": "C17"}, {"self_ref": "#/texts/110", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 231.33333333333334, "t": 707.0, "r": 246.0, "b": 699.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "C22", "text": "C22"}, {"self_ref": "#/texts/111", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 132.0, "t": 691.0, "r": 143.33333333333334, "b": 682.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "C2", "text": "C2"}, {"self_ref": "#/texts/112", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 240.66666666666666, "t": 689.6666666666666, "r": 251.33333333333334, "b": 681.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "08", "text": "08"}, {"self_ref": "#/texts/113", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 147.33333333333334, "t": 686.3333333333334, "r": 165.33333333333334, "b": 678.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "(C3o", "text": "(C3o"}, {"self_ref": "#/texts/114", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 87.33333333333333, "t": 677.0, "r": 98.66666666666667, "b": 668.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "C5", "text": "C5"}, {"self_ref": "#/texts/115", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 71.33333333333333, "t": 672.3333333333334, "r": 82.66666666666667, "b": 664.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "C6", "text": "C6"}, {"self_ref": "#/texts/116", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 137.33333333333334, "t": 672.3333333333334, "r": 148.66666666666666, "b": 663.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "C1", "text": "C1"}, {"self_ref": "#/texts/117", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 162.66666666666666, "t": 673.0, "r": 200.66666666666666, "b": 663.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "CC12 Ciz", "text": "CC12 Ciz"}, {"self_ref": "#/texts/118", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 221.33333333333334, "t": 673.6666666666666, "r": 236.0, "b": 665.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "C16", "text": "C16"}, {"self_ref": "#/texts/119", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 118.0, "t": 659.0, "r": 128.66666666666666, "b": 651.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "IC1", "text": "IC1"}, {"self_ref": "#/texts/120", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 227.33333333333334, "t": 652.3333333333334, "r": 242.66666666666666, "b": 643.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "C18", "text": "C18"}, {"self_ref": "#/texts/121", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 136.0, "t": 642.3333333333334, "r": 146.0, "b": 634.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "Cg", "text": "Cg"}, {"self_ref": "#/texts/122", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 165.33333333333334, "t": 643.6666666666666, "r": 177.33333333333334, "b": 634.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "C8", "text": "C8"}, {"self_ref": "#/texts/123", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 179.33333333333334, "t": 641.0, "r": 193.33333333333334, "b": 633.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "C14", "text": "C14"}, {"self_ref": "#/texts/124", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 71.33333333333333, "t": 625.6666666666666, "r": 86.66666666666667, "b": 617.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "C31", "text": "C31"}, {"self_ref": "#/texts/125", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 109.33333333333333, "t": 623.0, "r": 124.66666666666667, "b": 614.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "IC19", "text": "IC19"}, {"self_ref": "#/texts/126", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 165.33333333333334, "t": 625.6666666666666, "r": 176.66666666666666, "b": 617.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "06", "text": "06"}, {"self_ref": "#/texts/127", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 27.666666666666668, "t": 566.0, "r": 277.3333333333333, "b": 391.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 774]}], "orig": "[M+NH,]*) from the HR-ESI-MS, 14 mass units more than 1, in accordance with the presence of an additional methylene group. The ID-NMR data of 2 were similar to those of 1 eXcept for the presence of 23-methoxybutenolide moiety at €-17, which were supported by the HMBC correlations   from H-17 The relative configuration of 2 was assigned from the rotating frame Overhauser enhancement spectroscopy (ROESY) specThe correlations that such groups were cofacial, and these were assigned arbitrarily as ß-oriented.  The ROESY cross-peaks of Me-I9/H-9, OH-8/H-9 and H-2/Me-29 showed the a-orientation of those groups. However; the available evidences were insufficient to determine  the configuration at C-23. Therefore, the stercochemistry of compound 2 was established as shown.", "text": "[M+NH,]*) from the HR-ESI-MS, 14 mass units more than 1, in accordance with the presence of an additional methylene group. The ID-NMR data of 2 were similar to those of 1 eXcept for the presence of 23-methoxybutenolide moiety at €-17, which were supported by the HMBC correlations   from H-17 The relative configuration of 2 was assigned from the rotating frame Overhauser enhancement spectroscopy (ROESY) specThe correlations that such groups were cofacial, and these were assigned arbitrarily as ß-oriented.  The ROESY cross-peaks of Me-I9/H-9, OH-8/H-9 and H-2/Me-29 showed the a-orientation of those groups. However; the available evidences were insufficient to determine  the configuration at C-23. Therefore, the stercochemistry of compound 2 was established as shown."}, {"self_ref": "#/texts/128", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 28.0, "t": 392.3333333333333, "r": 279.0, "b": 151.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 824]}], "orig": "Khaysenelide € (3), possessed the molecular formula C2,H,,012 by HR-ESI-MS. The 'H-NMR displayed the existence of three tertiary methyls implied that 3 was a rearranged phragmalin-type limonoid as khayanolide B.'5) Extensive analysis of ID- and 2D-NMR data of 3 suggested a close similarity between 3 and khayanolide B.15) sharing the same B. C, and D rings. The main differences were the signal absence of furan (E) ever; degradation of E was unreasonable according to the HR-ESI-MS. Weak carbon and proton signals observed from 120.3(C-22), 98.7 (C-21) and 8n 6.20 (H-22), 6.22 (H-21), indicated that the furan was modified to be 21-hydroxybutenolide moiety as trichano above deduce was confirmed and the structure of 3 was determined as depicted in Fig. 1, and the absolute configuration of I4R. I7R Ar ring how ring ring", "text": "Khaysenelide € (3), possessed the molecular formula C2,H,,012 by HR-ESI-MS. The 'H-NMR displayed the existence of three tertiary methyls implied that 3 was a rearranged phragmalin-type limonoid as khayanolide B.'5) Extensive analysis of ID- and 2D-NMR data of 3 suggested a close similarity between 3 and khayanolide B.15) sharing the same B. C, and D rings. The main differences were the signal absence of furan (E) ever; degradation of E was unreasonable according to the HR-ESI-MS. Weak carbon and proton signals observed from 120.3(C-22), 98.7 (C-21) and 8n 6.20 (H-22), 6.22 (H-21), indicated that the furan was modified to be 21-hydroxybutenolide moiety as trichano above deduce was confirmed and the structure of 3 was determined as depicted in Fig. 1, and the absolute configuration of I4R. I7R Ar ring how ring ring"}, {"self_ref": "#/texts/129", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 30.0, "t": 150.66666666666663, "r": 279.6666666666667, "b": 46.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 402]}], "orig": "Khaysenelide D white powder, its molecular formula was determined as at The whole feature of the 'H- and \"C-NMR spectral data (Tables 1 2) indicated that 2 also possessed rearranged phragmalin-type limonoid as but   similar carbon and proton signals about E indicated that 4 also possess 21-hydroxybutenolide   moiety which was in accordance with the molecular formula '4) Thus, the structure of 4 ring", "text": "Khaysenelide D white powder, its molecular formula was determined as at The whole feature of the 'H- and \"C-NMR spectral data (Tables 1 2) indicated that 2 also possessed rearranged phragmalin-type limonoid as but   similar carbon and proton signals about E indicated that 4 also possess 21-hydroxybutenolide   moiety which was in accordance with the molecular formula '4) Thus, the structure of 4 ring"}, {"self_ref": "#/texts/130", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 3, "bbox": {"l": 285.3333333333333, "t": 590.3333333333334, "r": 478.0, "b": 579.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 51]}], "orig": "Fig. 4 Single-Crystal X-Ray Structure of Compound 3", "text": "Fig. 4 Single-Crystal X-Ray Structure of Compound 3"}, {"self_ref": "#/texts/131", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 476.3333333333333, "t": 748.0, "r": 483.6666666666667, "b": 742.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "011", "text": "011"}, {"self_ref": "#/texts/132", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 500.3333333333333, "t": 723.3333333333334, "r": 511.0, "b": 716.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "C23", "text": "C23"}, {"self_ref": "#/texts/133", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 443.6666666666667, "t": 710.0, "r": 453.6666666666667, "b": 703.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "012", "text": "012"}, {"self_ref": "#/texts/134", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 494.3333333333333, "t": 704.6666666666666, "r": 505.0, "b": 698.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "C22", "text": "C22"}, {"self_ref": "#/texts/135", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 370.3333333333333, "t": 696.6666666666666, "r": 377.6666666666667, "b": 690.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "03", "text": "03"}, {"self_ref": "#/texts/136", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 369.0, "t": 650.0, "r": 379.0, "b": 643.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "Cio", "text": "Cio"}, {"self_ref": "#/texts/137", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 415.0, "t": 621.3333333333334, "r": 423.0, "b": 616.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "0", "text": "0"}, {"self_ref": "#/texts/138", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 363.6666666666667, "t": 612.6666666666666, "r": 371.0, "b": 606.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "01", "text": "01"}, {"self_ref": "#/texts/139", "parent": {"$ref": "#/tables/1"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 3, "bbox": {"l": 286.0, "t": 564.6044154273903, "r": 534.0, "b": 543.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 36]}], "orig": "Table 2 ofCompounds 6 in DMSO-d Data", "text": "Table 2 ofCompounds 6 in DMSO-d Data"}, {"self_ref": "#/texts/140", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 305.0, "t": 185.33333333333337, "r": 535.3333333333334, "b": 174.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 72]}, {"page_no": 3, "bbox": {"l": 305.0, "t": 185.33333333333337, "r": 535.3333333333334, "b": 174.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [73, 268]}], "orig": "The signal based on \"C-NMR and reference b) The partial signal obpartial was determined as depicted in Fig  1, and the absolute configuration of carbon skeleton was also as same as 3 based on the similar circular dichroism (CD) spectra between compounds 3 and (Fig. 6)", "text": "The signal based on \"C-NMR and reference b) The partial signal obpartial was determined as depicted in Fig  1, and the absolute configuration of carbon skeleton was also as same as 3 based on the similar circular dichroism (CD) spectra between compounds 3 and (Fig. 6)"}, {"self_ref": "#/texts/141", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 288.0, "t": 105.33333333333337, "r": 536.3333333333334, "b": 71.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 70]}], "orig": "Khaysenelide E (5), a white powder; was shown to have the Based on the", "text": "Khaysenelide E (5), a white powder; was shown to have the Based on the"}, {"self_ref": "#/texts/142", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 4, "bbox": {"l": 38.0, "t": 774.0, "r": 52.666666666666664, "b": 766.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "308", "text": "308"}, {"self_ref": "#/texts/143", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 4, "bbox": {"l": 470.0, "t": 772.0, "r": 544.6666666666666, "b": 762.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "Vol. 63. No. 4 (2015)", "text": "Vol. 63. No. 4 (2015)"}, {"self_ref": "#/texts/144", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 4, "bbox": {"l": 36.666666666666664, "t": 599.6666666666666, "r": 208.0, "b": 589.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 42]}], "orig": "Fig. 5. Thc ECD Spectra of Compounds and 2", "text": "Fig. 5. Thc ECD Spectra of Compounds and 2"}, {"self_ref": "#/texts/145", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 52.333333333333336, "t": 687.6666666666666, "r": 82.66666666666667, "b": 678.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "CDmdeg]", "text": "CDmdeg]"}, {"self_ref": "#/texts/146", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 145.66666666666666, "t": 619.6666666666666, "r": 157.0, "b": 613.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "250", "text": "250"}, {"self_ref": "#/texts/147", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 208.33333333333334, "t": 619.6666666666666, "r": 219.66666666666666, "b": 613.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "300", "text": "300"}, {"self_ref": "#/texts/148", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 259.0, "t": 619.6666666666666, "r": 270.3333333333333, "b": 613.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "340", "text": "340"}, {"self_ref": "#/texts/149", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 151.33333333333334, "t": 613.3333333333334, "r": 202.0, "b": 604.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 15]}], "orig": "Wavelength [nm]", "text": "Wavelength [nm]"}, {"self_ref": "#/texts/150", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 4, "bbox": {"l": 36.0, "t": 413.3333333333333, "r": 207.33333333333334, "b": 402.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 44]}], "orig": "Fig. 6. The ECD Spectra of Compounds 3 and 4", "text": "Fig. 6. The ECD Spectra of Compounds 3 and 4"}, {"self_ref": "#/texts/151", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 45.0, "t": 505.6666666666667, "r": 77.33333333333333, "b": 496.6666666666667, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "CD[mdeg]", "text": "CD[mdeg]"}, {"self_ref": "#/texts/152", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 77.0, "t": 435.0, "r": 89.0, "b": 428.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "200", "text": "200"}, {"self_ref": "#/texts/153", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 143.66666666666666, "t": 434.3333333333333, "r": 155.66666666666666, "b": 427.6666666666667, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "250", "text": "250"}, {"self_ref": "#/texts/154", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 210.33333333333334, "t": 434.3333333333333, "r": 222.33333333333334, "b": 427.6666666666667, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "300", "text": "300"}, {"self_ref": "#/texts/155", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 264.3333333333333, "t": 434.3333333333333, "r": 275.6666666666667, "b": 427.6666666666667, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "340", "text": "340"}, {"self_ref": "#/texts/156", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 149.66666666666666, "t": 428.0, "r": 203.33333333333334, "b": 418.6666666666667, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 15]}], "orig": "Wavclength [nm]", "text": "Wavclength [nm]"}, {"self_ref": "#/texts/157", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 35.20092023383405, "t": 372.0, "r": 285.6666666666667, "b": 163.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 900]}], "orig": "5 was also a rearranged phragmalin-type limonoid as 3. Extensive analyses of ID-NMR data (Tables 1, 2) revealed that 5 shared the common Ap A;, C, and D with 1-O-acctylkhayanolide B.'5)  However, the   characteristic nals for furan moiety were absent in the NMR spectra of signals al 133.0. 149.7. and 169.2, which were in agreement  with the presence of a 23-hydroxybutenolide ety as compound 2 Further HMBC correlations, from H-17 to C-23, indicated that  23-methoxybutenolide   moiety connected at C-17 in 5 The relative configuration of 5 was established by ROESY   spectrum in which corrclations H-17/12ß indicated that these groups were on the same side of the molecule, and were arbitrarily assigned as ß-oriented. The   ROESY correlations of Me-19/H-9. OH-8/H-9   implied that were a-oriented .  Thus, the structure of 5 was determined as depicted in Fig. 1. pound Av; rings sig; moithey they", "text": "5 was also a rearranged phragmalin-type limonoid as 3. Extensive analyses of ID-NMR data (Tables 1, 2) revealed that 5 shared the common Ap A;, C, and D with 1-O-acctylkhayanolide B.'5)  However, the   characteristic nals for furan moiety were absent in the NMR spectra of signals al 133.0. 149.7. and 169.2, which were in agreement  with the presence of a 23-hydroxybutenolide ety as compound 2 Further HMBC correlations, from H-17 to C-23, indicated that  23-methoxybutenolide   moiety connected at C-17 in 5 The relative configuration of 5 was established by ROESY   spectrum in which corrclations H-17/12ß indicated that these groups were on the same side of the molecule, and were arbitrarily assigned as ß-oriented. The   ROESY correlations of Me-19/H-9. OH-8/H-9   implied that were a-oriented .  Thus, the structure of 5 was determined as depicted in Fig. 1. pound Av; rings sig; moithey they"}, {"self_ref": "#/texts/158", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 34.0, "t": 164.33333333333337, "r": 284.0, "b": 48.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 481]}], "orig": "Khaysenelide F (6) white   powder; was given the than 5 The whole feature of the 'H- and \"C-NMR spectral data indicated that 6 possessed the same rearranged phragmalin-type limonoid skeleton as 5 The weak but similar carbon and proton signals (Tables 1, 2) about E indicated that 6 also possessed 23-hydroxybutenolide moiety at C-17 as 5, except for the absence of OMe-23. Thus; the structure of 6 was determined to be 23-O-demethoxy derivative of 5, and named khaysenelide F. ring", "text": "Khaysenelide F (6) white   powder; was given the than 5 The whole feature of the 'H- and \"C-NMR spectral data indicated that 6 possessed the same rearranged phragmalin-type limonoid skeleton as 5 The weak but similar carbon and proton signals (Tables 1, 2) about E indicated that 6 also possessed 23-hydroxybutenolide moiety at C-17 as 5, except for the absence of OMe-23. Thus; the structure of 6 was determined to be 23-O-demethoxy derivative of 5, and named khaysenelide F. ring"}, {"self_ref": "#/texts/159", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 294.3333333333333, "t": 754.3333333333334, "r": 544.3333333333334, "b": 686.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 215]}], "orig": "The structures of   known   compounds were   identified as khayanolide B 1-O-deacetyl-2a-hydroxykhayanolide senegalensions A 1 (12)7) by comparison of their physical and spectroscopic data with those of literatures.", "text": "The structures of   known   compounds were   identified as khayanolide B 1-O-deacetyl-2a-hydroxykhayanolide senegalensions A 1 (12)7) by comparison of their physical and spectroscopic data with those of literatures."}, {"self_ref": "#/texts/160", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 294.0, "t": 685.3333333333334, "r": 544.3333333333334, "b": 638.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 202]}], "orig": "Compounds 1-12 was evaluated for inhibitory effect on NO production in   lipopolysaccaride-activeted RAW264.7 macrophages.  Unfortunately; did not show potent activity value more than 50 uM). they (ICso", "text": "Compounds 1-12 was evaluated for inhibitory effect on NO production in   lipopolysaccaride-activeted RAW264.7 macrophages.  Unfortunately; did not show potent activity value more than 50 uM). they (ICso"}, {"self_ref": "#/texts/161", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 4, "bbox": {"l": 295.0, "t": 626.3333333333334, "r": 357.6666666666667, "b": 613.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 12]}], "orig": "Experimental", "text": "Experimental", "level": 1}, {"self_ref": "#/texts/162", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 293.0, "t": 614.3333333333334, "r": 543.3333333333334, "b": 372.7817484442847, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 968]}], "orig": "General Experimental Procedures Optical rotations were measured with a JASCO P-1020 polarimeter   (JASCO Japan). CD spectra were obtained on JASCO J 810 spectropolarimeter (JASCO, Tokyo; Japan). UV spectra recorded on a UV-2450 UVIVis spectrophotometer (Shimadzu; Japan)   X-Ray were measured Bruker Smart CCD with graphite monochromator with CuKa   radiation at 291(2)K (Bruker. Karlsruhe, Germany)_ NMR spectra were recorded on Bruker AV III-500 NMR instrument (H: 125 MHz) with tetramethyl silane (TMS) as internal   standard (Bruker;  Karlsruhe, Germany). HR-ESI-MS was obtained on an Agilent 6520B Q-TOF Column chromatography (CC) was carried oul on   silica gel (200-300mesh, Qingdao Marine Chemical Inc., Qingdao, (Mitsubishi Chemical Tokyo; Japan), MPLC (Beijing H&E Ltd._ Beijing,   China)   Preparative   HPLC was carried out using a Shimadzu LC-6A instrument  with a SPD-IOA detector   (Shimadzu;  Tokyo; Japan) ashimTokyo. Tokyo; gel Corp . Co.. using pack", "text": "General Experimental Procedures Optical rotations were measured with a JASCO P-1020 polarimeter   (JASCO Japan). CD spectra were obtained on JASCO J 810 spectropolarimeter (JASCO, Tokyo; Japan). UV spectra recorded on a UV-2450 UVIVis spectrophotometer (Shimadzu; Japan)   X-Ray were measured Bruker Smart CCD with graphite monochromator with CuKa   radiation at 291(2)K (Bruker. Karlsruhe, Germany)_ NMR spectra were recorded on Bruker AV III-500 NMR instrument (H: 125 MHz) with tetramethyl silane (TMS) as internal   standard (Bruker;  Karlsruhe, Germany). HR-ESI-MS was obtained on an Agilent 6520B Q-TOF Column chromatography (CC) was carried oul on   silica gel (200-300mesh, Qingdao Marine Chemical Inc., Qingdao, (Mitsubishi Chemical Tokyo; Japan), MPLC (Beijing H&E Ltd._ Beijing,   China)   Preparative   HPLC was carried out using a Shimadzu LC-6A instrument  with a SPD-IOA detector   (Shimadzu;  Tokyo; Japan) ashimTokyo. Tokyo; gel Corp . Co.. using pack"}, {"self_ref": "#/texts/163", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 293.6666666666667, "t": 371.0, "r": 543.0, "b": 289.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 375]}], "orig": "Plant Material The stem bark of the Khaya se<PERSON>galensis were collected from Xishuangbanna,  Yunnan Province; China in 2009, and were authenticated by Professor Mian Department   of  Pharmacognosy;  China Pharmaceuti cal University. A voucher specimen (No. 2009-KS-LQP) was deposited in the Department of Natural Medicinal Chemistry; China Pharmaceutical University: <PERSON>.", "text": "Plant Material The stem bark of the Khaya se<PERSON>galensis were collected from Xishuangbanna,  Yunnan Province; China in 2009, and were authenticated by Professor Mian Department   of  Pharmacognosy;  China Pharmaceuti cal University. A voucher specimen (No. 2009-KS-LQP) was deposited in the Department of Natural Medicinal Chemistry; China Pharmaceutical University: <PERSON>."}, {"self_ref": "#/texts/164", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 292.3333333333333, "t": 290.0, "r": 542.6666666666666, "b": 57.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1012]}], "orig": "Extraction and Isolation The air-dried stem bark of the K. senegalensis (3.5kg) were exhaustively extracted with 95% ethanol (EtOH) (3hX3) in room temperature. The EtOH extract was concentrated under reduced pressure to obtain residue (314g) which was suspended in H,0 and successively partitioned with petroleum ether (PE), acetyl acetate (EtOAc) respectively. The EtOAc extract (53.5g) was subjected to column chromatography (CC) on silica eluted with eight   fractions (Frs. A-H) Fraction E (8.0g) was separated by silica gel (PE-EtOAc, 5:1, 2:1. 1:1, 1:2) to obtain five subfractions El-ES. E3 was chromatographed over silica gel (CH,Cl2-MeOH, 25:1) to give   five  subfractions; E31E3S, E32 was subjected to Sephadex LH-20 (MeOH) MPLC with a continuous gradient of MeOH-H,O (30:70, 50:50, IOmL/ min)   and prep-HPLC (MeOH-H,O, 40:60 IOmL/min) to afford compound (3mg) E4 was separated on silica gel fractions was separated by Sephadex   LH-20 (MeOH) and further chromatographed over prep-HPLC gel gradiusing", "text": "Extraction and Isolation The air-dried stem bark of the K. senegalensis (3.5kg) were exhaustively extracted with 95% ethanol (EtOH) (3hX3) in room temperature. The EtOH extract was concentrated under reduced pressure to obtain residue (314g) which was suspended in H,0 and successively partitioned with petroleum ether (PE), acetyl acetate (EtOAc) respectively. The EtOAc extract (53.5g) was subjected to column chromatography (CC) on silica eluted with eight   fractions (Frs. A-H) Fraction E (8.0g) was separated by silica gel (PE-EtOAc, 5:1, 2:1. 1:1, 1:2) to obtain five subfractions El-ES. E3 was chromatographed over silica gel (CH,Cl2-MeOH, 25:1) to give   five  subfractions; E31E3S, E32 was subjected to Sephadex LH-20 (MeOH) MPLC with a continuous gradient of MeOH-H,O (30:70, 50:50, IOmL/ min)   and prep-HPLC (MeOH-H,O, 40:60 IOmL/min) to afford compound (3mg) E4 was separated on silica gel fractions was separated by Sephadex   LH-20 (MeOH) and further chromatographed over prep-HPLC gel gradiusing"}, {"self_ref": "#/texts/165", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 5, "bbox": {"l": 27.333333333333332, "t": 768.0, "r": 102.0, "b": 758.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "Vol. 63. No. 4 (2015)", "text": "Vol. 63. No. 4 (2015)"}, {"self_ref": "#/texts/166", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 5, "bbox": {"l": 518.0, "t": 768.0, "r": 532.6666666666666, "b": 759.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "309", "text": "309"}, {"self_ref": "#/texts/167", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 25.666666666666668, "t": 750.6666666666666, "r": 275.3333333333333, "b": 678.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 271]}], "orig": "(4mg) and 6 (4mg) E5 was isolated by CC on ODS (MeOH H,0, 30:70-90:10, v/v) to obtain four subfractions ESa-ESd. ESa   was dissolved with  methanol o crystallize.  The crystal was subsequently subjected to prep-HPLC (acetonitrile-H,O, 25 : 75. to give   compounds 3 (4mg)", "text": "(4mg) and 6 (4mg) E5 was isolated by CC on ODS (MeOH H,0, 30:70-90:10, v/v) to obtain four subfractions ESa-ESd. ESa   was dissolved with  methanol o crystallize.  The crystal was subsequently subjected to prep-HPLC (acetonitrile-H,O, 25 : 75. to give   compounds 3 (4mg)"}, {"self_ref": "#/texts/168", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 25.0, "t": 680.3333333333334, "r": 276.3333333333333, "b": 447.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 790]}], "orig": "Fraction F (8.0g) was chromatographed over silica gel elutwith gradient   of CH,Clz-MeOH (100:1, 40:1, 20:1, Fb was separated by silica tions FbA-FbE. FbE was separated by ODS (MeOH-H,O, 30: 70-100;0) to give six subfractions FbEa-FbEf:. FbEa was subjected to silica FbEa4 was chromatographed on prep-HPLC (MeOH-H,O, 40:60, to  obtain compound 1/   (4mg) FbEa7 subjected to prep-HPLC (MeOH-H,O, 50:50, vv; IOmL/min) to obtain compounds 10 (4mg) and 12 (5mg) Fraction G (4.0g) was separated by silica to give six subfractions GI-G6. G4 was subjected to Sephadex   LH-20 (MeOH) and further   performed on prep-HPLC 7 (IOmg) 8 (6mg) and 9 (9mg) G5 was   chromatographed over 20:1) and further subjected t0 prep-HPLC (acetonitrile H,O 15:85, vlv; IOmL/min) to yield compound 2 (Smg) ing gel gel", "text": "Fraction F (8.0g) was chromatographed over silica gel elutwith gradient   of CH,Clz-MeOH (100:1, 40:1, 20:1, Fb was separated by silica tions FbA-FbE. FbE was separated by ODS (MeOH-H,O, 30: 70-100;0) to give six subfractions FbEa-FbEf:. FbEa was subjected to silica FbEa4 was chromatographed on prep-HPLC (MeOH-H,O, 40:60, to  obtain compound 1/   (4mg) FbEa7 subjected to prep-HPLC (MeOH-H,O, 50:50, vv; IOmL/min) to obtain compounds 10 (4mg) and 12 (5mg) Fraction G (4.0g) was separated by silica to give six subfractions GI-G6. G4 was subjected to Sephadex   LH-20 (MeOH) and further   performed on prep-HPLC 7 (IOmg) 8 (6mg) and 9 (9mg) G5 was   chromatographed over 20:1) and further subjected t0 prep-HPLC (acetonitrile H,O 15:85, vlv; IOmL/min) to yield compound 2 (Smg) ing gel gel"}, {"self_ref": "#/texts/169", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 27.0, "t": 437.0, "r": 276.3333333333333, "b": 379.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 141]}], "orig": "+25.5 (c=0.15 nm; IR (KBr) Ymax: 3450, 2923, 1735, 1700, 1640, 1616, 1401, 1087; 'Hand \"C-NMR data, see Tables and 2; ESI-MS 533 [M-H] [M+Na]", "text": "+25.5 (c=0.15 nm; IR (KBr) Ymax: 3450, 2923, 1735, 1700, 1640, 1616, 1401, 1087; 'Hand \"C-NMR data, see Tables and 2; ESI-MS 533 [M-H] [M+Na]"}, {"self_ref": "#/texts/170", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 26.666666666666668, "t": 368.6666666666667, "r": 276.3333333333333, "b": 320.6666666666667, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 210]}], "orig": "(c=0.06 MeOH); UV (MeOH) /max (loge) 204 (3.68) nm; IR (KBr) 'max: 3442, 2933. 1738, 1703, 1642, 1623, 1387,1068; 'H- and TC-NMR data, see Tables and 2; ESI-MS mlz 571 HR-ESI-MS mlz 566.2591   [M+NH] (Calcd for", "text": "(c=0.06 MeOH); UV (MeOH) /max (loge) 204 (3.68) nm; IR (KBr) 'max: 3442, 2933. 1738, 1703, 1642, 1623, 1387,1068; 'H- and TC-NMR data, see Tables and 2; ESI-MS mlz 571 HR-ESI-MS mlz 566.2591   [M+NH] (Calcd for"}, {"self_ref": "#/texts/171", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 27.666666666666668, "t": 299.3333333333333, "r": 277.0, "b": 251.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 177]}], "orig": "+1.51 (c=0.07 MeOH); UV (MeOH) (logs) 207 (3.79) nm; IR (KBr) Vmax: 3437, 2944, 1727, 1650.1628, 1460, 1378, 1036; H- and \"C-NMR data, see Tables 1 and 2; ESI-MS mlz 568 [M+NH]'", "text": "+1.51 (c=0.07 MeOH); UV (MeOH) (logs) 207 (3.79) nm; IR (KBr) Vmax: 3437, 2944, 1727, 1650.1628, 1460, 1378, 1036; H- and \"C-NMR data, see Tables 1 and 2; ESI-MS mlz 568 [M+NH]'"}, {"self_ref": "#/texts/172", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 27.666666666666668, "t": 229.0, "r": 276.6666666666667, "b": 182.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 183]}], "orig": "(loge) 207 (3.82) nm; IR (KBr) 3445, 2943.1730. 1638. 1618, 1448.1388. 1032: 'H- and \"C-NMR data. see Tables 1 and 2; ESI-MS mlz 608 [M+NH,] HR-ESI-MS mlz 613.1889 [M+Na] (Calcd   for", "text": "(loge) 207 (3.82) nm; IR (KBr) 3445, 2943.1730. 1638. 1618, 1448.1388. 1032: 'H- and \"C-NMR data. see Tables 1 and 2; ESI-MS mlz 608 [M+NH,] HR-ESI-MS mlz 613.1889 [M+Na] (Calcd   for"}, {"self_ref": "#/texts/173", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 28.0, "t": 173.0, "r": 277.0, "b": 113.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 272]}], "orig": "Khaysenelide E (5): White amorphous powder; [a]3s -10.4 (c=0.10 MeOH): UV (MeOH) (loge) 205 (3.54) nm; IR (KBr) Vmax: 3455, 2937. 1736.1638. 1621. 1436. 1383 . 1028: 'H- and FC-NMR data, see Tables 1 and 2; ESI-MS mlz 641 [M+CI] HR-ESI-MS mlz 629.2206   [M+Na]+ (Calcd for", "text": "Khaysenelide E (5): White amorphous powder; [a]3s -10.4 (c=0.10 MeOH): UV (MeOH) (loge) 205 (3.54) nm; IR (KBr) Vmax: 3455, 2937. 1736.1638. 1621. 1436. 1383 . 1028: 'H- and FC-NMR data, see Tables 1 and 2; ESI-MS mlz 641 [M+CI] HR-ESI-MS mlz 629.2206   [M+Na]+ (Calcd for"}, {"self_ref": "#/texts/174", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 28.666666666666668, "t": 103.66666666666663, "r": 277.3333333333333, "b": 44.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 222]}], "orig": "Khaysenelide F (6): White amorphous powder; [a]3s +15.2 (loge) 205 (3.84) nm; IR (KBr) Vmax= 3438. 2923, 1718, 1632. 1619. 1436, 1391,1035; 'H and see Tables and ESI-MS mlz 591 [M-H] HR-ESI-MS mlz 615.2045 [M+Na](Calcd for", "text": "Khaysenelide F (6): White amorphous powder; [a]3s +15.2 (loge) 205 (3.84) nm; IR (KBr) Vmax= 3438. 2923, 1718, 1632. 1619. 1436, 1391,1035; 'H and see Tables and ESI-MS mlz 591 [M-H] HR-ESI-MS mlz 615.2045 [M+Na](Calcd for"}, {"self_ref": "#/texts/175", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 283.6666666666667, "t": 739.0, "r": 534.0, "b": 575.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 537]}], "orig": "Single-Crystal X-Ray Crystallography of 1 The colorless crystals of 1 were obtained from MeOH-H,O. Crystal data were obtained on Bruker Smart CCD with a graphite monostructure was solved by direct methods the SHELXS-97 and   expanded   using   difference Fourier   techniques, refined B=99.500(2)9, Dcalcd =1.369mglmm 4=0.893 mm 9280 reflections measured (9.602202139.94); 4530   unique =0.0162)   which were used in   all  calculations: the final refinement gave R,=0.0321 and wRz =0.0863 (all data); flack parameter=0.03(15) using (Rin", "text": "Single-Crystal X-Ray Crystallography of 1 The colorless crystals of 1 were obtained from MeOH-H,O. Crystal data were obtained on Bruker Smart CCD with a graphite monostructure was solved by direct methods the SHELXS-97 and   expanded   using   difference Fourier   techniques, refined B=99.500(2)9, Dcalcd =1.369mglmm 4=0.893 mm 9280 reflections measured (9.602202139.94); 4530   unique =0.0162)   which were used in   all  calculations: the final refinement gave R,=0.0321 and wRz =0.0863 (all data); flack parameter=0.03(15) using (Rin"}, {"self_ref": "#/texts/176", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 284.20092023383404, "t": 576.3333333333334, "r": 533.3333333333334, "b": 460.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 311]}], "orig": "Single-Crystal X-Ray Crystallography of 3 The X-ray data of 3 were collected at   291(2)K with  CuKa radiation space group PI; unit cell dimensions V= \"=1.045 mm 27440 reflections measured (6.5362202143.948); 10024 unique =0.0246) which were used in all calculations: the final   refinement Crystal Dcalcd (Rint", "text": "Single-Crystal X-Ray Crystallography of 3 The X-ray data of 3 were collected at   291(2)K with  CuKa radiation space group PI; unit cell dimensions V= \"=1.045 mm 27440 reflections measured (6.5362202143.948); 10024 unique =0.0246) which were used in all calculations: the final   refinement Crystal Dcalcd (Rint"}, {"self_ref": "#/texts/177", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 284.6666666666667, "t": 449.0, "r": 534.0, "b": 401.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 227]}], "orig": "NO Production Bioassay The protocol for NO production bioassays was provided in the previously published paper.'8) NMonomethyl-L-arginine was uscd as the positive control. AII the experiments were performed in three replicates_", "text": "NO Production Bioassay The protocol for NO production bioassays was provided in the previously published paper.'8) NMonomethyl-L-arginine was uscd as the positive control. AII the experiments were performed in three replicates_"}, {"self_ref": "#/texts/178", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 284.3333333333333, "t": 391.6666666666667, "r": 534.3333333333334, "b": 297.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 393]}], "orig": "Acknowledgments This research work was financially supported by the National Natural Sciences Foundation of   China   (31470416) the National High   Technology Research and   Development   Program of   China  (863 Program) (2013AA093001), the Program InnovativeResearch Team in and the project funded by the Priority Academic Program Development of Jiangsu Higher Education Institutions (PAPD)", "text": "Acknowledgments This research work was financially supported by the National Natural Sciences Foundation of   China   (31470416) the National High   Technology Research and   Development   Program of   China  (863 Program) (2013AA093001), the Program InnovativeResearch Team in and the project funded by the Priority Academic Program Development of Jiangsu Higher Education Institutions (PAPD)"}, {"self_ref": "#/texts/179", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 286.0, "t": 287.0, "r": 472.0, "b": 265.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 51]}], "orig": "Conflict of Interest The   authors declare interest", "text": "Conflict of Interest The   authors declare interest"}, {"self_ref": "#/texts/180", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 5, "bbox": {"l": 286.0, "t": 251.33333333333337, "r": 334.6666666666667, "b": 242.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 10]}], "orig": "References", "text": "References", "level": 1}, {"self_ref": "#/texts/181", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 290.0, "t": 241.66666666666663, "r": 534.3333333333334, "b": 220.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 98]}], "orig": "1) <PERSON>, \"Progress in the Chemistry of Organic   Natural Springer Vienna, 1984, pp: 1-102", "text": "1) <PERSON>, \"Progress in the Chemistry of Organic   Natural Springer Vienna, 1984, pp: 1-102", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/182", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 290.0, "t": 219.33333333333337, "r": 298.6666666666667, "b": 210.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "2)", "text": "2)", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/183", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 290.0, "t": 209.33333333333337, "r": 534.0, "b": 199.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 66]}], "orig": "3) <PERSON><PERSON><PERSON>, \"The Crown Agents for the Colonies; London, 1937,", "text": "3) <PERSON><PERSON><PERSON>, \"The Crown Agents for the Colonies; London, 1937,", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/184", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 304.0, "t": 188.0, "r": 532.6666666666666, "b": 167.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 111]}], "orig": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> G. N. J Chem. Ecol . 25.", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> G. N. J Chem. Ecol . 25.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/185", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 290.0, "t": 157.33333333333337, "r": 534.3333333333334, "b": 136.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 85]}], "orig": "5) Abdelgaleil $ A <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, 75, 566-572 (2004).", "text": "5) Abdelgaleil $ A <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, 75, 566-572 (2004).", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/186", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 304.6666666666667, "t": 124.66666666666663, "r": 428.0, "b": 114.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 35]}], "orig": "Phytother: Res . 12, 448-450 (1998)", "text": "Phytother: Res . 12, 448-450 (1998)", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/187", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 290.6666666666667, "t": 113.33333333333337, "r": 342.6666666666667, "b": 94.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 13]}], "orig": "7) 284 (1992)", "text": "7) 284 (1992)", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/188", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 290.6666666666667, "t": 93.33333333333337, "r": 533.3333333333334, "b": 73.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 78]}], "orig": "8) <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON> N Phvtochemistry; 47, 14231425 (1998).", "text": "8) <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON> N Phvtochemistry; 47, 14231425 (1998).", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/189", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 290.6666666666667, "t": 72.66666666666663, "r": 534.3333333333334, "b": 51.33333333333337, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 111]}], "orig": "9) <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> A <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> Tetrahed<PERSON> , 41, 6473-6477 (2000) A.", "text": "9) <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> A <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> Tetrahed<PERSON> , 41, 6473-6477 (2000) A.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/190", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 6, "bbox": {"l": 38.0, "t": 771.6666666666666, "r": 53.666666666666664, "b": 762.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "310", "text": "310"}, {"self_ref": "#/texts/191", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 6, "bbox": {"l": 470.0, "t": 770.0, "r": 544.6666666666666, "b": 760.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "Vol. 63. No. 4 (2015)", "text": "Vol. 63. No. 4 (2015)"}, {"self_ref": "#/texts/192", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 38.666666666666664, "t": 754.285041951094, "r": 157.99265734998608, "b": 732.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 44]}], "orig": "10) Yuan T.. C. R.. 669674 (2010) <PERSON>", "text": "10) Yuan T.. C. R.. 669674 (2010) <PERSON>", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/193", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 38.0, "t": 700.9517086177606, "r": 287.0, "b": 669.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 82]}], "orig": "12) <PERSON>. <PERSON>, <PERSON><PERSON>, Wang <PERSON> . 76. 327 333 (2013). <PERSON>", "text": "12) <PERSON>. <PERSON>, <PERSON><PERSON>, Wang <PERSON> . 76. 327 333 (2013). <PERSON>", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/194", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 38.666666666666664, "t": 732.9517086177606, "r": 286.6666666666667, "b": 700.6666666666666, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 74]}], "orig": "11) Yuan €. M. Y; G. IL, Li $. L.. Di Y. T.. <PERSON><PERSON> L<PERSON> (2012) <PERSON>", "text": "11) Yuan €. M. Y; G. IL, Li $. L.. Di Y. T.. <PERSON><PERSON> L<PERSON> (2012) <PERSON>", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/195", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 38.666666666666664, "t": 668.6666666666666, "r": 272.0, "b": 647.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 96]}], "orig": "13) Nakatani <PERSON><PERSON><PERSON> T.. <PERSON> M. J Nat. Prod . 64, 1261-1265 (2001).", "text": "13) Nakatani <PERSON><PERSON><PERSON> T.. <PERSON> M. J Nat. Prod . 64, 1261-1265 (2001).", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/196", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 38.0, "t": 647.6666666666666, "r": 286.6666666666667, "b": 635.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 39]}], "orig": "14) <PERSON> $ M. Yang", "text": "14) <PERSON> $ M. Yang", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/197", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 314.0, "t": 753.6183752844272, "r": 486.6666666666667, "b": 741.3816247155728, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 48]}], "orig": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> Planta Med . 79. 1767-1774 (2013). Kong", "text": "<PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON> Planta Med . 79. 1767-1774 (2013). Kong", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/198", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 296.0, "t": 743.6236910711001, "r": 450.65932401665276, "b": 722.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 55]}], "orig": "15) <PERSON>, <PERSON> , <PERSON><PERSON><PERSON><PERSON>, 294 299 (2009) <PERSON>", "text": "15) <PERSON>, <PERSON> , <PERSON><PERSON><PERSON><PERSON>, 294 299 (2009) <PERSON>", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/199", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 296.6666666666667, "t": 722.0, "r": 543.3333333333334, "b": 700.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 92]}], "orig": "16) <PERSON>, <PERSON>, <PERSON><PERSON>, Phytochemistry; 70. 1305-1308 (2009) <PERSON>.", "text": "16) <PERSON>, <PERSON>, <PERSON><PERSON>, Phytochemistry; 70. 1305-1308 (2009) <PERSON>.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/200", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 296.0, "t": 700.0, "r": 543.6666666666666, "b": 679.3333333333334, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 98]}], "orig": "17) <PERSON>. M. <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> <PERSON><PERSON> <PERSON><PERSON> J <PERSON> Nat. <PERSON>", "text": "17) <PERSON>. M. <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> <PERSON><PERSON> <PERSON><PERSON> J <PERSON> Nat. <PERSON>", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/201", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 296.0, "t": 667.6120396482929, "r": 543.3333333333334, "b": 645.714958048906, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 92]}], "orig": "18) <PERSON> $. M.. M. H. L. Y <PERSON>t . 15. 1572-1575 (2013). <PERSON>", "text": "18) <PERSON> $. M.. M. H. L. Y <PERSON>t . 15. 1572-1575 (2013). <PERSON>", "enumerated": false, "marker": "-"}], "pictures": [{"self_ref": "#/pictures/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/11"}, {"$ref": "#/texts/12"}, {"$ref": "#/texts/13"}, {"$ref": "#/texts/14"}, {"$ref": "#/texts/15"}, {"$ref": "#/texts/16"}, {"$ref": "#/texts/17"}, {"$ref": "#/texts/18"}, {"$ref": "#/texts/19"}, {"$ref": "#/texts/20"}, {"$ref": "#/texts/21"}, {"$ref": "#/texts/22"}, {"$ref": "#/texts/23"}, {"$ref": "#/texts/24"}, {"$ref": "#/texts/25"}, {"$ref": "#/texts/26"}, {"$ref": "#/texts/27"}, {"$ref": "#/texts/28"}, {"$ref": "#/texts/29"}, {"$ref": "#/texts/30"}, {"$ref": "#/texts/31"}, {"$ref": "#/texts/32"}, {"$ref": "#/texts/33"}, {"$ref": "#/texts/34"}, {"$ref": "#/texts/35"}, {"$ref": "#/texts/36"}, {"$ref": "#/texts/37"}, {"$ref": "#/texts/38"}, {"$ref": "#/texts/39"}, {"$ref": "#/texts/40"}, {"$ref": "#/texts/41"}, {"$ref": "#/texts/42"}, {"$ref": "#/texts/43"}, {"$ref": "#/texts/44"}, {"$ref": "#/texts/45"}, {"$ref": "#/texts/46"}, {"$ref": "#/texts/47"}, {"$ref": "#/texts/48"}, {"$ref": "#/texts/49"}, {"$ref": "#/texts/50"}, {"$ref": "#/texts/51"}, {"$ref": "#/texts/52"}, {"$ref": "#/texts/53"}, {"$ref": "#/texts/54"}, {"$ref": "#/texts/55"}, {"$ref": "#/texts/56"}, {"$ref": "#/texts/57"}, {"$ref": "#/texts/58"}, {"$ref": "#/texts/59"}, {"$ref": "#/texts/60"}, {"$ref": "#/texts/61"}, {"$ref": "#/texts/62"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 90.0816879272461, "t": 231.4359130859375, "r": 467.75445556640625, "b": 83.13330078125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/11"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/71"}, {"$ref": "#/texts/72"}, {"$ref": "#/texts/73"}, {"$ref": "#/texts/74"}, {"$ref": "#/texts/75"}, {"$ref": "#/texts/76"}, {"$ref": "#/texts/77"}, {"$ref": "#/texts/78"}, {"$ref": "#/texts/79"}, {"$ref": "#/texts/80"}, {"$ref": "#/texts/81"}, {"$ref": "#/texts/82"}, {"$ref": "#/texts/83"}, {"$ref": "#/texts/84"}, {"$ref": "#/texts/85"}, {"$ref": "#/texts/86"}, {"$ref": "#/texts/87"}, {"$ref": "#/texts/88"}, {"$ref": "#/texts/89"}, {"$ref": "#/texts/90"}, {"$ref": "#/texts/91"}, {"$ref": "#/texts/92"}, {"$ref": "#/texts/93"}, {"$ref": "#/texts/94"}, {"$ref": "#/texts/95"}, {"$ref": "#/texts/96"}, {"$ref": "#/texts/97"}, {"$ref": "#/texts/98"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 2, "bbox": {"l": 122.23912048339844, "t": 348.22613525390625, "r": 462.261474609375, "b": 165.29376220703125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/71"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/2", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/104"}, {"$ref": "#/texts/105"}, {"$ref": "#/texts/106"}, {"$ref": "#/texts/107"}, {"$ref": "#/texts/108"}, {"$ref": "#/texts/109"}, {"$ref": "#/texts/110"}, {"$ref": "#/texts/111"}, {"$ref": "#/texts/112"}, {"$ref": "#/texts/113"}, {"$ref": "#/texts/114"}, {"$ref": "#/texts/115"}, {"$ref": "#/texts/116"}, {"$ref": "#/texts/117"}, {"$ref": "#/texts/118"}, {"$ref": "#/texts/119"}, {"$ref": "#/texts/120"}, {"$ref": "#/texts/121"}, {"$ref": "#/texts/122"}, {"$ref": "#/texts/123"}, {"$ref": "#/texts/124"}, {"$ref": "#/texts/125"}, {"$ref": "#/texts/126"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 3, "bbox": {"l": 38.41836166381836, "t": 747.2228469848633, "r": 263.37921142578125, "b": 603.5718536376953, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/104"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/3", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/130"}, {"$ref": "#/texts/131"}, {"$ref": "#/texts/132"}, {"$ref": "#/texts/133"}, {"$ref": "#/texts/134"}, {"$ref": "#/texts/135"}, {"$ref": "#/texts/136"}, {"$ref": "#/texts/137"}, {"$ref": "#/texts/138"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 3, "bbox": {"l": 297.4427490234375, "t": 749.0465354919434, "r": 521.3675537109375, "b": 597.9652099609375, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/130"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/4", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/144"}, {"$ref": "#/texts/145"}, {"$ref": "#/texts/146"}, {"$ref": "#/texts/147"}, {"$ref": "#/texts/148"}, {"$ref": "#/texts/149"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 4, "bbox": {"l": 52.29179763793945, "t": 753.2914886474609, "r": 270.5820007324219, "b": 605.0852661132812, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/144"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/5", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/150"}, {"$ref": "#/texts/151"}, {"$ref": "#/texts/152"}, {"$ref": "#/texts/153"}, {"$ref": "#/texts/154"}, {"$ref": "#/texts/155"}, {"$ref": "#/texts/156"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 4, "bbox": {"l": 44.5072135925293, "t": 575.8856658935547, "r": 275.8767395019531, "b": 418.0191955566406, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/150"}], "references": [], "footnotes": [], "annotations": []}], "tables": [{"self_ref": "#/tables/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/69"}], "content_layer": "body", "label": "table", "prov": [{"page_no": 2, "bbox": {"l": 39.8158073425293, "t": 738.8854675292969, "r": 544.4288330078125, "b": 370.2229309082031, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/69"}], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 55.333333333333336, "t": 82.66666666666667, "r": 68.0, "b": 90.66666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "No.", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 117.33333333333333, "t": 87.33333333333333, "r": 141.33333333333334, "b": 96.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "in Hz)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 187.33333333333334, "t": 87.33333333333333, "r": 219.66666666666666, "b": 96.66666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "(Jin Hz)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 346.6666666666667, "t": 87.33333333333333, "r": 370.0, "b": 96.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "in Hz)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 413.3333333333333, "t": 87.33333333333333, "r": 446.0, "b": 96.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "UJin Hz)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 489.3333333333333, "t": 86.66666666666667, "r": 521.6666666666666, "b": 96.66666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "(Jin Hz)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 180.0, "t": 103.33333333333333, "r": 226.66666666666666, "b": 112.66666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "3.02, d (10.0)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 249.33333333333334, "t": 103.33333333333333, "r": 308.0, "b": 113.33333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "4.29,dd (9.5,6.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 330.6666666666667, "t": 103.33333333333333, "r": 378.0, "b": 112.66666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "4.38. d (10.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 474.6666666666667, "t": 103.33333333333333, "r": 536.6666666666666, "b": 112.66666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "4.35, dd (9.5.6.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 55.666666666666664, "t": 115.66666666666667, "r": 60.333333333333336, "b": 123.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 419.0, "t": 113.66666666666667, "r": 440.6666666666667, "b": 123.66666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "3.319", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 55.666666666666664, "t": 127.0, "r": 61.0, "b": 134.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 103.33333333333333, "t": 126.0, "r": 146.66666666666666, "b": 146.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.73, brs 4,40. d (5.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 187.0, "t": 124.66666666666667, "r": 219.33333333333334, "b": 135.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "2.73, brs", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 323.3333333333333, "t": 125.33333333333333, "r": 385.3333333333333, "b": 146.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "3.05, d (8.5) 4.13, dd (9.0,5.0)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.0, "t": 126.0, "r": 451.3333333333333, "b": 146.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "2.94, d (6.5) 4.10, 1 (5.0)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 56.333333333333336, "t": 138.33333333333334, "r": 61.0, "b": 145.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 248.0, "t": 136.66666666666666, "r": 310.0, "b": 147.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "4,.04, dd (7.5,4.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 484.6666666666667, "t": 136.66666666666666, "r": 526.6666666666666, "b": 147.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "4.09. 1 (5.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 92.66666666666667, "t": 148.0, "r": 158.0, "b": 158.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.75. dd (14.0, 5.0)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 190.0, "t": 148.66666666666666, "r": 216.66666666666666, "b": 157.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1.75. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 265.3333333333333, "t": 148.0, "r": 292.0, "b": 156.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "2.05. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 332.6666666666667, "t": 148.0, "r": 376.0, "b": 157.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "2.30, d (8.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.0, "t": 148.0, "r": 451.3333333333333, "b": 157.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "2.15. d (9.0)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 112.0, "t": 160.0, "r": 138.66666666666666, "b": 168.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.67 m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 266.0, "t": 159.33333333333334, "r": 292.0, "b": 168.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.69, m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 341.3333333333333, "t": 159.33333333333334, "r": 367.3333333333333, "b": 168.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "1.78, m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 492.0, "t": 159.33333333333334, "r": 518.6666666666666, "b": 168.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "1.55, m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 51.333333333333336, "t": 170.66666666666666, "r": 65.33333333333333, "b": 180.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "11p", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 190.0, "t": 170.66666666666666, "r": 216.66666666666666, "b": 179.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1.33. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 265.3333333333333, "t": 170.66666666666666, "r": 292.0, "b": 179.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.85, mn", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 341.3333333333333, "t": 170.66666666666666, "r": 367.3333333333333, "b": 179.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "1.99, m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 416.6666666666667, "t": 170.66666666666666, "r": 443.3333333333333, "b": 179.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "1.67. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 492.0, "t": 170.0, "r": 518.6666666666666, "b": 179.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "1.65. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 51.333333333333336, "t": 182.0, "r": 66.0, "b": 190.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "12a", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 112.0, "t": 182.0, "r": 138.66666666666666, "b": 190.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.20. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.0, "t": 183.66666666666666, "r": 216.33333333333334, "b": 190.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "M", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 265.3333333333333, "t": 182.0, "r": 292.0, "b": 190.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.12, m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 341.3333333333333, "t": 181.33333333333334, "r": 367.3333333333333, "b": 190.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "1.21, m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 416.0, "t": 181.33333333333334, "r": 442.6666666666667, "b": 190.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.71. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 491.3333333333333, "t": 181.33333333333334, "r": 518.6666666666666, "b": 190.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.67, m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 172.66666666666666, "t": 192.66666666666666, "r": 234.0, "b": 213.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1.53. m 11.69. dd (6.5.2.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 265.3333333333333, "t": 192.66666666666666, "r": 292.0, "b": 202.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.85. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.6666666666667, "t": 192.66666666666666, "r": 367.3333333333333, "b": 202.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "1.72, m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 416.6666666666667, "t": 192.66666666666666, "r": 443.3333333333333, "b": 201.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "1.91. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 492.0, "t": 192.66666666666666, "r": 518.6666666666666, "b": 202.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "1.88, m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 51.333333333333336, "t": 204.66666666666666, "r": 61.333333333333336, "b": 212.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "14", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 94.66666666666667, "t": 204.0, "r": 156.0, "b": 213.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.69, dd (6.5.2.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 51.333333333333336, "t": 215.33333333333334, "r": 64.66666666666667, "b": 224.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "15a", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 111.33333333333333, "t": 215.33333333333334, "r": 138.66666666666666, "b": 224.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.72. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 190.0, "t": 215.33333333333334, "r": 216.66666666666666, "b": 224.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "2.72. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 255.33333333333334, "t": 214.0, "r": 302.6666666666667, "b": 224.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "2,87.d (18.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 330.6666666666667, "t": 214.66666666666666, "r": 378.0, "b": 224.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "2.93. d (19.0)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 406.0, "t": 214.66666666666666, "r": 454.0, "b": 224.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "2.84, d (18.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 482.0, "t": 214.66666666666666, "r": 529.3333333333334, "b": 224.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "2.83, d (18.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 51.333333333333336, "t": 226.66666666666666, "r": 65.33333333333333, "b": 235.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ISb", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 111.33333333333333, "t": 226.0, "r": 138.66666666666666, "b": 235.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.70. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 189.33333333333334, "t": 226.0, "r": 216.66666666666666, "b": 235.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "2.70. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 255.33333333333334, "t": 226.0, "r": 302.6666666666667, "b": 235.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "2.67.d (18.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 330.6666666666667, "t": 226.0, "r": 378.0, "b": 235.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "2.73. d (19.0)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 406.0, "t": 226.0, "r": 454.0, "b": 236.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "2.68, d (18.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 481.3333333333333, "t": 226.0, "r": 529.3333333333334, "b": 236.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "2.64, d (18.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 51.333333333333336, "t": 238.0, "r": 61.333333333333336, "b": 246.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "17", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 113.33333333333333, "t": 237.33333333333334, "r": 137.33333333333334, "b": 246.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "5.22, $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 191.33333333333334, "t": 237.33333333333334, "r": 209.33333333333334, "b": 246.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "5.22.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 263.3333333333333, "t": 237.33333333333334, "r": 294.6666666666667, "b": 246.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "5.31. brs", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 342.6666666666667, "t": 237.33333333333334, "r": 360.0, "b": 246.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "5.09.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 418.0, "t": 237.33333333333334, "r": 436.0, "b": 246.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "5.42,", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 493.3333333333333, "t": 237.33333333333334, "r": 510.6666666666667, "b": 246.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "5.43.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 51.333333333333336, "t": 248.66666666666666, "r": 61.333333333333336, "b": 257.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "18", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 191.33333333333334, "t": 248.66666666666666, "r": 215.33333333333334, "b": 258.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "0.88, $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 342.6666666666667, "t": 248.66666666666666, "r": 366.6666666666667, "b": 258.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "1.05, $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 418.6666666666667, "t": 248.66666666666666, "r": 436.6666666666667, "b": 258.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "1.01,", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 494.0, "t": 248.66666666666666, "r": 511.3333333333333, "b": 257.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "1.00,", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 51.333333333333336, "t": 260.0, "r": 61.333333333333336, "b": 268.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "19", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 113.33333333333333, "t": 260.0, "r": 137.33333333333334, "b": 269.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.24, $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 191.33333333333334, "t": 260.0, "r": 210.0, "b": 269.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1.25.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 343.3333333333333, "t": 260.0, "r": 366.0, "b": 268.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "1.33. $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 264.6666666666667, "t": 282.0, "r": 283.3333333333333, "b": 291.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "6.22.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 50.666666666666664, "t": 294.0, "r": 60.666666666666664, "b": 302.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "23", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 110.66666666666667, "t": 292.6666666666667, "r": 140.0, "b": 302.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "6.12. s\"", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 191.33333333333334, "t": 293.3333333333333, "r": 216.0, "b": 302.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "6.12, $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 418.0, "t": 293.3333333333333, "r": 442.0, "b": 302.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "6.12. $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 51.333333333333336, "t": 304.6666666666667, "r": 61.333333333333336, "b": 313.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "28", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 113.33333333333333, "t": 304.6666666666667, "r": 137.33333333333334, "b": 313.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.16. s", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 192.0, "t": 304.6666666666667, "r": 216.0, "b": 324.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1.16. $ 1.07, s", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 266.6666666666667, "t": 304.6666666666667, "r": 291.3333333333333, "b": 313.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.94. s", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 330.3333333333333, "t": 304.6666666666667, "r": 378.0, "b": 324.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.91. $ 2.06. d (12.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 418.0, "t": 304.0, "r": 442.0, "b": 313.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "1.01. $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 51.333333333333336, "t": 316.0, "r": 66.0, "b": 336.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "29a 29b", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 114.0, "t": 316.0, "r": 131.33333333333334, "b": 324.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.07,", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 255.33333333333334, "t": 315.3333333333333, "r": 302.6666666666667, "b": 336.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.19. d (12.0) 1.71, d (12.0)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 406.6666666666667, "t": 315.3333333333333, "r": 453.3333333333333, "b": 336.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "1.68, d (12.0) 2.10. d (I2.0)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 482.0, "t": 315.3333333333333, "r": 530.0, "b": 336.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "1.68. d (11.5) 2.11, d (11.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 101.66666666666667, "t": 348.3333333333333, "r": 149.33333333333334, "b": 369.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.45.d (14.5) 3.69. $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 179.33333333333334, "t": 348.6666666666667, "r": 228.0, "b": 369.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "2.46, d (14.5) 3.69. $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 46.666666666666664, "t": 360.6666666666667, "r": 76.66666666666667, "b": 380.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OMe-7 23-OMe", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 266.0, "t": 359.6666666666667, "r": 291.3333333333333, "b": 369.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "3.62, $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 342.0, "t": 360.0, "r": 366.6666666666667, "b": 369.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "3.59, $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 417.3333333333333, "t": 360.0, "r": 442.0, "b": 380.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "3.60, $ 3.48.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 494.6666666666667, "t": 360.0, "r": 516.6666666666666, "b": 368.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "3.62.s", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 47.333333333333336, "t": 383.3333333333333, "r": 68.0, "b": 402.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OH-6 OH-8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 104.0, "t": 382.6666666666667, "r": 148.0, "b": 392.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "5.40. d (5.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 182.0, "t": 382.6666666666667, "r": 226.0, "b": 402.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "5.41, d (55) 4.81. $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 410.0, "t": 382.0, "r": 450.6666666666667, "b": 402.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "5.48,d (4.5) 5.25", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 490.0, "t": 382.0, "r": 521.3333333333334, "b": 391.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "5.51, brs", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 266.6666666666667, "t": 393.3333333333333, "r": 291.3333333333333, "b": 402.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "4.93. $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 493.3333333333333, "t": 393.3333333333333, "r": 510.6666666666667, "b": 402.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "5.20", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 270.6666666666667, "t": 404.6666666666667, "r": 287.3333333333333, "b": 413.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "4.43", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 47.333333333333336, "t": 416.0, "r": 70.66666666666667, "b": 436.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OAc-1 OH-3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 342.0, "t": 416.0, "r": 366.6666666666667, "b": 425.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "2.01. s", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 419.3333333333333, "t": 416.0, "r": 442.6666666666667, "b": 424.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "1.96. $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 494.6666666666667, "t": 416.0, "r": 518.0, "b": 424.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "1.96. $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 257.3333333333333, "t": 427.3333333333333, "r": 301.3333333333333, "b": 437.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "3.54. d (8.5)", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 21, "num_cols": 7, "grid": [[{"bbox": {"l": 55.333333333333336, "t": 82.66666666666667, "r": 68.0, "b": 90.66666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "No.", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 117.33333333333333, "t": 87.33333333333333, "r": 141.33333333333334, "b": 96.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "in Hz)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 187.33333333333334, "t": 87.33333333333333, "r": 219.66666666666666, "b": 96.66666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "(Jin Hz)", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 346.6666666666667, "t": 87.33333333333333, "r": 370.0, "b": 96.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "in Hz)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 413.3333333333333, "t": 87.33333333333333, "r": 446.0, "b": 96.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "UJin Hz)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 489.3333333333333, "t": 86.66666666666667, "r": 521.6666666666666, "b": 96.66666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "(Jin Hz)", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 55.666666666666664, "t": 115.66666666666667, "r": 60.333333333333336, "b": 123.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 180.0, "t": 103.33333333333333, "r": 226.66666666666666, "b": 112.66666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "3.02, d (10.0)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 249.33333333333334, "t": 103.33333333333333, "r": 308.0, "b": 113.33333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "4.29,dd (9.5,6.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 330.6666666666667, "t": 103.33333333333333, "r": 378.0, "b": 112.66666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "4.38. d (10.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 419.0, "t": 113.66666666666667, "r": 440.6666666666667, "b": 123.66666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "3.319", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 474.6666666666667, "t": 103.33333333333333, "r": 536.6666666666666, "b": 112.66666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "4.35, dd (9.5.6.5)", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 55.666666666666664, "t": 127.0, "r": 61.0, "b": 134.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 103.33333333333333, "t": 126.0, "r": 146.66666666666666, "b": 146.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.73, brs 4,40. d (5.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 187.0, "t": 124.66666666666667, "r": 219.33333333333334, "b": 135.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "2.73, brs", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 248.0, "t": 136.66666666666666, "r": 310.0, "b": 147.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "4,.04, dd (7.5,4.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 323.3333333333333, "t": 125.33333333333333, "r": 385.3333333333333, "b": 146.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "3.05, d (8.5) 4.13, dd (9.0,5.0)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.0, "t": 126.0, "r": 451.3333333333333, "b": 146.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "2.94, d (6.5) 4.10, 1 (5.0)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 484.6666666666667, "t": 136.66666666666666, "r": 526.6666666666666, "b": 147.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "4.09. 1 (5.5)", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 56.333333333333336, "t": 138.33333333333334, "r": 61.0, "b": 145.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "6", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 265.3333333333333, "t": 148.0, "r": 292.0, "b": 156.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "2.05. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 332.6666666666667, "t": 148.0, "r": 376.0, "b": 157.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "2.30, d (8.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.0, "t": 148.0, "r": 451.3333333333333, "b": 157.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "2.15. d (9.0)", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 92.66666666666667, "t": 148.0, "r": 158.0, "b": 158.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.75. dd (14.0, 5.0)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 190.0, "t": 148.66666666666666, "r": 216.66666666666666, "b": 157.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1.75. m", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 341.3333333333333, "t": 159.33333333333334, "r": 367.3333333333333, "b": 168.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "1.78, m", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 112.0, "t": 160.0, "r": 138.66666666666666, "b": 168.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.67 m", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 266.0, "t": 159.33333333333334, "r": 292.0, "b": 168.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.69, m", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 492.0, "t": 159.33333333333334, "r": 518.6666666666666, "b": 168.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "1.55, m", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 51.333333333333336, "t": 170.66666666666666, "r": 65.33333333333333, "b": 180.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "11p", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 190.0, "t": 170.66666666666666, "r": 216.66666666666666, "b": 179.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1.33. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 265.3333333333333, "t": 170.66666666666666, "r": 292.0, "b": 179.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.85, mn", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 341.3333333333333, "t": 170.66666666666666, "r": 367.3333333333333, "b": 179.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "1.99, m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 416.6666666666667, "t": 170.66666666666666, "r": 443.3333333333333, "b": 179.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "1.67. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 492.0, "t": 170.0, "r": 518.6666666666666, "b": 179.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "1.65. m", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 51.333333333333336, "t": 182.0, "r": 66.0, "b": 190.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "12a", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 112.0, "t": 182.0, "r": 138.66666666666666, "b": 190.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.20. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.0, "t": 183.66666666666666, "r": 216.33333333333334, "b": 190.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "M", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 265.3333333333333, "t": 182.0, "r": 292.0, "b": 190.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.12, m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 341.3333333333333, "t": 181.33333333333334, "r": 367.3333333333333, "b": 190.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "1.21, m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 416.0, "t": 181.33333333333334, "r": 442.6666666666667, "b": 190.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.71. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 491.3333333333333, "t": 181.33333333333334, "r": 518.6666666666666, "b": 190.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.67, m", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 51.333333333333336, "t": 204.66666666666666, "r": 61.333333333333336, "b": 212.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "14", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 94.66666666666667, "t": 204.0, "r": 156.0, "b": 213.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.69, dd (6.5.2.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 172.66666666666666, "t": 192.66666666666666, "r": 234.0, "b": 213.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1.53. m 11.69. dd (6.5.2.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 265.3333333333333, "t": 192.66666666666666, "r": 292.0, "b": 202.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.85. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.6666666666667, "t": 192.66666666666666, "r": 367.3333333333333, "b": 202.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "1.72, m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 416.6666666666667, "t": 192.66666666666666, "r": 443.3333333333333, "b": 201.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "1.91. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 492.0, "t": 192.66666666666666, "r": 518.6666666666666, "b": 202.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "1.88, m", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 51.333333333333336, "t": 215.33333333333334, "r": 64.66666666666667, "b": 224.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "15a", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 111.33333333333333, "t": 215.33333333333334, "r": 138.66666666666666, "b": 224.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.72. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 190.0, "t": 215.33333333333334, "r": 216.66666666666666, "b": 224.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "2.72. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 255.33333333333334, "t": 214.0, "r": 302.6666666666667, "b": 224.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "2,87.d (18.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 330.6666666666667, "t": 214.66666666666666, "r": 378.0, "b": 224.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "2.93. d (19.0)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 406.0, "t": 214.66666666666666, "r": 454.0, "b": 224.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "2.84, d (18.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 482.0, "t": 214.66666666666666, "r": 529.3333333333334, "b": 224.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "2.83, d (18.5)", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 51.333333333333336, "t": 226.66666666666666, "r": 65.33333333333333, "b": 235.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ISb", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 111.33333333333333, "t": 226.0, "r": 138.66666666666666, "b": 235.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.70. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 189.33333333333334, "t": 226.0, "r": 216.66666666666666, "b": 235.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "2.70. m", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 255.33333333333334, "t": 226.0, "r": 302.6666666666667, "b": 235.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "2.67.d (18.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 330.6666666666667, "t": 226.0, "r": 378.0, "b": 235.33333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "2.73. d (19.0)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 406.0, "t": 226.0, "r": 454.0, "b": 236.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "2.68, d (18.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 481.3333333333333, "t": 226.0, "r": 529.3333333333334, "b": 236.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "2.64, d (18.5)", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 51.333333333333336, "t": 238.0, "r": 61.333333333333336, "b": 246.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "17", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 113.33333333333333, "t": 237.33333333333334, "r": 137.33333333333334, "b": 246.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "5.22, $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 191.33333333333334, "t": 237.33333333333334, "r": 209.33333333333334, "b": 246.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "5.22.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 263.3333333333333, "t": 237.33333333333334, "r": 294.6666666666667, "b": 246.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "5.31. brs", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 342.6666666666667, "t": 237.33333333333334, "r": 360.0, "b": 246.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "5.09.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 418.0, "t": 237.33333333333334, "r": 436.0, "b": 246.66666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "5.42,", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 493.3333333333333, "t": 237.33333333333334, "r": 510.6666666666667, "b": 246.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "5.43.", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 51.333333333333336, "t": 248.66666666666666, "r": 61.333333333333336, "b": 257.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "18", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 191.33333333333334, "t": 248.66666666666666, "r": 215.33333333333334, "b": 258.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "0.88, $", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 342.6666666666667, "t": 248.66666666666666, "r": 366.6666666666667, "b": 258.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "1.05, $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 418.6666666666667, "t": 248.66666666666666, "r": 436.6666666666667, "b": 258.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "1.01,", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 494.0, "t": 248.66666666666666, "r": 511.3333333333333, "b": 257.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "1.00,", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 51.333333333333336, "t": 260.0, "r": 61.333333333333336, "b": 268.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "19", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 113.33333333333333, "t": 260.0, "r": 137.33333333333334, "b": 269.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.24, $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 191.33333333333334, "t": 260.0, "r": 210.0, "b": 269.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1.25.", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 343.3333333333333, "t": 260.0, "r": 366.0, "b": 268.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "1.33. $", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 50.666666666666664, "t": 294.0, "r": 60.666666666666664, "b": 302.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "23", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 110.66666666666667, "t": 292.6666666666667, "r": 140.0, "b": 302.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "6.12. s\"", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 191.33333333333334, "t": 293.3333333333333, "r": 216.0, "b": 302.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "6.12, $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 264.6666666666667, "t": 282.0, "r": 283.3333333333333, "b": 291.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "6.22.", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 418.0, "t": 293.3333333333333, "r": 442.0, "b": 302.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "6.12. $", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 51.333333333333336, "t": 304.6666666666667, "r": 61.333333333333336, "b": 313.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "28", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 113.33333333333333, "t": 304.6666666666667, "r": 137.33333333333334, "b": 313.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.16. s", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 192.0, "t": 304.6666666666667, "r": 216.0, "b": 324.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1.16. $ 1.07, s", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 266.6666666666667, "t": 304.6666666666667, "r": 291.3333333333333, "b": 313.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.94. s", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 330.3333333333333, "t": 304.6666666666667, "r": 378.0, "b": 324.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.91. $ 2.06. d (12.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 418.0, "t": 304.0, "r": 442.0, "b": 313.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "1.01. $", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 51.333333333333336, "t": 316.0, "r": 66.0, "b": 336.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "29a 29b", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 114.0, "t": 316.0, "r": 131.33333333333334, "b": 324.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.07,", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 255.33333333333334, "t": 315.3333333333333, "r": 302.6666666666667, "b": 336.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "1.19. d (12.0) 1.71, d (12.0)", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 406.6666666666667, "t": 315.3333333333333, "r": 453.3333333333333, "b": 336.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "1.68, d (12.0) 2.10. d (I2.0)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 482.0, "t": 315.3333333333333, "r": 530.0, "b": 336.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "1.68. d (11.5) 2.11, d (11.5)", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 46.666666666666664, "t": 360.6666666666667, "r": 76.66666666666667, "b": 380.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OMe-7 23-OMe", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 101.66666666666667, "t": 348.3333333333333, "r": 149.33333333333334, "b": 369.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.45.d (14.5) 3.69. $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 179.33333333333334, "t": 348.6666666666667, "r": 228.0, "b": 369.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "2.46, d (14.5) 3.69. $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 266.0, "t": 359.6666666666667, "r": 291.3333333333333, "b": 369.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "3.62, $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 342.0, "t": 360.0, "r": 366.6666666666667, "b": 369.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "3.59, $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 417.3333333333333, "t": 360.0, "r": 442.0, "b": 380.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "3.60, $ 3.48.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 494.6666666666667, "t": 360.0, "r": 516.6666666666666, "b": 368.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "3.62.s", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 47.333333333333336, "t": 383.3333333333333, "r": 68.0, "b": 402.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OH-6 OH-8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 104.0, "t": 382.6666666666667, "r": 148.0, "b": 392.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "5.40. d (5.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 182.0, "t": 382.6666666666667, "r": 226.0, "b": 402.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "5.41, d (55) 4.81. $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 266.6666666666667, "t": 393.3333333333333, "r": 291.3333333333333, "b": 402.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "4.93. $", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 410.0, "t": 382.0, "r": 450.6666666666667, "b": 402.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "5.48,d (4.5) 5.25", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 490.0, "t": 382.0, "r": 521.3333333333334, "b": 391.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "5.51, brs", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 270.6666666666667, "t": 404.6666666666667, "r": 287.3333333333333, "b": 413.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "4.43", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 493.3333333333333, "t": 393.3333333333333, "r": 510.6666666666667, "b": 402.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "5.20", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 47.333333333333336, "t": 416.0, "r": 70.66666666666667, "b": 436.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OAc-1 OH-3", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 257.3333333333333, "t": 427.3333333333333, "r": 301.3333333333333, "b": 437.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "3.54. d (8.5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 342.0, "t": 416.0, "r": 366.6666666666667, "b": 425.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "2.01. s", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 419.3333333333333, "t": 416.0, "r": 442.6666666666667, "b": 424.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "1.96. $", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 494.6666666666667, "t": 416.0, "r": 518.0, "b": 424.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "1.96. $", "column_header": false, "row_header": false, "row_section": false}]]}}, {"self_ref": "#/tables/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/139"}], "content_layer": "body", "label": "table", "prov": [{"page_no": 3, "bbox": {"l": 286.24859619140625, "t": 539.0414428710938, "r": 532.6563110351562, "b": 184.81396484375, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/139"}], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 302.0, "t": 272.6666666666667, "r": 316.0, "b": 280.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "No.", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 338.0, "t": 272.6666666666667, "r": 356.6666666666667, "b": 280.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "KS-1", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 372.0, "t": 272.0, "r": 390.6666666666667, "b": 280.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "KS-2", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 405.3333333333333, "t": 272.0, "r": 424.0, "b": 280.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "KS-3", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 438.6666666666667, "t": 272.0, "r": 458.0, "b": 280.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "KS-4", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 472.6666666666667, "t": 272.0, "r": 492.0, "b": 280.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "KS-5", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 506.0, "t": 271.3333333333333, "r": 525.3333333333334, "b": 280.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "KS-6", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 335.3333333333333, "t": 288.0, "r": 356.0, "b": 296.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "210.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 370.6666666666667, "t": 288.0, "r": 391.3333333333333, "b": 296.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "210.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 406.6666666666667, "t": 288.0, "r": 422.6666666666667, "b": 296.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "83.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 440.6666666666667, "t": 288.0, "r": 456.6666666666667, "b": 296.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "89.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.0, "t": 288.0, "r": 492.0, "b": 296.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "90.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.0, "t": 287.3333333333333, "r": 523.3333333333334, "b": 296.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "90.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 299.3333333333333, "r": 356.0, "b": 308.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "53.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 375.3333333333333, "t": 299.3333333333333, "r": 391.3333333333333, "b": 308.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "53,4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 299.3333333333333, "r": 423.3333333333333, "b": 308.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "72.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 298.6666666666667, "r": 492.6666666666667, "b": 307.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "71.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 371.3333333333333, "t": 310.6666666666667, "r": 392.0, "b": 319.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "2145", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 310.0, "r": 423.3333333333333, "b": 318.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "77.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 436.6666666666667, "t": 310.0, "r": 456.6666666666667, "b": 318.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "204.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 310.0, "r": 492.6666666666667, "b": 318.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "77.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.0, "t": 310.0, "r": 524.0, "b": 318.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "77.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 321.3333333333333, "r": 356.0, "b": 330.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "49.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 375.3333333333333, "t": 322.0, "r": 392.0, "b": 330.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "49.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 321.3333333333333, "r": 423.3333333333333, "b": 330.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "42.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 440.6666666666667, "t": 321.3333333333333, "r": 456.6666666666667, "b": 330.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "50.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.0, "t": 321.3333333333333, "r": 492.0, "b": 330.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "43.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.0, "t": 321.3333333333333, "r": 524.6666666666666, "b": 330.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "43.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 309.0, "t": 333.6666666666667, "r": 313.6666666666667, "b": 341.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 332.6666666666667, "r": 356.0, "b": 341.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "45.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 375.3333333333333, "t": 332.6666666666667, "r": 391.3333333333333, "b": 341.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "45.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 332.6666666666667, "r": 423.3333333333333, "b": 341.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "40.3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 440.6666666666667, "t": 332.6666666666667, "r": 457.3333333333333, "b": 341.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "39.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 332.6666666666667, "r": 492.6666666666667, "b": 342.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "38.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.0, "t": 332.6666666666667, "r": 523.3333333333334, "b": 340.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "39.1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 344.0, "r": 356.0, "b": 352.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "69.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 375.3333333333333, "t": 344.0, "r": 392.0, "b": 352.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "69.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 344.0, "r": 423.3333333333333, "b": 352.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "70.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 440.6666666666667, "t": 344.0, "r": 456.6666666666667, "b": 352.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "69.3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 343.3333333333333, "r": 492.6666666666667, "b": 352.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "71.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.0, "t": 343.3333333333333, "r": 524.0, "b": 352.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "70.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 336.6666666666667, "t": 355.3333333333333, "r": 356.6666666666667, "b": 364.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "175.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 372.0, "t": 354.6666666666667, "r": 392.0, "b": 363.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "175.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 403.3333333333333, "t": 355.3333333333333, "r": 423.3333333333333, "b": 363.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "174.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 437.3333333333333, "t": 355.3333333333333, "r": 456.6666666666667, "b": 363.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "172.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 472.6666666666667, "t": 354.6666666666667, "r": 492.6666666666667, "b": 363.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "174.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 504.0, "t": 354.6666666666667, "r": 524.6666666666666, "b": 363.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "174.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 366.6666666666667, "r": 356.6666666666667, "b": 374.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "71.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 366.0, "r": 392.0, "b": 374.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "71.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 366.0, "r": 423.3333333333333, "b": 374.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "86.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 440.6666666666667, "t": 366.0, "r": 456.6666666666667, "b": 374.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "86.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 366.0, "r": 492.6666666666667, "b": 374.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "85.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.0, "t": 366.0, "r": 524.6666666666666, "b": 374.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "85.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 377.3333333333333, "r": 356.6666666666667, "b": 385.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "60.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 377.3333333333333, "r": 392.0, "b": 385.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "59.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 377.3333333333333, "r": 423.3333333333333, "b": 385.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "54.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 440.6666666666667, "t": 377.3333333333333, "r": 456.6666666666667, "b": 385.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "53.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 377.3333333333333, "r": 492.6666666666667, "b": 385.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "54.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 376.6666666666667, "r": 524.6666666666666, "b": 385.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "54.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 304.6666666666667, "t": 388.6666666666667, "r": 314.6666666666667, "b": 396.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "10", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 388.0, "r": 356.6666666666667, "b": 396.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "48.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 388.0, "r": 392.0, "b": 396.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "48.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 388.0, "r": 423.3333333333333, "b": 396.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "58.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 441.3333333333333, "t": 388.0, "r": 457.3333333333333, "b": 396.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "60.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 388.0, "r": 492.6666666666667, "b": 396.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "60.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 388.0, "r": 524.6666666666666, "b": 396.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "60.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 399.3333333333333, "r": 356.0, "b": 408.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "22.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 399.3333333333333, "r": 392.0, "b": 407.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "22.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.0, "t": 399.3333333333333, "r": 423.3333333333333, "b": 407.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "16.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 477.3333333333333, "t": 398.6666666666667, "r": 492.6666666666667, "b": 407.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "15.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 398.6666666666667, "r": 524.6666666666666, "b": 407.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "15.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 304.6666666666667, "t": 410.6666666666667, "r": 314.6666666666667, "b": 418.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "12", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 410.6666666666667, "r": 356.6666666666667, "b": 418.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "34.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 410.0, "r": 392.0, "b": 418.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "34.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 410.0, "r": 423.3333333333333, "b": 418.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "25.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 440.6666666666667, "t": 410.0, "r": 457.3333333333333, "b": 418.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "25.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 410.0, "r": 492.6666666666667, "b": 418.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "25.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 409.3333333333333, "r": 524.0, "b": 418.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "25.1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 304.6666666666667, "t": 421.3333333333333, "r": 314.0, "b": 430.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "13", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 421.3333333333333, "r": 356.6666666666667, "b": 430.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "34.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 421.3333333333333, "r": 392.0, "b": 430.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "34.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 421.3333333333333, "r": 423.3333333333333, "b": 430.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "37.3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 440.6666666666667, "t": 421.3333333333333, "r": 456.6666666666667, "b": 430.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "37.1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 420.6666666666667, "r": 492.6666666666667, "b": 429.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "37.3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 420.6666666666667, "r": 524.6666666666666, "b": 429.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "37.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 305.3333333333333, "t": 432.6666666666667, "r": 314.6666666666667, "b": 440.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "14", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.6666666666667, "t": 432.6666666666667, "r": 356.6666666666667, "b": 440.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "50.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 432.6666666666667, "r": 392.6666666666667, "b": 440.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "50.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.0, "t": 432.0, "r": 423.3333333333333, "b": 440.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "80.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 441.3333333333333, "t": 432.0, "r": 457.3333333333333, "b": 441.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "83.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 432.0, "r": 493.3333333333333, "b": 440.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "80.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 432.0, "r": 524.6666666666666, "b": 440.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "80.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 305.3333333333333, "t": 444.0, "r": 314.6666666666667, "b": 452.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "15", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.6666666666667, "t": 443.3333333333333, "r": 356.0, "b": 452.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "26.1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 443.3333333333333, "r": 391.3333333333333, "b": 452.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "26.1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 443.3333333333333, "r": 424.0, "b": 452.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "31.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 441.3333333333333, "t": 443.3333333333333, "r": 458.0, "b": 451.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "32.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 442.6666666666667, "r": 493.3333333333333, "b": 451.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "31.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 442.6666666666667, "r": 524.6666666666666, "b": 451.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "31.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 305.3333333333333, "t": 454.6666666666667, "r": 315.3333333333333, "b": 463.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "16", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 336.6666666666667, "t": 454.6666666666667, "r": 356.6666666666667, "b": 463.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "169.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 372.6666666666667, "t": 454.6666666666667, "r": 390.6666666666667, "b": 462.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "169.1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 404.0, "t": 454.6666666666667, "r": 424.0, "b": 462.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "169.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 438.0, "t": 454.6666666666667, "r": 457.3333333333333, "b": 462.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "168.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 473.3333333333333, "t": 454.0, "r": 493.3333333333333, "b": 462.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "169,4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 505.3333333333333, "t": 454.0, "r": 524.6666666666666, "b": 462.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "169,4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 305.3333333333333, "t": 466.0, "r": 314.6666666666667, "b": 474.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "17", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.6666666666667, "t": 466.0, "r": 356.6666666666667, "b": 474.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "75.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.6666666666667, "t": 466.0, "r": 392.6666666666667, "b": 474.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "75.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.0, "t": 465.3333333333333, "r": 424.0, "b": 474.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "80.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 441.3333333333333, "t": 465.3333333333333, "r": 458.0, "b": 474.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "80.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 477.3333333333333, "t": 465.3333333333333, "r": 493.3333333333333, "b": 474.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "78.3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 465.3333333333333, "r": 524.6666666666666, "b": 474.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "78.3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 305.3333333333333, "t": 477.3333333333333, "r": 315.3333333333333, "b": 485.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "18", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.6666666666667, "t": 476.6666666666667, "r": 356.6666666666667, "b": 485.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "23.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 476.6666666666667, "r": 392.6666666666667, "b": 485.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "23.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 476.0, "r": 424.3333333333333, "b": 485.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "14.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 441.3333333333333, "t": 476.6666666666667, "r": 457.3333333333333, "b": 484.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "14.3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 477.3333333333333, "t": 476.0, "r": 493.3333333333333, "b": 484.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "13.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 509.3333333333333, "t": 476.0, "r": 524.6666666666666, "b": 484.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "13.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 305.3333333333333, "t": 488.0, "r": 315.3333333333333, "b": 496.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "19", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.6666666666667, "t": 488.0, "r": 357.3333333333333, "b": 496.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "23.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 488.0, "r": 392.6666666666667, "b": 496.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "23.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.0, "t": 488.0, "r": 424.6666666666667, "b": 496.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "18.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 442.0, "t": 488.0, "r": 458.0, "b": 496.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "17.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 477.3333333333333, "t": 488.0, "r": 493.3333333333333, "b": 496.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "18.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 487.3333333333333, "r": 524.6666666666666, "b": 496.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "18.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 304.6666666666667, "t": 499.3333333333333, "r": 315.3333333333333, "b": 507.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "20", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 337.3333333333333, "t": 498.0, "r": 362.0, "b": 507.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "130.5\"", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 372.6666666666667, "t": 499.3333333333333, "r": 392.6666666666667, "b": 507.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "133.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 473.3333333333333, "t": 498.6666666666667, "r": 493.3333333333333, "b": 507.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "133.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 504.6666666666667, "t": 497.3333333333333, "r": 530.0, "b": 507.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "130.39)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 304.6666666666667, "t": 510.6666666666667, "r": 314.6666666666667, "b": 518.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 21, "end_row_offset_idx": 22, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "21", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 372.6666666666667, "t": 510.0, "r": 392.6666666666667, "b": 518.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 21, "end_row_offset_idx": 22, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "169.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.0, "t": 508.6666666666667, "r": 428.6666666666667, "b": 518.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 21, "end_row_offset_idx": 22, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "98.76)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 473.3333333333333, "t": 509.3333333333333, "r": 493.3333333333333, "b": 518.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 21, "end_row_offset_idx": 22, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "169.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 505.3333333333333, "t": 508.6666666666667, "r": 530.0, "b": 518.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 21, "end_row_offset_idx": 22, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "169.19)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 304.6666666666667, "t": 521.3333333333334, "r": 315.3333333333333, "b": 530.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "22", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 337.3333333333333, "t": 520.0, "r": 362.0, "b": 530.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "149.5\"", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 372.3333333333333, "t": 520.0, "r": 392.6666666666667, "b": 530.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "149.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 404.3333333333333, "t": 519.6666666666666, "r": 429.0, "b": 530.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "120.86)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 473.3333333333333, "t": 520.6666666666666, "r": 493.3333333333333, "b": 529.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "149 7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 505.3333333333333, "t": 519.3333333333334, "r": 530.0, "b": 529.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "149.4\"", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 305.3333333333333, "t": 532.6666666666666, "r": 315.3333333333333, "b": 540.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 23, "end_row_offset_idx": 24, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "23", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 373.3333333333333, "t": 532.0, "r": 392.6666666666667, "b": 540.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 23, "end_row_offset_idx": 24, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "102.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 438.0, "t": 531.3333333333334, "r": 462.6666666666667, "b": 540.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 23, "end_row_offset_idx": 24, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "169.34)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 474.0, "t": 532.0, "r": 494.0, "b": 540.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 23, "end_row_offset_idx": 24, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "102.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 505.3333333333333, "t": 530.6666666666666, "r": 530.0, "b": 540.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 23, "end_row_offset_idx": 24, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "102.1\"", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 305.3333333333333, "t": 543.3333333333334, "r": 316.0, "b": 552.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "28", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 341.3333333333333, "t": 543.3333333333334, "r": 357.3333333333333, "b": 552.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "23.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.6666666666667, "t": 542.6666666666666, "r": 424.0, "b": 551.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "19.3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 442.0, "t": 543.3333333333334, "r": 458.0, "b": 551.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "14.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 478.0, "t": 542.6666666666666, "r": 493.3333333333333, "b": 551.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "19.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 509.3333333333333, "t": 542.6666666666666, "r": 525.3333333333334, "b": 551.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "19.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 305.3333333333333, "t": 554.6666666666666, "r": 315.3333333333333, "b": 563.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "29", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.6666666666667, "t": 554.6666666666666, "r": 392.6666666666667, "b": 562.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "23.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.0, "t": 554.0, "r": 424.0, "b": 562.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "45.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 441.3333333333333, "t": 554.0, "r": 458.0, "b": 562.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "40.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 477.3333333333333, "t": 554.0, "r": 493.3333333333333, "b": 562.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "41.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 509.3333333333333, "t": 554.0, "r": 525.3333333333334, "b": 562.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "41.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 305.3333333333333, "t": 566.0, "r": 315.3333333333333, "b": 574.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "30", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 341.3333333333333, "t": 565.3333333333334, "r": 357.3333333333333, "b": 574.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "38.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.6666666666667, "t": 565.3333333333334, "r": 392.6666666666667, "b": 574.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "38.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.6666666666667, "t": 565.3333333333334, "r": 424.6666666666667, "b": 573.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "62.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 442.0, "t": 565.3333333333334, "r": 458.0, "b": 573.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "57.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 477.3333333333333, "t": 564.6666666666666, "r": 492.6666666666667, "b": 573.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "58.1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 509.3333333333333, "t": 564.6666666666666, "r": 525.3333333333334, "b": 573.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "58.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 298.0, "t": 576.6666666666666, "r": 323.3333333333333, "b": 584.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 27, "end_row_offset_idx": 28, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OMe-7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 341.3333333333333, "t": 576.6666666666666, "r": 356.6666666666667, "b": 584.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 27, "end_row_offset_idx": 28, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "52.1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.6666666666667, "t": 576.6666666666666, "r": 392.0, "b": 584.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 27, "end_row_offset_idx": 28, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "521", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.6666666666667, "t": 576.6666666666666, "r": 424.6666666666667, "b": 584.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 27, "end_row_offset_idx": 28, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "51.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 509.3333333333333, "t": 576.0, "r": 524.6666666666666, "b": 584.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 27, "end_row_offset_idx": 28, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "511", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 296.0, "t": 588.0, "r": 324.6666666666667, "b": 596.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 28, "end_row_offset_idx": 29, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OMe-23", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.6666666666667, "t": 587.3333333333334, "r": 392.0, "b": 596.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 28, "end_row_offset_idx": 29, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "56.1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 477.3333333333333, "t": 587.3333333333334, "r": 494.0, "b": 595.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 28, "end_row_offset_idx": 29, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "56.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 291.3333333333333, "t": 598.6666666666666, "r": 330.0, "b": 608.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 29, "end_row_offset_idx": 30, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OCOCH -1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 438.0, "t": 598.0, "r": 458.0, "b": 606.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 29, "end_row_offset_idx": 30, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "169.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 474.0, "t": 598.0, "r": 494.0, "b": 606.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 29, "end_row_offset_idx": 30, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "169.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 505.3333333333333, "t": 598.0, "r": 525.3333333333334, "b": 606.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 29, "end_row_offset_idx": 30, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "169.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 291.3333333333333, "t": 610.0, "r": 329.3333333333333, "b": 620.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 30, "end_row_offset_idx": 31, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OCOCH,-1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 442.0, "t": 609.3333333333334, "r": 458.0, "b": 618.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 30, "end_row_offset_idx": 31, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "21.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 477.3333333333333, "t": 609.3333333333334, "r": 494.0, "b": 618.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 30, "end_row_offset_idx": 31, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "21.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 509.3333333333333, "t": 609.3333333333334, "r": 525.3333333333334, "b": 617.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 30, "end_row_offset_idx": 31, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "21.8", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 31, "num_cols": 7, "grid": [[{"bbox": {"l": 302.0, "t": 272.6666666666667, "r": 316.0, "b": 280.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "No.", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 338.0, "t": 272.6666666666667, "r": 356.6666666666667, "b": 280.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "KS-1", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 372.0, "t": 272.0, "r": 390.6666666666667, "b": 280.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "KS-2", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 405.3333333333333, "t": 272.0, "r": 424.0, "b": 280.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "KS-3", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 438.6666666666667, "t": 272.0, "r": 458.0, "b": 280.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "KS-4", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 472.6666666666667, "t": 272.0, "r": 492.0, "b": 280.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "KS-5", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 506.0, "t": 271.3333333333333, "r": 525.3333333333334, "b": 280.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "KS-6", "column_header": true, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 335.3333333333333, "t": 288.0, "r": 356.0, "b": 296.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "210.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 370.6666666666667, "t": 288.0, "r": 391.3333333333333, "b": 296.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "210.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 406.6666666666667, "t": 288.0, "r": 422.6666666666667, "b": 296.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "83.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 440.6666666666667, "t": 288.0, "r": 456.6666666666667, "b": 296.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "89.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.0, "t": 288.0, "r": 492.0, "b": 296.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "90.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.0, "t": 287.3333333333333, "r": 523.3333333333334, "b": 296.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "90.7", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 299.3333333333333, "r": 356.0, "b": 308.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "53.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 375.3333333333333, "t": 299.3333333333333, "r": 391.3333333333333, "b": 308.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "53,4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 299.3333333333333, "r": 423.3333333333333, "b": 308.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "72.6", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 298.6666666666667, "r": 492.6666666666667, "b": 307.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "71.9", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 371.3333333333333, "t": 310.6666666666667, "r": 392.0, "b": 319.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "2145", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 310.0, "r": 423.3333333333333, "b": 318.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "77.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 436.6666666666667, "t": 310.0, "r": 456.6666666666667, "b": 318.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "204.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 310.0, "r": 492.6666666666667, "b": 318.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "77.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.0, "t": 310.0, "r": 524.0, "b": 318.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "77.5", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 321.3333333333333, "r": 356.0, "b": 330.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "49.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 375.3333333333333, "t": 322.0, "r": 392.0, "b": 330.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "49.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 321.3333333333333, "r": 423.3333333333333, "b": 330.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "42.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 440.6666666666667, "t": 321.3333333333333, "r": 456.6666666666667, "b": 330.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "50.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.0, "t": 321.3333333333333, "r": 492.0, "b": 330.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "43.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.0, "t": 321.3333333333333, "r": 524.6666666666666, "b": 330.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "43.6", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 309.0, "t": 333.6666666666667, "r": 313.6666666666667, "b": 341.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 332.6666666666667, "r": 356.0, "b": 341.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "45.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 375.3333333333333, "t": 332.6666666666667, "r": 391.3333333333333, "b": 341.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "45.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 332.6666666666667, "r": 423.3333333333333, "b": 341.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "40.3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 440.6666666666667, "t": 332.6666666666667, "r": 457.3333333333333, "b": 341.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "39.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 332.6666666666667, "r": 492.6666666666667, "b": 342.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "38.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.0, "t": 332.6666666666667, "r": 523.3333333333334, "b": 340.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "39.1", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 344.0, "r": 356.0, "b": 352.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "69.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 375.3333333333333, "t": 344.0, "r": 392.0, "b": 352.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "69.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 344.0, "r": 423.3333333333333, "b": 352.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "70.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 440.6666666666667, "t": 344.0, "r": 456.6666666666667, "b": 352.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "69.3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 343.3333333333333, "r": 492.6666666666667, "b": 352.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "71.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.0, "t": 343.3333333333333, "r": 524.0, "b": 352.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "70.9", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 336.6666666666667, "t": 355.3333333333333, "r": 356.6666666666667, "b": 364.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "175.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 372.0, "t": 354.6666666666667, "r": 392.0, "b": 363.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "175.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 403.3333333333333, "t": 355.3333333333333, "r": 423.3333333333333, "b": 363.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "174.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 437.3333333333333, "t": 355.3333333333333, "r": 456.6666666666667, "b": 363.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "172.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 472.6666666666667, "t": 354.6666666666667, "r": 492.6666666666667, "b": 363.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "174.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 504.0, "t": 354.6666666666667, "r": 524.6666666666666, "b": 363.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "174.6", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 366.6666666666667, "r": 356.6666666666667, "b": 374.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "71.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 366.0, "r": 392.0, "b": 374.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "71.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 366.0, "r": 423.3333333333333, "b": 374.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "86.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 440.6666666666667, "t": 366.0, "r": 456.6666666666667, "b": 374.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "86.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 366.0, "r": 492.6666666666667, "b": 374.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "85.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.0, "t": 366.0, "r": 524.6666666666666, "b": 374.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "85.8", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 377.3333333333333, "r": 356.6666666666667, "b": 385.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "60.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 377.3333333333333, "r": 392.0, "b": 385.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "59.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 377.3333333333333, "r": 423.3333333333333, "b": 385.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "54.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 440.6666666666667, "t": 377.3333333333333, "r": 456.6666666666667, "b": 385.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "53.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 377.3333333333333, "r": 492.6666666666667, "b": 385.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "54.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 376.6666666666667, "r": 524.6666666666666, "b": 385.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "54.6", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 304.6666666666667, "t": 388.6666666666667, "r": 314.6666666666667, "b": 396.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "10", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 388.0, "r": 356.6666666666667, "b": 396.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "48.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 388.0, "r": 392.0, "b": 396.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "48.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 388.0, "r": 423.3333333333333, "b": 396.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "58.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 441.3333333333333, "t": 388.0, "r": 457.3333333333333, "b": 396.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "60.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 388.0, "r": 492.6666666666667, "b": 396.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "60.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 388.0, "r": 524.6666666666666, "b": 396.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "60.5", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 399.3333333333333, "r": 356.0, "b": 408.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "22.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 399.3333333333333, "r": 392.0, "b": 407.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "22.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.0, "t": 399.3333333333333, "r": 423.3333333333333, "b": 407.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "16.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 477.3333333333333, "t": 398.6666666666667, "r": 492.6666666666667, "b": 407.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "15.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 398.6666666666667, "r": 524.6666666666666, "b": 407.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "15.9", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 304.6666666666667, "t": 410.6666666666667, "r": 314.6666666666667, "b": 418.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "12", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 410.6666666666667, "r": 356.6666666666667, "b": 418.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "34.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 410.0, "r": 392.0, "b": 418.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "34.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 410.0, "r": 423.3333333333333, "b": 418.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "25.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 440.6666666666667, "t": 410.0, "r": 457.3333333333333, "b": 418.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "25.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 410.0, "r": 492.6666666666667, "b": 418.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "25.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 409.3333333333333, "r": 524.0, "b": 418.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "25.1", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 304.6666666666667, "t": 421.3333333333333, "r": 314.0, "b": 430.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "13", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.0, "t": 421.3333333333333, "r": 356.6666666666667, "b": 430.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "34.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 421.3333333333333, "r": 392.0, "b": 430.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "34.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 421.3333333333333, "r": 423.3333333333333, "b": 430.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "37.3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 440.6666666666667, "t": 421.3333333333333, "r": 456.6666666666667, "b": 430.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "37.1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 420.6666666666667, "r": 492.6666666666667, "b": 429.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "37.3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 420.6666666666667, "r": 524.6666666666666, "b": 429.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "37.4", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 305.3333333333333, "t": 432.6666666666667, "r": 314.6666666666667, "b": 440.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "14", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.6666666666667, "t": 432.6666666666667, "r": 356.6666666666667, "b": 440.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "50.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 432.6666666666667, "r": 392.6666666666667, "b": 440.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "50.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.0, "t": 432.0, "r": 423.3333333333333, "b": 440.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "80.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 441.3333333333333, "t": 432.0, "r": 457.3333333333333, "b": 441.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "83.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 432.0, "r": 493.3333333333333, "b": 440.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "80.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 432.0, "r": 524.6666666666666, "b": 440.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "80.6", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 305.3333333333333, "t": 444.0, "r": 314.6666666666667, "b": 452.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "15", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.6666666666667, "t": 443.3333333333333, "r": 356.0, "b": 452.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "26.1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 443.3333333333333, "r": 391.3333333333333, "b": 452.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "26.1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 443.3333333333333, "r": 424.0, "b": 452.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "31.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 441.3333333333333, "t": 443.3333333333333, "r": 458.0, "b": 451.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "32.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 476.6666666666667, "t": 442.6666666666667, "r": 493.3333333333333, "b": 451.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "31.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 442.6666666666667, "r": 524.6666666666666, "b": 451.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "31.6", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 305.3333333333333, "t": 454.6666666666667, "r": 315.3333333333333, "b": 463.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "16", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 336.6666666666667, "t": 454.6666666666667, "r": 356.6666666666667, "b": 463.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "169.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 372.6666666666667, "t": 454.6666666666667, "r": 390.6666666666667, "b": 462.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "169.1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 404.0, "t": 454.6666666666667, "r": 424.0, "b": 462.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "169.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 438.0, "t": 454.6666666666667, "r": 457.3333333333333, "b": 462.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "168.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 473.3333333333333, "t": 454.0, "r": 493.3333333333333, "b": 462.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "169,4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 505.3333333333333, "t": 454.0, "r": 524.6666666666666, "b": 462.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "169,4", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 305.3333333333333, "t": 466.0, "r": 314.6666666666667, "b": 474.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "17", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.6666666666667, "t": 466.0, "r": 356.6666666666667, "b": 474.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "75.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.6666666666667, "t": 466.0, "r": 392.6666666666667, "b": 474.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "75.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.0, "t": 465.3333333333333, "r": 424.0, "b": 474.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "80.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 441.3333333333333, "t": 465.3333333333333, "r": 458.0, "b": 474.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "80.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 477.3333333333333, "t": 465.3333333333333, "r": 493.3333333333333, "b": 474.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "78.3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 465.3333333333333, "r": 524.6666666666666, "b": 474.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "78.3", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 305.3333333333333, "t": 477.3333333333333, "r": 315.3333333333333, "b": 485.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "18", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.6666666666667, "t": 476.6666666666667, "r": 356.6666666666667, "b": 485.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "23.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 476.6666666666667, "r": 392.6666666666667, "b": 485.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "23.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 407.3333333333333, "t": 476.0, "r": 424.3333333333333, "b": 485.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "14.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 441.3333333333333, "t": 476.6666666666667, "r": 457.3333333333333, "b": 484.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "14.3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 477.3333333333333, "t": 476.0, "r": 493.3333333333333, "b": 484.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "13.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 509.3333333333333, "t": 476.0, "r": 524.6666666666666, "b": 484.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "13.8", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 305.3333333333333, "t": 488.0, "r": 315.3333333333333, "b": 496.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "19", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 340.6666666666667, "t": 488.0, "r": 357.3333333333333, "b": 496.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "23.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.0, "t": 488.0, "r": 392.6666666666667, "b": 496.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "23.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.0, "t": 488.0, "r": 424.6666666666667, "b": 496.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "18.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 442.0, "t": 488.0, "r": 458.0, "b": 496.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "17.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 477.3333333333333, "t": 488.0, "r": 493.3333333333333, "b": 496.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "18.4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 508.6666666666667, "t": 487.3333333333333, "r": 524.6666666666666, "b": 496.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "18.4", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 304.6666666666667, "t": 499.3333333333333, "r": 315.3333333333333, "b": 507.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "20", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 337.3333333333333, "t": 498.0, "r": 362.0, "b": 507.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "130.5\"", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 372.6666666666667, "t": 499.3333333333333, "r": 392.6666666666667, "b": 507.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "133.8", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 473.3333333333333, "t": 498.6666666666667, "r": 493.3333333333333, "b": 507.3333333333333, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "133.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 504.6666666666667, "t": 497.3333333333333, "r": 530.0, "b": 507.6666666666667, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "130.39)", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 304.6666666666667, "t": 510.6666666666667, "r": 314.6666666666667, "b": 518.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 21, "end_row_offset_idx": 22, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "21", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 21, "end_row_offset_idx": 22, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 372.6666666666667, "t": 510.0, "r": 392.6666666666667, "b": 518.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 21, "end_row_offset_idx": 22, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "169.0", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.0, "t": 508.6666666666667, "r": 428.6666666666667, "b": 518.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 21, "end_row_offset_idx": 22, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "98.76)", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 21, "end_row_offset_idx": 22, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 473.3333333333333, "t": 509.3333333333333, "r": 493.3333333333333, "b": 518.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 21, "end_row_offset_idx": 22, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "169.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 505.3333333333333, "t": 508.6666666666667, "r": 530.0, "b": 518.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 21, "end_row_offset_idx": 22, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "169.19)", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 304.6666666666667, "t": 521.3333333333334, "r": 315.3333333333333, "b": 530.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "22", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 337.3333333333333, "t": 520.0, "r": 362.0, "b": 530.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "149.5\"", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 372.3333333333333, "t": 520.0, "r": 392.6666666666667, "b": 530.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "149.5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 404.3333333333333, "t": 519.6666666666666, "r": 429.0, "b": 530.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "120.86)", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 473.3333333333333, "t": 520.6666666666666, "r": 493.3333333333333, "b": 529.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "149 7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 505.3333333333333, "t": 519.3333333333334, "r": 530.0, "b": 529.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "149.4\"", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 305.3333333333333, "t": 532.6666666666666, "r": 315.3333333333333, "b": 540.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 23, "end_row_offset_idx": 24, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "23", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 23, "end_row_offset_idx": 24, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 373.3333333333333, "t": 532.0, "r": 392.6666666666667, "b": 540.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 23, "end_row_offset_idx": 24, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "102.5", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 23, "end_row_offset_idx": 24, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 438.0, "t": 531.3333333333334, "r": 462.6666666666667, "b": 540.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 23, "end_row_offset_idx": 24, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "169.34)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 474.0, "t": 532.0, "r": 494.0, "b": 540.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 23, "end_row_offset_idx": 24, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "102.6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 505.3333333333333, "t": 530.6666666666666, "r": 530.0, "b": 540.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 23, "end_row_offset_idx": 24, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "102.1\"", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 305.3333333333333, "t": 543.3333333333334, "r": 316.0, "b": 552.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "28", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 341.3333333333333, "t": 543.3333333333334, "r": 357.3333333333333, "b": 552.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "23.4", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.6666666666667, "t": 542.6666666666666, "r": 424.0, "b": 551.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "19.3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 442.0, "t": 543.3333333333334, "r": 458.0, "b": 551.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "14.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 478.0, "t": 542.6666666666666, "r": 493.3333333333333, "b": 551.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "19.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 509.3333333333333, "t": 542.6666666666666, "r": 525.3333333333334, "b": 551.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "19.2", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 305.3333333333333, "t": 554.6666666666666, "r": 315.3333333333333, "b": 563.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "29", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.6666666666667, "t": 554.6666666666666, "r": 392.6666666666667, "b": 562.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "23.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.0, "t": 554.0, "r": 424.0, "b": 562.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "45.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 441.3333333333333, "t": 554.0, "r": 458.0, "b": 562.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "40.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 477.3333333333333, "t": 554.0, "r": 493.3333333333333, "b": 562.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "41.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 509.3333333333333, "t": 554.0, "r": 525.3333333333334, "b": 562.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "41.2", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 305.3333333333333, "t": 566.0, "r": 315.3333333333333, "b": 574.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "30", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 341.3333333333333, "t": 565.3333333333334, "r": 357.3333333333333, "b": 574.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "38.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.6666666666667, "t": 565.3333333333334, "r": 392.6666666666667, "b": 574.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "38.2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.6666666666667, "t": 565.3333333333334, "r": 424.6666666666667, "b": 573.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "62.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 442.0, "t": 565.3333333333334, "r": 458.0, "b": 573.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "57.9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 477.3333333333333, "t": 564.6666666666666, "r": 492.6666666666667, "b": 573.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "58.1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 509.3333333333333, "t": 564.6666666666666, "r": 525.3333333333334, "b": 573.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "58.2", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 298.0, "t": 576.6666666666666, "r": 323.3333333333333, "b": 584.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 27, "end_row_offset_idx": 28, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OMe-7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 341.3333333333333, "t": 576.6666666666666, "r": 356.6666666666667, "b": 584.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 27, "end_row_offset_idx": 28, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "52.1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.6666666666667, "t": 576.6666666666666, "r": 392.0, "b": 584.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 27, "end_row_offset_idx": 28, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "521", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 408.6666666666667, "t": 576.6666666666666, "r": 424.6666666666667, "b": 584.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 27, "end_row_offset_idx": 28, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "51.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 27, "end_row_offset_idx": 28, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 27, "end_row_offset_idx": 28, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 509.3333333333333, "t": 576.0, "r": 524.6666666666666, "b": 584.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 27, "end_row_offset_idx": 28, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "511", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 296.0, "t": 588.0, "r": 324.6666666666667, "b": 596.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 28, "end_row_offset_idx": 29, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OMe-23", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 28, "end_row_offset_idx": 29, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 376.6666666666667, "t": 587.3333333333334, "r": 392.0, "b": 596.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 28, "end_row_offset_idx": 29, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "56.1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 28, "end_row_offset_idx": 29, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 28, "end_row_offset_idx": 29, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 477.3333333333333, "t": 587.3333333333334, "r": 494.0, "b": 595.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 28, "end_row_offset_idx": 29, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "56.2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 28, "end_row_offset_idx": 29, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 291.3333333333333, "t": 598.6666666666666, "r": 330.0, "b": 608.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 29, "end_row_offset_idx": 30, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OCOCH -1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 29, "end_row_offset_idx": 30, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 29, "end_row_offset_idx": 30, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 29, "end_row_offset_idx": 30, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 438.0, "t": 598.0, "r": 458.0, "b": 606.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 29, "end_row_offset_idx": 30, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "169.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 474.0, "t": 598.0, "r": 494.0, "b": 606.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 29, "end_row_offset_idx": 30, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "169.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 505.3333333333333, "t": 598.0, "r": 525.3333333333334, "b": 606.6666666666666, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 29, "end_row_offset_idx": 30, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "169.8", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 291.3333333333333, "t": 610.0, "r": 329.3333333333333, "b": 620.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 30, "end_row_offset_idx": 31, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OCOCH,-1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 30, "end_row_offset_idx": 31, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 30, "end_row_offset_idx": 31, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 30, "end_row_offset_idx": 31, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 442.0, "t": 609.3333333333334, "r": 458.0, "b": 618.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 30, "end_row_offset_idx": 31, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "21.7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 477.3333333333333, "t": 609.3333333333334, "r": 494.0, "b": 618.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 30, "end_row_offset_idx": 31, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "21.8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 509.3333333333333, "t": 609.3333333333334, "r": 525.3333333333334, "b": 617.3333333333334, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 30, "end_row_offset_idx": 31, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "21.8", "column_header": false, "row_header": false, "row_section": false}]]}}], "key_value_items": [], "form_items": [], "pages": {"1": {"size": {"width": 570.0, "height": 807.0}, "page_no": 1}, "2": {"size": {"width": 570.0, "height": 810.0}, "page_no": 2}, "3": {"size": {"width": 570.0, "height": 807.0}, "page_no": 3}, "4": {"size": {"width": 570.0, "height": 810.0}, "page_no": 4}, "5": {"size": {"width": 570.0, "height": 808.0}, "page_no": 5}, "6": {"size": {"width": 570.0, "height": 810.0}, "page_no": 6}}}