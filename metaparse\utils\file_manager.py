"""
File management utilities for the MetaParse package.

This module provides utilities for managing files and directories.
Author : <PERSON> Jadhav
Date : 2025-05-20
"""

import os
import csv
import json
from PIL import Image
from typing import List, Dict, Any, Union


class FileManager:
    """
    Manages file operations for document extraction results.
    
    This class handles creating output folders, saving text, tables, images, and JSON data.
    """
    
    def __init__(self, save_path: str):
        """
        Initialize the FileManager with a save path.
        
        Args:
            save_path: Path where extracted data will be saved
        """
        self.save_path = save_path
        self.create_output_folder()

    def create_output_folder(self):
        """Ensure the output folder exists, create if it doesn't."""
        if not os.path.exists(self.save_path):
            os.makedirs(self.save_path)

    def save_text(self, text: str, filename: str):
        """
        Save extracted text to a file.
        
        Args:
            text: Text content to save
            filename: Name of the output file
        """
        with open(os.path.join(self.save_path, filename), 'w', encoding='utf-8') as f:
            f.write(text)

    def save_table(self, table_data: List[List[str]], filename: str):
        """
        Save extracted tables to a CSV file.
        
        Args:
            table_data: Table data as a list of rows
            filename: Name of the output file
        """
        with open(os.path.join(self.save_path, filename), 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerows(table_data)

    def save_json(self, data: Dict[str, Any], filename: str):
        """
        Save data to a JSON file.
        
        Args:
            data: Data to save as JSON
            filename: Name of the output file
        """
        json_path = os.path.join(self.save_path, filename if filename.endswith(".json") else f"{filename}.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"✅ JSON saved: {json_path}")

    def save_image(self, images: Union[Image.Image, List[Image.Image]], filenames: Union[str, List[str]]):
        """
        Save extracted images as PNG files inside 'images' folder.
        
        Args:
            images: Single image or list of images
            filenames: Single filename or list of filenames (without extension)
        """
        # Define path for 'images' subfolder
        image_folder = os.path.join(self.save_path, "images")
        os.makedirs(image_folder, exist_ok=True)

        # Handle single image case
        if isinstance(images, Image.Image):
            images = [images]
            filenames = [filenames]

        for image, name in zip(images, filenames):
            image_path = os.path.join(image_folder, f"{name}.png")
            image.save(image_path)
