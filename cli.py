"""
Command-line interface for the MetaParse package.

This module provides a command-line interface for using the MetaParse package.

Author : Anand Jadhav
Date : 2025-05-20
"""

import os
import time
import argparse
import asyncio
import glob
import pandas as pd
from typing import List, Dict, Any, Optional

from metaparse.core.pipeline import DocExtractionPipeline


def find_document_files_recursive(folder_path: str) -> List[str]:
    """
    Recursively find all PDF and DOCX files in a folder and its subfolders.

    Args:
        folder_path: Path to the folder to search

    Returns:
        List of file paths to PDF and DOCX files
    """
    document_files = []

    # Check if the folder exists
    if not os.path.isdir(folder_path):
        print(f"Warning: {folder_path} is not a valid directory")
        return document_files

    # Walk through the directory tree
    for root, _, files in os.walk(folder_path):
        for file in files:
            # Check if the file has a supported extension
            if file.lower().endswith(('.pdf', '.docx')):
                # Get the full path to the file
                file_path = os.path.join(root, file)
                document_files.append(file_path)

    return document_files


def extract_doc_data(doc_path: str, output_folder: str, optimization_level: str = 'balanced', use_cache: bool = True) -> Dict[str, Any]:
    """
    Extract data from a single document file.

    Args:
        doc_path: Path to the document file
        output_folder: Path to the output folder
        optimization_level: Optimization level ('speed', 'balanced', or 'quality')
        use_cache: Whether to use caching

    Returns:
        Dictionary with extraction results
    """
    pipeline = DocExtractionPipeline(
        doc_path,
        output_folder,
        optimization_level=optimization_level,
        use_cache=use_cache
    )
    pipeline.run_pipeline()
    return {
        'doc_path': doc_path,
        'output_folder': output_folder,
        'success': True
    }


async def extract_doc_data_async(doc_path: str, output_folder: str, optimization_level: str = 'balanced', use_cache: bool = True) -> Dict[str, Any]:
    """
    Extract data from a single document file asynchronously.

    Args:
        doc_path: Path to the document file
        output_folder: Path to the output folder
        optimization_level: Optimization level ('speed', 'balanced', or 'quality')
        use_cache: Whether to use caching

    Returns:
        Dictionary with extraction results
    """
    pipeline = DocExtractionPipeline(
        doc_path,
        output_folder,
        optimization_level=optimization_level,
        use_cache=use_cache
    )
    await pipeline.run_pipeline_async()
    return {
        'doc_path': doc_path,
        'output_folder': output_folder,
        'success': True
    }


def batch_process_docs(doc_files: List[str], output_base_folder: str, input_folder: str, parallel: bool = True,
                      optimization_level: str = 'speed', max_workers: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    Process multiple document files in batch mode.

    Args:
        doc_files: List of document file paths
        output_base_folder: Base folder for output
        input_folder: Input folder containing the documents (used for creating relative paths)
        parallel: Whether to use parallel processing
        optimization_level: Optimization level ('speed', 'balanced', or 'quality')
        max_workers: Maximum number of worker processes

    Returns:
        List of dictionaries with extraction results
    """
    # Create output folders for each document
    save_paths = []
    for doc_file in doc_files:
        # Extract filename without extension
        doc_filename = os.path.basename(doc_file)
        doc_name = os.path.splitext(doc_filename)[0]

        # Create a unique output folder name
        # If the file is in a subfolder, include the subfolder structure in the output folder name
        if os.path.dirname(doc_file) != os.path.normpath(input_folder):
            # Get the relative path from the input folder to the file's directory
            rel_path = os.path.relpath(os.path.dirname(doc_file), input_folder)
            # Replace path separators with underscores to create a valid folder name
            subfolder_part = rel_path.replace(os.path.sep, '_')
            # Combine the subfolder part with the document name
            output_folder_name = f"{subfolder_part}_{doc_name}"
        else:
            output_folder_name = doc_name

        output_folder = os.path.join(output_base_folder, output_folder_name)
        save_paths.append(output_folder)

    # Process in parallel or sequentially
    if parallel:

        results = DocExtractionPipeline.process_batch_parallel(
            doc_files,
            save_paths,
            optimization_level=optimization_level,
            max_workers=max_workers
        )
    else:
        # Sequential processing
        results = []
        for doc_file, save_path in zip(doc_files, save_paths):
            try:
                result = extract_doc_data(doc_file, save_path, optimization_level)
                results.append(result)
            except Exception as e:
                print(f"❌ Error processing {os.path.basename(doc_file)}: {e}")
                results.append({
                    'doc_path': doc_file,
                    'output_folder': save_path,
                    'success': False,
                    'error': str(e)
                })

    return results


async def batch_process_docs_async(doc_files: List[str], output_base_folder: str, input_folder: str,
                                 optimization_level: str = 'quality', concurrency_limit: int = 3) -> List[Dict[str, Any]]:
    """
    Process multiple document files asynchronously.

    Args:
        doc_files: List of document file paths
        output_base_folder: Base folder for output
        input_folder: Input folder containing the documents (used for creating relative paths)
        optimization_level: Optimization level ('speed', 'balanced', or 'quality')
        concurrency_limit: Maximum number of concurrent tasks

    Returns:
        List of dictionaries with extraction results
    """
    # Create output folders for each document
    save_paths = []
    for doc_file in doc_files:
        # Extract filename without extension for any supported document type
        doc_filename = os.path.basename(doc_file)
        doc_name = os.path.splitext(doc_filename)[0]

        # Create a unique output folder name
        # If the file is in a subfolder, include the subfolder structure in the output folder name
        if os.path.dirname(doc_file) != os.path.normpath(input_folder):
            # Get the relative path from the input folder to the file's directory
            rel_path = os.path.relpath(os.path.dirname(doc_file), input_folder)
            # Replace path separators with underscores to create a valid folder name
            subfolder_part = rel_path.replace(os.path.sep, '_')
            # Combine the subfolder part with the document name
            output_folder_name = f"{subfolder_part}_{doc_name}"
        else:
            output_folder_name = doc_name

        output_folder = os.path.join(output_base_folder, output_folder_name)
        save_paths.append(output_folder)

    # Process asynchronously with concurrency control
    results = await DocExtractionPipeline.process_batch_async(
        doc_files,
        save_paths,
        optimization_level=optimization_level,
        concurrency_limit=concurrency_limit
    )

    return results


def generate_summary_sheet(processed_docs: List[Dict[str, Any]], output_base_folder: str, input_path: str, is_batch_mode: bool = False) -> str:
    """
    Generate a summary Excel sheet with details about each processed document.

    Args:
        processed_docs: List of dictionaries with processing results
        output_base_folder: Base folder where output is stored
        input_path: Path to the input file or folder 
        is_batch_mode: Whether processing was done in batch mode

    Returns:
        Path to the generated summary sheet
    """ 
    summary_data = []

    for doc_result in processed_docs:
        doc_path = doc_result.get('doc_path', '')
        output_folder = doc_result.get('output_folder', '')
        success = doc_result.get('success', False)

        if not success or not os.path.exists(output_folder):
            # Skip failed documents
            continue

        doc_name = os.path.basename(doc_path)

        # Count JSON files
        json_files = glob.glob(os.path.join(output_folder, "*.json"))
        num_json_files = len(json_files)

        # Count table Excel files
        table_files = glob.glob(os.path.join(output_folder, "table_*.xlsx"))
        num_tables = len(table_files)

        # Count image files - images are saved in the 'images' subfolder
        images_folder = os.path.join(output_folder, "images")
        if os.path.exists(images_folder):
            image_files = glob.glob(os.path.join(images_folder, "*.png"))
            num_images = len(image_files)
        else:
            # Fallback to check for images in the main output folder
            image_files = glob.glob(os.path.join(output_folder, "*.png"))
            num_images = len(image_files)

        # Add to summary data
        summary_data.append({
            'document_name': doc_name,
            'number_of_json_data_files': num_json_files,
            'number_of_tables': num_tables,
            'number_of_pictures': num_images
        })

    # Check if we have any data to include in the summary
    if not summary_data:
        print("⚠️ No valid document data found for summary sheet. Checking output folders directly...")

        # Try to find document folders in the output directory
        if os.path.exists(output_base_folder):
            # First, get a mapping of folder names to original file names from the input directory
            folder_to_file_map = {}
            if os.path.isdir(input_path):
                # Recursively find all document files
                all_files = find_document_files_recursive(input_path)
                for file_path in all_files:
                    file = os.path.basename(file_path)
                    file_name_without_ext = os.path.splitext(file)[0]
                    folder_to_file_map[file_name_without_ext] = file

            for folder_name in os.listdir(output_base_folder):
                folder_path = os.path.join(output_base_folder, folder_name)
                if os.path.isdir(folder_path):
                    # Count JSON files
                    json_files = glob.glob(os.path.join(folder_path, "*.json"))
                    num_json_files = len(json_files)

                    # Count table Excel files
                    table_files = glob.glob(os.path.join(folder_path, "table_*.xlsx"))
                    num_tables = len(table_files)

                    # Count image files - images are saved in the 'images' subfolder
                    images_folder = os.path.join(folder_path, "images")
                    if os.path.exists(images_folder):
                        image_files = glob.glob(os.path.join(images_folder, "*.png"))
                        num_images = len(image_files)
                    else:
                        # Fallback to check for images in the main output folder
                        image_files = glob.glob(os.path.join(folder_path, "*.png"))
                        num_images = len(image_files)

                    # Only add folders that contain extracted data
                    if num_json_files > 0 or num_tables > 0 or num_images > 0:

                        # Use the original file name with extension if available
                        if folder_name in folder_to_file_map:
                            document_name = folder_to_file_map[folder_name]
                        else:
                            # Try to find a matching file in the input directory
                            matching_file = None
                            if os.path.isdir(input_path):
                                for file in os.listdir(input_path):
                                    if file.lower().startswith(folder_name.lower()):
                                        matching_file = file
                                        break

                            if matching_file:
                                document_name = matching_file
                            else:
                                # If no match found, use folder name
                                document_name = folder_name

                        summary_data.append({
                            'document_name': document_name,
                            'number_of_json_data_files': num_json_files,
                            'number_of_tables': num_tables,
                            'number_of_pictures': num_images
                        })

    # Create DataFrame
    df = pd.DataFrame(summary_data)

    # Check if we have any data after our fallback approach
    if df.empty:
        print("❌ No data available for summary sheet. Summary sheet will not be generated.")
        return None

    # Determine summary sheet name
    if is_batch_mode:
        # Use input folder name for batch mode
        input_folder_name = os.path.basename(os.path.normpath(input_path))
        summary_file_name = f"{input_folder_name}_summary.xlsx"
    else:
        # Use input document name for single file mode
        input_doc_name = os.path.basename(input_path)
        input_doc_name_without_ext = os.path.splitext(input_doc_name)[0]
        summary_file_name = f"{input_doc_name_without_ext}_summary.xlsx"

    # Create summary file path (always in the output folder)
    summary_file_path = os.path.join(output_base_folder, summary_file_name)

    # Save to Excel
    df.to_excel(summary_file_path, index=False)

    # print(f"✅ Summary sheet generated at: {summary_file_path}")
    return summary_file_path


def main():
    """Main entry point for the command-line interface."""
    print("MetaParse Extraction Process Started...")

    start = time.time()
    parser = argparse.ArgumentParser(description="Document Data Extraction Tool")
    parser.add_argument("--input", "-i", required=True, help="Input document file or folder")
    parser.add_argument("--output", "-o", required=True, help="Output base folder")
    parser.add_argument("--batch", "-b", action="store_true", help="Process all documents in input folder")
    parser.add_argument("--parallel", "-p", action="store_true", help="Use parallel processing for batch mode")
    parser.add_argument("--async", "-a", action="store_true", dest="use_async", help="Use async processing")
    parser.add_argument("--workers", "-w", type=int, default=None, help="Number of worker processes/threads")
    parser.add_argument("--optimization", "-opt", choices=["speed", "balanced", "quality"],
                        default="balanced", help="Optimization level")
    parser.add_argument("--no-cache", action="store_true", help="Disable caching")

    args = parser.parse_args()

    # Determine if input is a file or directory
    if args.batch or os.path.isdir(args.input):

        # Batch processing mode
        doc_folder = args.input
        output_base_folder = args.output

        # Recursively collect all document files from the folder and its subfolders
        doc_files = find_document_files_recursive(doc_folder)

        if not doc_files:
            print("No PDF or DOCX files found in the specified folder or its subfolders.")
            exit(1)

        print(f"Found {len(doc_files)} document files to process.")

        # Print the list of files that will be processed
        print("Files to be processed:")
        for i, file_path in enumerate(doc_files, 1):
            print(f"  {i}. {file_path}")

        if args.use_async:
            # Use async processing
            async def main_async():
                results = await batch_process_docs_async(
                    doc_files,
                    output_base_folder,
                    input_folder=doc_folder,
                    optimization_level=args.optimization,
                    concurrency_limit=args.workers or 3
                )
                return results

            results = asyncio.run(main_async())
        else:
            # Use parallel or sequential processing
            results = batch_process_docs(
                doc_files,
                output_base_folder,
                input_folder=doc_folder,
                parallel=args.parallel,
                optimization_level=args.optimization,
                max_workers=args.workers
            )

        # summary
        successful = sum(1 for r in results if r.get('success', False))
        print(f"\nSuccessfully processed: {successful}/{len(results)} files")

        # Generate summary sheet
        generate_summary_sheet(results, output_base_folder, args.input, is_batch_mode=True)

    else:
        # Single file processing mode
        doc_file = args.input

        if not os.path.exists(doc_file):
            print(f"Document file not found: {doc_file}")
            exit(1)

        # Extract filename without extension for any supported document type
        doc_filename = os.path.basename(doc_file)
        doc_name = os.path.splitext(doc_filename)[0]
        output_folder = os.path.join(args.output, doc_name)

        if args.use_async:
            # Use async processing for single file
            async def main_async():
                return await extract_doc_data_async(
                    doc_file,
                    output_folder,
                    optimization_level=args.optimization,
                    use_cache=not args.no_cache
                )

            result = asyncio.run(main_async())
            # Generate summary sheet for single file
            generate_summary_sheet([result], args.output, args.input, is_batch_mode=False)
        else:
            # Use synchronous processing for single file
            result = extract_doc_data(
                doc_file,
                output_folder,
                optimization_level=args.optimization,
                use_cache=not args.no_cache
            )
            # Generate summary sheet for single file
            generate_summary_sheet([result], args.output, args.input, is_batch_mode=False)

    print(f"✅✅ Complete Extraction Time = {round((time.time() - start), 2)} Seconds. ✅✅")


if __name__ == "__main__":
    main()

 

###############################
## command-line usage:
# For single file:
# python cli.py --input "C:\Users\<USER>\Desktop\metaparse_package\pdf_folder\41419292R.article.002.pdf" --output "C:\Users\<USER>\Desktop\metaparse_package\extracted_data_folder" --optimization quality
# python cli.py --input "C:\Users\<USER>\Desktop\metaparse_package\pdf_folder\04261226J.article.001.pdf" --output "C:\Users\<USER>\Desktop\metaparse_package\extracted_data_folder" --no-cache --optimization quality

# This will generate a summary Excel sheet named "41419292R.article.003_summary.xlsx" in the output folder

# python cli.py --input "C:\Users\<USER>\Desktop\MetaParse\pdf_folder\41289237G.article.003.pdf" --output "C:\Users\<USER>\Desktop\metaparse\extracted_data_folder" --optimization quality

# cli.py --input "C:\Users\<USER>\Desktop\MetaParse\pdf_folder\half_page.PNG" --output "C:\Users\<USER>\Desktop\metaparse\extracted_data_folder" --optimization quality

# For batch mode: python cli.py --input "C:\Users\<USER>\Desktop\MetaParse\pdf_folder" --output "C:\Users\<USER>\Desktop\MetaParse\extracted_data_folder" --batch --parallel --optimization speed
# This will generate a summary Excel sheet named "pdf_folder_summary.xlsx" in the output folder

# For async mode: python cli.py --input "C:\Users\<USER>\Desktop\MetaParse_package\pdf_folder" --output "C:\Users\<USER>\Desktop\MetaPars_package\extracted_data_folder" --batch --async --workers 4
# This will also generate a summary Excel sheet with details about all processed documents