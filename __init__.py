"""
MetaParse - A document parsing and extraction tool for various document formats.

This package provides tools to extract text, tables, and images from various document formats
including PDF, DOCX, images, Excel, and PowerPoint.
"""

__version__ = "0.1.0"
__author__ = "Anand Jadhav"
__email__ = "<EMAIL>"
__license__ = "MIT"

# Import main components for easier access
from metaparse.core.pipeline import DocExtractionPipeline

# Define what's available when using "from metaparse import *"
__all__ = ['DocExtractionPipeline']
