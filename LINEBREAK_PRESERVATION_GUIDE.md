# Linebreak Preservation in Docling Text Extraction

## Overview

This guide explains how to preserve linebreaks as `\n` characters when extracting text from PDF files using the enhanced Docling library integration in MetaParse.

## Problem Statement

By default, Docling extracts text from PDF documents and joins text blocks with spaces, which can lose the original paragraph structure and line breaks. This enhancement allows you to preserve linebreaks as `\n` characters in the extracted JSON data.

## Solution

The enhanced `DocExtractor` class now includes linebreak preservation functionality with configurable options.

## Features

### 1. Configurable Linebreak Preservation
- **Enable/Disable**: Toggle linebreak preservation on or off
- **Custom Characters**: Use custom characters for linebreaks and paragraph separators
- **Flexible Processing**: Works with both table cells and regular text content

### 2. Enhanced Text Processing
- **Paragraph Structure**: Maintains paragraph boundaries with double newlines
- **Line Structure**: Preserves individual line breaks within paragraphs
- **Table Cells**: Applies linebreak preservation to table cell content

### 3. Multiple Export Formats
- **JSON Export**: Preserves linebreaks in dictionary format
- **Markdown Export**: Custom markdown export with linebreak preservation
- **Backward Compatibility**: Falls back to default behavior when disabled

## Usage

### Basic Usage with Linebreak Preservation

```python
from metaparse.extractors.doc_extractor_linebreak import DocExtractor

# Create extractor with default linebreak preservation
extractor = DocExtractor(
    doc_path="your_document.pdf",
    optimization_level='quality',
    use_cache=False
)

# Extract text with preserved linebreaks
result = extractor.extract_text_optimized()

# The result will contain text with \n characters preserving line structure
```

### Custom Configuration

```python
from metaparse.extractors.doc_extractor_linebreak import DocExtractor, DocExtractorConfig

# Create custom configuration
custom_settings = {
    'text_processing': {
        'preserve_linebreaks': True,        # Enable linebreak preservation
        'linebreak_character': '\n',       # Character for line breaks
        'paragraph_separator': '\n\n'      # Separator between paragraphs
    }
}

# Create extractor with custom settings
extractor = DocExtractor(
    doc_path="your_document.pdf",
    optimization_level='quality',
    use_cache=False,
    custom_settings=custom_settings
)

result = extractor.extract_text_optimized()
```

### Alternative Linebreak Characters

```python
# Use pipe characters instead of newlines (useful for certain processing pipelines)
custom_settings = {
    'text_processing': {
        'preserve_linebreaks': True,
        'linebreak_character': ' | ',      # Use pipe separator
        'paragraph_separator': ' || '      # Double pipe for paragraphs
    }
}
```

### Disable Linebreak Preservation

```python
# Disable linebreak preservation (default Docling behavior)
custom_settings = {
    'text_processing': {
        'preserve_linebreaks': False
    }
}
```

## Configuration Options

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `preserve_linebreaks` | boolean | `True` | Enable/disable linebreak preservation |
| `linebreak_character` | string | `'\n'` | Character(s) to use for line breaks |
| `paragraph_separator` | string | `'\n\n'` | Character(s) to use between paragraphs |

## Example Output

### With Linebreak Preservation (Enabled)
```json
{
  "pages": {
    "1": {
      "text": "This is the first line of a paragraph.\nThis is the second line of the same paragraph.\n\nThis is a new paragraph.\nWith another line in it.",
      "linebreaks_preserved": true,
      "paragraph_count": 2
    }
  }
}
```

### Without Linebreak Preservation (Disabled)
```json
{
  "pages": {
    "1": {
      "text": "This is the first line of a paragraph. This is the second line of the same paragraph. This is a new paragraph. With another line in it."
    }
  }
}
```

## Advanced Usage

### Using DocProcessor with Linebreak Preservation

```python
from metaparse.extractors.doc_extractor_linebreak import DocProcessor

# Create processor with linebreak preservation
processor = DocProcessor(
    optimization_level='quality',
    use_cache=False
)

# Process multiple documents
doc_files = ["doc1.pdf", "doc2.pdf", "doc3.pdf"]
results = processor.process_docs_sequential(doc_files)

# All documents will be processed with linebreak preservation enabled
```

### Custom Export Methods

```python
# Use custom export methods for enhanced linebreak handling
extractor = DocExtractor("document.pdf")

# Convert document first
result = extractor.converter.convert(extractor.doc_path)

# Export with custom linebreak preservation
markdown_with_linebreaks = extractor.export_to_markdown_with_linebreaks(result)
dict_with_linebreaks = extractor.export_to_dict_with_linebreaks(result)
```

## Testing

Use the provided test script to verify linebreak preservation:

```bash
python test_linebreak_preservation.py
```

This script will:
1. Test extraction with linebreak preservation enabled
2. Test extraction with linebreak preservation disabled
3. Compare the results and show the differences
4. Demonstrate custom configuration options

## Benefits

1. **Improved Text Quality**: Maintains original document structure
2. **Better NLP Processing**: Preserves paragraph boundaries for better analysis
3. **Flexible Output**: Configurable linebreak characters for different use cases
4. **Backward Compatibility**: Can be disabled to maintain original behavior
5. **Enhanced RAG**: Better text chunks for retrieval-augmented generation

## Troubleshooting

### Common Issues

1. **No linebreaks in output**: Ensure `preserve_linebreaks` is set to `True`
2. **Unexpected characters**: Check `linebreak_character` setting
3. **Performance impact**: Linebreak processing adds minimal overhead
4. **Memory usage**: No significant increase in memory usage

### Debug Information

The processed text items include debug information:
- `linebreaks_preserved`: Boolean indicating if linebreaks were processed
- `original_line_count`: Number of original lines (for text items)
- `paragraph_count`: Number of paragraphs (for page text)

## Integration with Existing Code

This enhancement is backward compatible. Existing code will continue to work with the default linebreak preservation enabled. To maintain the original behavior, explicitly set `preserve_linebreaks: False`.

## Performance Considerations

- **Minimal Impact**: Linebreak processing adds negligible overhead
- **Memory Efficient**: No significant increase in memory usage
- **Caching Compatible**: Works with existing caching mechanisms
- **Parallel Processing**: Compatible with parallel document processing

## Future Enhancements

Potential future improvements:
- Smart paragraph detection based on font changes
- Configurable line spacing thresholds
- Integration with custom Docling serializers
- Support for different document types (DOCX, HTML, etc.)
