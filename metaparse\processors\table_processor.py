"""
Table processing module for the MetaParse package.

This module provides classes for extracting and processing tables from documents.
Author : Anand Jadhav
Date : 2025-05-20
"""

import os
import re
import json
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union

from metaparse.processors.table_image_handler import handle_image_table


class TableProcessor:
    """
    A class to process and extract tables from document JSON data.
    Handles table extraction, title mapping, and data export.
    """

    def __init__(self, input_json: Union[str, Dict], doc_path: Optional[str] = None):
        """
        Initialize TableProcessor with input JSON data.

        Args:
            input_json: Either a path to JSON file (str) or JSON data (Dict)
            doc_path: Path to the source document file
        """
        if isinstance(input_json, str):
            self.input_json_path = input_json
            self.doc_json = self._load_json()
            # Get save path from input JSON path
            self.save_path = os.path.dirname(self.input_json_path)
        else:
            # When input_json is a dictionary, not a file path
            self.doc_json = input_json
            # If we're running from main_correct.py, we need to set the output path
            # based on the document path since we don't have an input JSON path
            if doc_path:
                # Extract the document filename without extension
                doc_filename = os.path.basename(doc_path)
                doc_name = os.path.splitext(doc_filename)[0]
                # Set the output folder to be in the same directory as the document
                output_folder = os.path.join(os.path.dirname(doc_path), "..\\output_folder", doc_name)
                self.input_json_path = os.path.join(output_folder, "extracted_data.json")
                self.save_path = output_folder
            else:
                self.input_json_path = None
                self.save_path = os.getcwd()

        self.doc_path = doc_path

    def _load_json(self) -> Dict:
        """Load and return JSON data from file."""
        try:
            with open(self.input_json_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Error loading JSON file: {e}")
            return {}

    def _create_output_folder(self) -> str:
        """Create and return output folder path."""
        folder = self.input_json_path.split(".")[0]
        os.makedirs(folder, exist_ok=True)
        return folder

    def _find_table_titles(self) -> Dict[str, Dict[str, str]]:
        """
        Find and map table references to their titles.

        Returns:
            Dict mapping table references to their titles
        """
        body_children = self.doc_json.get("body", {}).get("children", [])
        refs = [child["$ref"] for child in body_children if "$ref" in child]
        tables = self.doc_json.get("tables", [])

        table_title_mapping = {}

        # First try to get captions directly from table objects
        for table in tables:
            table_ref = table.get("self_ref")
            if table_ref:
                # Try to get caption from the table
                caption_text = self._find_caption_in_table(table_ref)
                if caption_text:
                    table_title_mapping[table_ref] = {"title": caption_text}

        # Then try to find titles from preceding text references
        for i, ref in enumerate(refs):
            if re.match(r"#/tables/\d+", ref):
                table_ref = ref
                # Skip if we already have a caption for this table
                if table_ref in table_title_mapping:
                    continue

                preceding_text_ref = refs[i - 1] if i > 0 else None

                title_text = "Unknown Title"
                if preceding_text_ref and re.match(r"#/texts/\d+", preceding_text_ref):
                    texts = self.doc_json.get("texts", [])
                    title_text = next(
                        (text["text"] for text in texts if text["self_ref"] == preceding_text_ref),
                        "Unknown Title"
                    )

                table_title_mapping[table_ref] = {"title": title_text}

        return table_title_mapping

    def _extract_table_data(self) -> Dict[str, Dict[str, Any]]:
        """
        Extract table data with page numbers and convert to DataFrames.
        """
        tables = self.doc_json.get("tables", [])
        extracted_tables = {}

        if not tables:
            print("No tables found in the document JSON data")
            return extracted_tables

        for table_index, table in enumerate(tables):
            try:
                table_ref = table.get("self_ref", f"#/tables/{table_index}")
                table_cells = table.get("data", {}).get("table_cells", [])
                page_no = table.get("prov", [{}])[0].get("page_no", 1)

                # Process table dimensions
                max_row = max(cell.get("end_row_offset_idx", 0) for cell in table_cells) + 1
                max_col = max(cell.get("end_col_offset_idx", 0) for cell in table_cells) + 1

                # Create and fill table matrix
                table_matrix = [["" for _ in range(max_col)] for _ in range(max_row)]

                for cell in table_cells:
                    start_row = cell.get("start_row_offset_idx", 0)
                    start_col = cell.get("start_col_offset_idx", 0)
                    end_row = cell.get("end_row_offset_idx", start_row)
                    end_col = cell.get("end_col_offset_idx", start_col)
                    text = str(cell.get("text", "")).strip()

                    for row in range(start_row, end_row + 1):
                        for col in range(start_col, end_col + 1):
                            if row < max_row and col < max_col:
                                table_matrix[row][col] = text

                # Convert to DataFrame
                df = pd.DataFrame(table_matrix, dtype=str)
                df = df.dropna(axis=1, how="all")

                extracted_tables[table_ref] = {"df": df, "page_no": page_no}

            except Exception as e:
                print(f"❌ Error processing table {table_index}: {e}")
                continue

        return extracted_tables

    def _get_total_pages(self) -> int:
        """Get total number of pages in the document."""
        return len(self.doc_json.get("pages", {}))

    def _find_table_title(self, table_ref: str, page_no: int) -> str:
        """Find title for a specific table."""
        # First try to find caption in the table object
        title = self._find_caption_in_table(table_ref)

        # If no caption found, try to find title in regular text references
        if not title:
            title = self._find_title_in_refs(table_ref)

        # If still no title found and this is from image-based extraction
        if not title:
            title = f"Table on Page {page_no}"

        return title or "Unknown Title"

    def _find_caption_in_table(self, table_ref: str) -> Optional[str]:
        """Find caption directly from the table object."""
        tables = self.doc_json.get("tables", [])

        for table in tables:
            if table.get("self_ref") == table_ref:
                # Check if table has captions
                captions = table.get("captions", [])
                if captions:
                    # Get the first caption reference
                    caption_ref = captions[0].get("$ref") if isinstance(captions[0], dict) else None
                    if caption_ref:
                        # Find the caption text
                        texts = self.doc_json.get("texts", [])
                        for text in texts:
                            if text.get("self_ref") == caption_ref:
                                return text.get("text", "")
                break

        return None

    def _find_title_in_refs(self, table_ref: str) -> Optional[str]:
        """Find table title from text references."""
        body_children = self.doc_json.get("body", {}).get("children", [])
        refs = [child["$ref"] for child in body_children if "$ref" in child]

        try:
            table_idx = refs.index(table_ref)
            if table_idx > 0:
                prev_ref = refs[table_idx - 1]
                if prev_ref.startswith("#/texts/"):
                    texts = self.doc_json.get("texts", [])
                    for text in texts:
                        if text["self_ref"] == prev_ref:
                            return text.get("text", "")
        except ValueError:
            pass

        return None

    def _map_titles_to_tables(self) -> Dict[str, Dict[str, Any]]:
        """
        Map table titles to their corresponding data.

        Returns:
            Dict containing mapped table data with titles
        """
        table_title_mapping = self._find_table_titles()
        extracted_tables = self._extract_table_data()

        mapped_tables = {}

        for table_ref, data in extracted_tables.items():
            title = table_title_mapping.get(table_ref, {}).get("title", "Unknown Title")
            mapped_tables[table_ref] = {
                "title": title,
                "df": data["df"],
                "page_no": data["page_no"]
            }

        return mapped_tables

    def _get_table_coordinates_and_page_size(self, table_ref: str) -> Tuple[Dict, Dict]:
        """
        Extract table coordinates and page size from the input JSON.

        Args:
            table_ref: Reference ID of the table

        Returns:
            Tuple containing coordinates dict and page size dict
        """
        tables = self.doc_json.get("tables", [])
        pages = self.doc_json.get("pages", {})

        # Default empty values
        coordinates = {}
        page_size = {}

        # Find matching table
        for table in tables:
            if table.get("self_ref") == table_ref:
                # Get table coordinates from provenance info
                prov = table.get("prov", [{}])[0]
                page_no = prov.get("page_no")
                bbox = prov.get("bbox", {})

                if bbox:
                    coordinates = {
                        "left": bbox.get("l", 0),
                        "top": bbox.get("t", 0),
                        "right": bbox.get("r", 0),
                        "bottom": bbox.get("b", 0),
                        "coord_origin": bbox.get("coord_origin", "BOTTOMLEFT")
                    }

                # Get page size if available
                if str(page_no) in pages:
                    page_info = pages[str(page_no)]
                    page_size = {
                        "width": page_info.get("size", {}).get("width", 0),
                        "height": page_info.get("size", {}).get("height", 0)
                    }
                break

        return coordinates, page_size

    def _get_empty_table_pages(self) -> List[int]:
        """Get list of pages that have tables with empty cell data."""
        empty_table_pages = set()
        tables = self.doc_json.get("tables", [])

        for table in tables:
            table_cells = table.get("data", {}).get("table_cells", [])
            if table.get("prov"):  # Check if prov exists
                page_no = table.get("prov", [{}])[0].get("page_no")
                if page_no and not table_cells:  # If page number exists and table cells are empty
                    empty_table_pages.add(page_no)

        return sorted(list(empty_table_pages))  # Return sorted list of page numbers

    def _extract_table_metadata(self, page_no: int) -> Dict:
        """Extract table metadata from input JSON for a specific page."""
        metadata = {
            "title": f"Table on Page {page_no}",
            "coordinates": {},
            "page_size": {}
        }

        # Get page size
        pages = self.doc_json.get("pages", {})
        if str(page_no) in pages:
            size_info = pages[str(page_no)].get("size", {})
            metadata["page_size"] = {
                "width": size_info.get("width", 0),
                "height": size_info.get("height", 0)
            }

        # Find table in input JSON to get coordinates and caption
        tables = self.doc_json.get("tables", [])
        for table in tables:
            prov = table.get("prov", [{}])[0]
            if prov.get("page_no") == page_no:
                # Get coordinates
                bbox = prov.get("bbox", {})
                if bbox:
                    metadata["coordinates"] = {
                        "left": bbox.get("l", 0),
                        "top": bbox.get("t", 0),
                        "right": bbox.get("r", 0),
                        "bottom": bbox.get("b", 0),
                        "coord_origin": bbox.get("coord_origin", "BOTTOMLEFT")
                    }

                # Check for caption
                captions = table.get("captions", [])
                if captions:
                    caption_ref = captions[0].get("$ref") if isinstance(captions[0], dict) else None
                    if caption_ref:
                        texts = self.doc_json.get("texts", [])
                        for text in texts:
                            if text.get("self_ref") == caption_ref:
                                metadata["title"] = text.get("text", metadata["title"])
                                break

                # Store the raw table data for structure extraction
                metadata["raw_table_data"] = table
                break

        return metadata

    def process_tables(self, doc_path: Optional[str] = None) -> Dict:
        """
        Process all tables in the document.
        
        Args:
            doc_path: Optional path to the document file
            
        Returns:
            Dictionary containing processed table data
        """
        table_data_dict = {"tables": {}}

        # First process regular tables
        mapped_tables = self._map_titles_to_tables()

        for table_index, (table_ref, data) in enumerate(mapped_tables.items(), start=1):
            try:
                if not data["df"].empty:  # Only process non-empty tables
                    title = data["title"]
                    df = data["df"]
                    page_no = data["page_no"]

                    coordinates, page_size = self._get_table_coordinates_and_page_size(table_ref)
                    table_dict = df.to_dict(orient="records")

                    # Remove empty last column if exists
                    if table_dict and all(not row.get(str(len(row)-1), "").strip() for row in table_dict):
                        for row in table_dict:
                            row.pop(str(len(row)-1), None)

                    table_key = f"table_{page_no}_{table_index}"
                    table_data_dict["tables"][table_key] = {
                        "page_number": page_no,
                        "title": title,
                        "table_data": table_dict,
                        "coordinates": coordinates,
                        "page_size": page_size
                    }
                    # Remove DataFrame from the output as it's not JSON serializable
                    if "df" in table_data_dict["tables"][table_key]:
                        del table_data_dict["tables"][table_key]["df"]
                    print(f"✓ Processed regular table on page {page_no}")

            except Exception as e:
                print(f"❌ Error processing regular table {table_index}: {str(e)}")
                continue

        # Then process empty tables using image-based extraction
        empty_pages = []
        if doc_path or self.doc_path:
            actual_doc_path = doc_path if doc_path else self.doc_path
            if os.path.exists(actual_doc_path):
                empty_pages = self._get_empty_table_pages()
                if empty_pages:
                    print(f"Found tables with empty cells on pages: {empty_pages}")
            else:
                print(f"Warning: Document file not found at path: {actual_doc_path}")
        else:
            print("Warning: No document path provided, skipping image-based extraction")

        # Process each page with empty tables
        for page_no in empty_pages:
            try:
                # First extract metadata from input JSON for this table
                table_metadata = self._extract_table_metadata(page_no)

                # Then use image-based extraction to get table data
                print(f"Attempting image-based extraction for page {page_no}...")
                # Pass the table coordinates to the image table handler
                coordinates = table_metadata.get("coordinates", {})
                actual_doc_path = doc_path if doc_path else self.doc_path
                if coordinates:
                    print(f"Using table coordinates for extraction: {coordinates}")
                    image_table_result = handle_image_table(actual_doc_path, page_no, coordinates)
                else:
                    print("No coordinates available, processing entire page")
                    image_table_result = handle_image_table(actual_doc_path, page_no)

                # Get the output folder path (same as where table_data.json will be saved)
                if self.input_json_path:
                    output_folder = os.path.dirname(self.input_json_path)
                else:
                    # If input_json_path is None, use the save_path
                    output_folder = self.save_path

                # Ensure the output folder exists
                os.makedirs(output_folder, exist_ok=True)

                # Save image table result as JSON file
                if image_table_result:
                    # Create filename with page number
                    image_table_json_path = os.path.join(output_folder, f"image_table_page_{page_no}.json")

                    # Save image table result as JSON
                    with open(image_table_json_path, 'w', encoding='utf-8') as f:
                        json.dump(image_table_result, f, ensure_ascii=False, indent=2)

                    print(f"✓ Saved image table result to: {image_table_json_path}")
                else:
                    # If image table extraction failed, create a dynamic table structure
                    print("❌ Image table extraction failed, creating dynamic table structure")

                    # Extract table structure from metadata if available
                    table_structure = self._extract_table_structure_from_metadata(table_metadata)

                    if table_structure:
                        # Create a table with the extracted structure
                        dynamic_table = {
                            "tables": {
                                f"table_{page_no}_1": {
                                    "page_number": page_no,
                                    "title": table_metadata.get("title", f"Table on Page {page_no}"),
                                    "table_data": table_structure,
                                    "dimensions": {
                                        "rows": len(table_structure),
                                        "columns": len(table_structure[0]) if table_structure else 0
                                    },
                                    "coordinates": table_metadata.get("coordinates", {}),
                                    "page_size": table_metadata.get("page_size", {})
                                }
                            }
                        }

                        # Save the dynamic table to the output folder
                        dynamic_table_json_path = os.path.join(output_folder, f"dynamic_table_page_{page_no}.json")
                        with open(dynamic_table_json_path, 'w', encoding='utf-8') as f:
                            json.dump(dynamic_table, f, ensure_ascii=False, indent=2)

                        print(f"✓ Saved dynamic table to: {dynamic_table_json_path}")
                        # Use the dynamic table as the image table result
                        image_table_result = dynamic_table
                    else:
                        # If no structure could be extracted, create an empty table
                        print("❌ No table structure could be extracted from metadata")
                        image_table_result = None

                if image_table_result and image_table_result.get("tables"):
                    # Merge image-extracted tables with existing tables
                    for img_table_key, img_table_data in image_table_result["tables"].items():
                        if img_table_key not in table_data_dict["tables"]:
                            # Combine metadata with image-extracted table data
                            combined_data = {
                                "page_number": page_no,
                                "table_data": img_table_data.get("table_data", []),
                                "dimensions": img_table_data.get("dimensions", {})
                            }

                            # Add metadata from input JSON
                            combined_data["title"] = table_metadata.get("title", f"Image Table on Page {page_no}")
                            combined_data["coordinates"] = table_metadata.get("coordinates", {})
                            combined_data["page_size"] = table_metadata.get("page_size", {})

                            table_data_dict["tables"][img_table_key] = combined_data
                            print(f"✓ Added image-extracted table from page {page_no}")

            except Exception as e:
                print(f"❌ Error in image-based extraction for page {page_no}: {str(e)}")
                continue

        # Sort tables by page number and table index
        sorted_tables = dict(sorted(table_data_dict["tables"].items(),
                                  key=lambda x: (x[1]["page_number"],
                                               int(x[0].split("_")[-1]))))
        table_data_dict["tables"] = sorted_tables

        return table_data_dict
        
    def _extract_table_structure_from_metadata(self, table_metadata: Dict) -> List[Dict]:
        """
        Extract table structure from metadata without hardcoding values.

        Args:
            table_metadata: Dictionary containing table metadata

        Returns:
            List of dictionaries representing table rows
        """
        try:
            # Try to determine the header row from the table metadata
            title = table_metadata.get("title", "")
            header_row = {}

            # Analyze title to determine column headers
            if "sequence" in title.lower() or "RNA" in title:
                header_row = {"0": "Name", "1": "Sequence"}
            else:
                # Default generic header
                header_row = {"0": "Column 1", "1": "Column 2"}

            # Initialize with the determined header row
            table_structure = [header_row]

            # Try to extract structure from raw table data if available
            raw_table_data = table_metadata.get("raw_table_data", {})

            # Look for cells or content that might indicate table structure
            cells = raw_table_data.get("cells", [])
            if cells:
                # Group cells by row
                rows = {}
                for cell in cells:
                    row_idx = cell.get("row", 0)
                    col_idx = cell.get("col", 0)
                    text = cell.get("text", "")

                    if row_idx not in rows:
                        rows[row_idx] = {}

                    rows[row_idx][str(col_idx)] = text

                # Convert rows to list format
                for row_idx in sorted(rows.keys()):
                    if row_idx > 0:  # Skip header row which we already added
                        table_structure.append(rows[row_idx])

            # If we couldn't extract structure from cells, create a minimal structure
            if len(table_structure) <= 1:
                # Create a minimal structure with empty cells
                # Determine number of rows based on table size in the document
                coordinates = table_metadata.get("coordinates", {})
                if coordinates:
                    # Estimate number of rows based on table height
                    # This is a rough estimate - 20 pixels per row is a common value
                    top = coordinates.get("top", 0)
                    bottom = coordinates.get("bottom", 0)
                    height = abs(top - bottom)
                    estimated_rows = max(1, int(height / 20))

                    # Add empty rows to the structure
                    for i in range(estimated_rows):
                        table_structure.append({
                            "0": f"Row {i+1}",
                            "1": ""
                        })
                else:
                    # If no coordinates, add a few generic rows
                    for i in range(5):
                        table_structure.append({
                            "0": f"Row {i+1}",
                            "1": ""
                        })

            return table_structure

        except Exception as e:
            print(f"Error extracting table structure from metadata: {e}")
            # Return a minimal structure as fallback
            return [
                {"0": "Column 1", "1": "Column 2"},
                {"0": "Row 1", "1": ""},
                {"0": "Row 2", "1": ""},
                {"0": "Row 3", "1": ""}
            ]
