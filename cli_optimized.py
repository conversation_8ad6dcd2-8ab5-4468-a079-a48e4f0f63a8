"""
Optimized command-line interface for the MetaParse package.

This module provides an enhanced asynchronous implementation of the CLI
for faster document processing, especially with large batches of files.
Author : Anand Jadhav
Date : 2025-05-20
"""

import os
import time
import argparse
import asyncio
import glob
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ProcessPoolExecutor
import multiprocessing

from metaparse.core.pipeline import DocExtractionPipeline


def find_document_files_recursive(folder_path: str) -> List[str]:
    """
    Recursively find all PDF and DOCX files in a folder and its subfolders.

    Args:
        folder_path: Path to the folder to search

    Returns:
        List of file paths to PDF and DOCX files
    """
    document_files = []

    # Check if the folder exists
    if not os.path.isdir(folder_path):
        print(f"Warning: {folder_path} is not a valid directory")
        return document_files

    # Walk through the directory tree
    for root, _, files in os.walk(folder_path):
        for file in files:
            # Check if the file has a supported extension
            if file.lower().endswith(('.pdf', '.docx')):
                # Get the full path to the file
                file_path = os.path.join(root, file)
                document_files.append(file_path)

    return document_files


def create_output_folders(doc_files: List[str], output_base_folder: str, input_folder: str) -> List[str]:
    """
    Create output folders for each document with proper naming based on subfolder structure.

    Args:
        doc_files: List of document file paths
        output_base_folder: Base folder for output
        input_folder: Input folder containing the documents

    Returns:
        List of output folder paths
    """
    save_paths = []
    for doc_file in doc_files:
        # Extract filename without extension
        doc_filename = os.path.basename(doc_file)
        doc_name = os.path.splitext(doc_filename)[0]

        # Create a unique output folder name
        # If the file is in a subfolder, include the subfolder structure in the output folder name
        if os.path.dirname(doc_file) != os.path.normpath(input_folder):
            # Get the relative path from the input folder to the file's directory
            rel_path = os.path.relpath(os.path.dirname(doc_file), input_folder)
            # Replace path separators with underscores to create a valid folder name
            subfolder_part = rel_path.replace(os.path.sep, '_')
            # Combine the subfolder part with the document name
            output_folder_name = f"{subfolder_part}_{doc_name}"
        else:
            output_folder_name = doc_name

        output_folder = os.path.join(output_base_folder, output_folder_name)
        save_paths.append(output_folder)

    return save_paths


def extract_doc_data_sync(doc_path: str, output_folder: str, optimization_level: str = 'balanced', use_cache: bool = True) -> Dict[str, Any]:
    """
    Extract data from a single document file synchronously.

    Args:
        doc_path: Path to the document file
        output_folder: Path to the output folder
        optimization_level: Optimization level ('speed', 'balanced', or 'quality')
        use_cache: Whether to use caching

    Returns:
        Dictionary with extraction results
    """
    try:
        pipeline = DocExtractionPipeline(
            doc_path,
            output_folder,
            optimization_level=optimization_level,
            use_cache=use_cache
        )
        pipeline.run_pipeline()
        return {
            'doc_path': doc_path,
            'output_folder': output_folder,
            'success': True
        }
    except Exception as e:
        print(f"❌ Error processing {os.path.basename(doc_path)}: {e}")
        return {
            'doc_path': doc_path,
            'output_folder': output_folder,
            'success': False,
            'error': str(e)
        }


async def extract_doc_data_async(doc_path: str, output_folder: str, optimization_level: str = 'balanced', use_cache: bool = True) -> Dict[str, Any]:
    """
    Extract data from a single document file asynchronously.

    Args:
        doc_path: Path to the document file
        output_folder: Path to the output folder
        optimization_level: Optimization level ('speed', 'balanced', or 'quality')
        use_cache: Whether to use caching

    Returns:
        Dictionary with extraction results
    """
    # Run CPU-intensive document processing in a process pool
    loop = asyncio.get_running_loop()
    with ProcessPoolExecutor(max_workers=1) as pool:
        return await loop.run_in_executor(
            pool,
            extract_doc_data_sync,
            doc_path,
            output_folder,
            optimization_level,
            use_cache
        )


async def process_batch_with_semaphore(doc_files: List[str], output_folders: List[str],
                                      optimization_level: str = 'speed',
                                      concurrency_limit: int = None) -> List[Dict[str, Any]]:
    """
    Process multiple documents with controlled concurrency using semaphores.

    Args:
        doc_files: List of document file paths
        output_folders: List of output folder paths
        optimization_level: Optimization level ('speed', 'balanced', or 'quality')
        concurrency_limit: Maximum number of concurrent tasks

    Returns:
        List of dictionaries with extraction results
    """
    if concurrency_limit is None:
        # Default to number of CPU cores
        concurrency_limit = max(1, multiprocessing.cpu_count())

    print(f"Processing with concurrency limit: {concurrency_limit}")
    semaphore = asyncio.Semaphore(concurrency_limit)
    results = []

    async def process_with_semaphore(doc_path, output_folder, index):
        async with semaphore:
            print(f"Processing {index+1}/{len(doc_files)}: {os.path.basename(doc_path)}")
            start_time = time.time()
            result = await extract_doc_data_async(doc_path, output_folder, optimization_level)
            elapsed = time.time() - start_time
            print(f"✅ Completed {index+1}/{len(doc_files)}: {os.path.basename(doc_path)} in {elapsed:.2f} seconds")
            return result

    # Create tasks for all documents
    tasks = [
        process_with_semaphore(doc_file, output_folder, i)
        for i, (doc_file, output_folder) in enumerate(zip(doc_files, output_folders))
    ]

    # Process documents and collect results as they complete
    for completed_task in asyncio.as_completed(tasks):
        result = await completed_task
        results.append(result)

        # Print progress
        successful = sum(1 for r in results if r.get('success', False))
        print(f"Progress: {len(results)}/{len(doc_files)} files processed, {successful} successful")

    return results


def generate_summary_sheet(processed_docs: List[Dict[str, Any]], output_base_folder: str, input_path: str, is_batch_mode: bool = False) -> str:
    """
    Generate a summary Excel sheet with details about each processed document.

    Args:
        processed_docs: List of dictionaries with processing results
        output_base_folder: Base folder where output is stored
        input_path: Path to the input file or folder
        is_batch_mode: Whether processing was done in batch mode

    Returns:
        Path to the generated summary sheet
    """
    summary_data = []

    for doc_result in processed_docs:
        doc_path = doc_result.get('doc_path', '')
        output_folder = doc_result.get('output_folder', '')
        success = doc_result.get('success', False)

        if not success or not os.path.exists(output_folder):
            # Skip failed documents
            continue

        doc_name = os.path.basename(doc_path)

        ## Count JSON files
        json_files = glob.glob(os.path.join(output_folder, "*.json"))
        num_json_files = len(json_files)

        ## Count table Excel files
        table_files = glob.glob(os.path.join(output_folder, "table_*.xlsx"))
        num_tables = len(table_files)

        ## Count image files 
        images_folder = os.path.join(output_folder, "images")
        if os.path.exists(images_folder):
            image_files = glob.glob(os.path.join(images_folder, "*.png"))
            num_images = len(image_files)
        else:
            # Fallback to check for images in the main output folder
            image_files = glob.glob(os.path.join(output_folder, "*.png"))
            num_images = len(image_files)

        # Add to summary data
        summary_data.append({
            'document_name': doc_name,
            'number_of_json_data_files': num_json_files,
            'number_of_tables': num_tables,
            'number_of_figures': num_images
        })

    # Check if we have any data to include in the summary
    if not summary_data:
        print("⚠️ No valid document data found for summary sheet.")
        return None

    # Create DataFrame
    df = pd.DataFrame(summary_data)

    # Determine summary sheet name
    if is_batch_mode:
        # Use input folder name for batch mode
        input_folder_name = os.path.basename(os.path.normpath(input_path))
        summary_file_name = f"{input_folder_name}_summary.xlsx"
    else:
        # Use input document name for single file mode
        input_doc_name = os.path.basename(input_path)
        input_doc_name_without_ext = os.path.splitext(input_doc_name)[0]
        summary_file_name = f"{input_doc_name_without_ext}_summary.xlsx"

    # Create summary file path (always in the output folder)
    summary_file_path = os.path.join(output_base_folder, summary_file_name)

    # Save to Excel
    df.to_excel(summary_file_path, index=False)

    print(f"✅ Summary sheet generated at: {summary_file_path}")
    return summary_file_path


async def main_async():
    """Main entry point for the optimized asynchronous CLI."""
    print("MetaParse Optimized Extraction Process Started...")

    start = time.time()
    parser = argparse.ArgumentParser(description="Optimized Document Data Extraction Tool")
    parser.add_argument("--input", "-i", required=True, help="Input document file or folder")
    parser.add_argument("--output", "-o", required=True, help="Output base folder")
    parser.add_argument("--workers", "-w", type=int, default=None, help="Number of concurrent workers")
    parser.add_argument("--optimization", "-opt", choices=["speed", "balanced", "quality"],
                        default="speed", help="Optimization level")
    parser.add_argument("--no-cache", action="store_true", help="Disable caching")

    args = parser.parse_args()

    # Determine if input is a file or directory
    if os.path.isdir(args.input):
        # Batch processing mode
        input_folder = args.input
        output_base_folder = args.output

        # Recursively collect all document files from the folder and its subfolders
        doc_files = find_document_files_recursive(input_folder)

        if not doc_files:
            print("No PDF or DOCX files found in the specified folder or its subfolders.")
            return 1

        print(f"Found {len(doc_files)} document files to process.")

        # Print the list of files that will be processed
        print("Files to be processed:")
        for i, file_path in enumerate(doc_files, 1):
            print(f"  {i}. {file_path}")

        # Create output folders
        output_folders = create_output_folders(doc_files, output_base_folder, input_folder)

        # Process documents with enhanced async processing
        results = await process_batch_with_semaphore(
            doc_files,
            output_folders,
            optimization_level=args.optimization,
            concurrency_limit=args.workers
        )

        # Summary
        successful = sum(1 for r in results if r.get('success', False))
        print(f"\nSuccessfully processed: {successful}/{len(results)} files")

        # Generate summary sheet
        generate_summary_sheet(results, output_base_folder, args.input, is_batch_mode=True)

    else:
        # Single file processing mode
        doc_file = args.input

        if not os.path.exists(doc_file):
            print(f"Document file not found: {doc_file}")
            return 1

        # Extract filename without extension for any supported document type
        doc_filename = os.path.basename(doc_file)
        doc_name = os.path.splitext(doc_filename)[0]
        output_folder = os.path.join(args.output, doc_name)

        # Process single document
        result = await extract_doc_data_async(
            doc_file,
            output_folder,
            optimization_level=args.optimization,
            use_cache=not args.no_cache
        )

        # Generate summary sheet for single file
        generate_summary_sheet([result], args.output, args.input, is_batch_mode=False)

    print(f"✅✅ Complete Extraction Time = {round((time.time() - start), 2)} Seconds. ✅✅")
    return 0


def main():
    """Entry point for the command-line interface."""
    return asyncio.run(main_async())


if __name__ == "__main__":
    import sys
    sys.exit(main())
 

# python cli_optimized.py --input "C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder" --output "C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder" --optimization speed  --workers 4