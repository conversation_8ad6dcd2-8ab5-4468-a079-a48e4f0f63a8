#!/usr/bin/env python3
"""
Debug script to trace linebreak preservation step by step.

This script will help us understand exactly where linebreaks are being lost
in the extraction process.

Author: <PERSON>hav
Date: 2025-01-27
"""

import os
import json
import sys
from pathlib import Path

def debug_docling_extraction():
    """Debug the Docling extraction process step by step."""
    
    print("🔍 Debugging Linebreak Preservation - Step by Step")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("metaparse/extractors/doc_extractor_linebreak.py").exists():
        print("❌ Error: Please run this script from the MetaParse project root directory")
        return False
    
    try:
        # Import required modules
        sys.path.insert(0, '.')
        from metaparse.extractors.doc_extractor_linebreak import DocExtractor
        
        # Your PDF file
        pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
        
        if not Path(pdf_file).exists():
            print(f"❌ PDF file not found: {pdf_file}")
            return False
        
        print(f"📄 Debugging with: {Path(pdf_file).name}")
        
        # Step 1: Create extractor and check configuration
        print(f"\n🔍 Step 1: Creating DocExtractor and checking configuration")
        extractor = DocExtractor(
            pdf_file,
            optimization_level='quality',
            use_cache=False
        )
        
        # Check configuration
        text_settings = extractor.config.settings.get('text_processing', {})
        print(f"   preserve_linebreaks: {text_settings.get('preserve_linebreaks', 'NOT SET')}")
        print(f"   linebreak_character: '{text_settings.get('linebreak_character', 'NOT SET')}'")
        print(f"   paragraph_separator: '{text_settings.get('paragraph_separator', 'NOT SET')}'")
        
        # Step 2: Get raw Docling output
        print(f"\n🔍 Step 2: Getting raw Docling output")
        result = extractor.converter.convert(extractor.doc_path)
        raw_dict = result.document.export_to_dict()
        
        print(f"   Raw document sections: {list(raw_dict.keys())}")
        
        # Check raw pages
        if 'pages' in raw_dict:
            print(f"   Raw pages count: {len(raw_dict['pages'])}")
            first_page_key = list(raw_dict['pages'].keys())[0]
            first_page_raw = raw_dict['pages'][first_page_key]
            
            if 'text' in first_page_raw:
                raw_text = first_page_raw['text']
                print(f"   Raw text length: {len(raw_text)} chars")
                print(f"   Raw linebreaks: {raw_text.count(chr(10))} (\\n)")
                print(f"   Raw text sample: '{raw_text[:100]}...'")
            else:
                print(f"   ⚠️ No 'text' field in raw page")
        
        # Check raw texts section
        if 'texts' in raw_dict:
            print(f"   Raw texts count: {len(raw_dict['texts'])}")
            if raw_dict['texts']:
                first_text = raw_dict['texts'][0]
                if 'text' in first_text:
                    text_content = first_text['text']
                    print(f"   First text item: '{text_content[:50]}...'")
                    print(f"   First text linebreaks: {text_content.count(chr(10))}")
        
        # Step 3: Apply linebreak processing
        print(f"\n🔍 Step 3: Applying linebreak processing")
        processed_dict = extractor._process_text_with_linebreaks(raw_dict.copy())
        
        # Check processed pages
        if 'pages' in processed_dict:
            first_page_processed = processed_dict['pages'][first_page_key]
            
            if 'text' in first_page_processed:
                processed_text = first_page_processed['text']
                print(f"   Processed text length: {len(processed_text)} chars")
                print(f"   Processed linebreaks: {processed_text.count(chr(10))} (\\n)")
                print(f"   Processed text sample: '{processed_text[:100]}...'")
                
                # Check processing flags
                print(f"   linebreaks_preserved: {first_page_processed.get('linebreaks_preserved', 'NOT SET')}")
                print(f"   elements_processed: {first_page_processed.get('elements_processed', 'NOT SET')}")
                print(f"   paragraph_count: {first_page_processed.get('paragraph_count', 'NOT SET')}")
            else:
                print(f"   ⚠️ No 'text' field in processed page")
        
        # Step 4: Check document structure for debugging
        print(f"\n🔍 Step 4: Analyzing document structure")
        
        # Check if we have text elements with position data
        if 'texts' in raw_dict:
            print(f"   Analyzing {len(raw_dict['texts'])} text elements...")
            
            elements_with_bbox = 0
            elements_with_prov = 0
            
            for i, text_item in enumerate(raw_dict['texts'][:5]):  # Check first 5
                print(f"   Text element {i+1}:")
                print(f"     text: '{text_item.get('text', 'NO TEXT')[:30]}...'")
                
                if 'prov' in text_item and text_item['prov']:
                    elements_with_prov += 1
                    prov = text_item['prov'][0]
                    print(f"     page: {prov.get('page_no', 'NO PAGE')}")
                    
                    if 'bbox' in prov:
                        elements_with_bbox += 1
                        bbox = prov['bbox']
                        print(f"     bbox: t={bbox.get('t', 'N/A')}, b={bbox.get('b', 'N/A')}, l={bbox.get('l', 'N/A')}, r={bbox.get('r', 'N/A')}")
                    else:
                        print(f"     bbox: NOT FOUND")
                else:
                    print(f"     prov: NOT FOUND")
            
            print(f"   Elements with prov: {elements_with_prov}/{len(raw_dict['texts'])}")
            print(f"   Elements with bbox: {elements_with_bbox}/{len(raw_dict['texts'])}")
        
        # Step 5: Test manual linebreak insertion
        print(f"\n🔍 Step 5: Testing manual linebreak insertion")
        
        if 'pages' in raw_dict:
            first_page_raw = raw_dict['pages'][first_page_key]
            if 'text' in first_page_raw:
                original_text = first_page_raw['text']
                
                # Try simple sentence splitting
                sentences = original_text.split('. ')
                if len(sentences) > 1:
                    manual_linebreak_text = '.\n'.join(sentences)
                    print(f"   Manual linebreak insertion:")
                    print(f"   Original: '{original_text[:100]}...'")
                    print(f"   With linebreaks: '{manual_linebreak_text[:100]}...'")
                    print(f"   Linebreaks added: {manual_linebreak_text.count(chr(10))}")
                else:
                    print(f"   ⚠️ Could not split into sentences")
        
        # Step 6: Save debug output
        print(f"\n🔍 Step 6: Saving debug output")
        
        debug_output = {
            'raw_docling_output': raw_dict,
            'processed_output': processed_dict,
            'configuration': extractor.config.settings,
            'debug_info': {
                'raw_text_sample': raw_dict['pages'][first_page_key]['text'][:200] if 'pages' in raw_dict and first_page_key in raw_dict['pages'] and 'text' in raw_dict['pages'][first_page_key] else 'NO TEXT',
                'processed_text_sample': processed_dict['pages'][first_page_key]['text'][:200] if 'pages' in processed_dict and first_page_key in processed_dict['pages'] and 'text' in processed_dict['pages'][first_page_key] else 'NO TEXT',
                'raw_linebreaks': raw_dict['pages'][first_page_key]['text'].count('\n') if 'pages' in raw_dict and first_page_key in raw_dict['pages'] and 'text' in raw_dict['pages'][first_page_key] else 0,
                'processed_linebreaks': processed_dict['pages'][first_page_key]['text'].count('\n') if 'pages' in processed_dict and first_page_key in processed_dict['pages'] and 'text' in processed_dict['pages'][first_page_key] else 0
            }
        }
        
        debug_file = Path("debug_linebreak_output.json")
        with open(debug_file, 'w', encoding='utf-8') as f:
            json.dump(debug_output, f, indent=2, ensure_ascii=False)
        
        print(f"   Debug output saved to: {debug_file}")
        
        # Summary
        print(f"\n📊 Debug Summary:")
        print(f"   Raw Docling linebreaks: {debug_output['debug_info']['raw_linebreaks']}")
        print(f"   Processed linebreaks: {debug_output['debug_info']['processed_linebreaks']}")
        
        if debug_output['debug_info']['raw_linebreaks'] == 0:
            print(f"   🔍 ISSUE: Docling itself is not providing linebreaks!")
            print(f"   💡 Solution: We need to create linebreaks from document structure")
        elif debug_output['debug_info']['processed_linebreaks'] == 0:
            print(f"   🔍 ISSUE: Our processing is removing linebreaks!")
            print(f"   💡 Solution: Fix the linebreak processing logic")
        else:
            print(f"   ✅ Linebreaks are being preserved correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debugging function."""
    
    print("🚀 Linebreak Preservation Debugging")
    print("=" * 40)
    
    print("\n💡 This script will trace the linebreak preservation process")
    print("   step by step to identify where linebreaks are being lost.")
    
    success = debug_docling_extraction()
    
    if success:
        print(f"\n🎯 Debugging completed!")
        print(f"\n📚 Check the debug output:")
        print("   - debug_linebreak_output.json (detailed analysis)")
        print("   - Look at the debug summary above")
        
        print(f"\n🔧 Next steps based on findings:")
        print("   1. If raw Docling has no linebreaks: Need to create from structure")
        print("   2. If processing removes linebreaks: Fix processing logic")
        print("   3. If linebreaks exist: Check JSON serialization")
        
    else:
        print(f"\n❌ Debugging failed. Please check the error messages above.")
    
    print(f"\n✨ Debugging completed!")

if __name__ == "__main__":
    main()
