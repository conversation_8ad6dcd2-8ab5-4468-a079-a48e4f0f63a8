"""
Document processing modules for the MetaParse package.

This module contains classes and functions for processing extracted document content,
such as tables, images, and text segmentation.
"""

# Import processor components for easier access
from metaparse.processors.doc_segmenter import DocSegmenter
from metaparse.processors.image_processor import ImageExtractor
from metaparse.processors.table_processor import TableProcessor
from metaparse.processors.table_image_handler import handle_image_table

# Define what's available when importing from this module
__all__ = ['DocSegmenter', 'ImageExtractor', 'TableProcessor', 'handle_image_table']
