#!/usr/bin/env python3
"""
Test the markdown-based approach for linebreak preservation.

This script tests whether using <PERSON><PERSON>'s markdown export
successfully preserves linebreaks in the JSON output.

Author: <PERSON> J<PERSON>hav
Date: 2025-01-27
"""

import json
from pathlib import Path
from simple_markdown_extractor import extract_pdf_with_markdown_linebreaks


def test_markdown_approach():
    """Test the markdown-based linebreak preservation."""
    
    print("Testing Markdown-Based Linebreak Preservation")
    print("=" * 45)
    
    # Configuration
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    output_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder\test_markdown_approach.json"
    
    # Check if PDF exists
    if not Path(pdf_file).exists():
        print("❌ PDF file not found!")
        return False
    
    try:
        # Extract using markdown approach
        print("🔄 Extracting PDF using markdown approach...")
        data = extract_pdf_with_markdown_linebreaks(pdf_file, output_file)
        
        print(f"✅ Extraction completed: {output_file}")
        
        # Analyze results
        analyze_markdown_results(data, output_file)
        
        return True
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_markdown_results(data, output_file):
    """Analyze the markdown extraction results."""
    
    print(f"\n📊 Markdown Approach Analysis:")
    print("-" * 30)
    
    # Basic file info
    file_size = Path(output_file).stat().st_size
    print(f"📁 Output file size: {file_size:,} bytes")
    
    # Check structure
    print(f"📋 JSON sections: {list(data.keys())}")
    
    if 'pages' in data and data['pages']:
        print(f"📄 Pages: {len(data['pages'])}")
        
        # Analyze first page
        first_page = list(data['pages'].values())[0]
        
        if 'text' in first_page:
            text = first_page['text']
            
            # Count elements
            total_chars = len(text)
            linebreak_count = text.count('\n')
            word_count = len(text.split())
            
            print(f"\n📝 First Page Text Analysis:")
            print(f"   Total characters: {total_chars:,}")
            print(f"   Word count: {word_count:,}")
            print(f"   Linebreaks (\\n): {linebreak_count}")
            
            if linebreak_count > 0:
                print(f"   ✅ SUCCESS: Markdown approach preserved {linebreak_count} linebreaks!")
                
                # Calculate density
                linebreak_density = (linebreak_count / total_chars) * 100 if total_chars > 0 else 0
                print(f"   📊 Linebreak density: {linebreak_density:.2f}%")
                
                # Show sample with visible linebreaks
                print(f"\n📖 Sample Text (first 300 characters):")
                sample = text[:300]
                
                # Replace \n with visible representation for display
                display_sample = sample.replace('\n', '\\n\n   ')
                print(f"   '{display_sample}...'")
                
                # Show actual lines
                lines = text.split('\n')
                print(f"\n📋 First 5 lines:")
                for i, line in enumerate(lines[:5], 1):
                    print(f"   Line {i}: '{line.strip()}'")
                
                # Check if this matches your expected format
                print(f"\n🎯 Format Check:")
                if "prostate\n" in text.lower():
                    print(f"   ✅ Found 'prostate\\n' pattern - matches your example!")
                else:
                    print(f"   ⚠️ 'prostate\\n' pattern not found")
                
                if "cancer by" in text.lower():
                    print(f"   ✅ Found 'cancer by' text")
                else:
                    print(f"   ⚠️ 'cancer by' text not found")
                
            else:
                print(f"   ❌ ISSUE: No linebreaks found even with markdown approach")
            
            # Check for source marker
            if first_page.get('source') == 'markdown_with_linebreaks':
                print(f"   ✅ Source marked as markdown")
            
        else:
            print("⚠️ No text found in first page")
    
    else:
        print("❌ No pages found in data")
    
    # Check other sections
    other_sections = ['texts', 'tables', 'pictures']
    for section in other_sections:
        if section in data and data[section]:
            count = len(data[section])
            print(f"📋 {section.capitalize()}: {count} items")


def compare_with_expected():
    """Show comparison with your expected format."""
    
    print(f"\n🎯 Expected vs Actual Comparison:")
    print("-" * 35)
    
    expected = '''Prognostic value of lncRNA LINC01018 in prostate
cancer by regulating miR-182-5p (The role of
LINC01018 in prostate cancer)

'''
    
    print(f"Expected format:")
    print(f"'{expected}'")
    
    print(f"Expected linebreaks: {expected.count(chr(10))}")
    print(f"Expected pattern: Text breaks at 'prostate\\n' and 'of\\n'")


def main():
    """Main function."""
    
    print("🚀 Markdown Approach Test")
    print("=" * 25)
    
    print("\n💡 This test uses Docling's markdown export to preserve linebreaks")
    print("   and converts it to the JSON structure you need.")
    
    success = test_markdown_approach()
    
    if success:
        print(f"\n🎉 Markdown approach test completed!")
        
        compare_with_expected()
        
        print(f"\n📚 Next steps:")
        print("✅ Check the JSON file to see the preserved linebreaks")
        print("✅ Compare with your expected format")
        print("✅ If this works, use simple_markdown_extractor.py for your needs")
        
    else:
        print(f"\n❌ Markdown approach test failed.")
    
    print(f"\n✨ Test completed!")


if __name__ == "__main__":
    main()
