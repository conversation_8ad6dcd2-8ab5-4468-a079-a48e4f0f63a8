#!/usr/bin/env python3
"""
Test script for the updated main block with your specific PDF file and output folder.

This script tests the enhanced DocExtractor with your specified paths:
- PDF file: C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf
- Output folder: C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder

Author: Anand Jadhav
Date: 2025-01-27
"""

import os
import sys
import subprocess
from pathlib import Path

def test_default_paths():
    """Test the main block with default paths."""
    print("🧪 Testing Main Block with Default Paths")
    print("-" * 50)
    
    # Your specified paths
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    output_folder = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder"
    
    print(f"📁 Expected PDF file: {pdf_file}")
    print(f"📁 Expected output folder: {output_folder}")
    
    # Check if PDF file exists
    if Path(pdf_file).exists():
        print("✅ PDF file exists")
    else:
        print("❌ PDF file not found")
        print("💡 Please ensure the PDF file exists at the specified location")
        return False
    
    # Test running the main block without arguments (should use defaults)
    try:
        print("\n🔄 Running main block with default settings...")
        result = subprocess.run([
            sys.executable, 
            "metaparse/extractors/doc_extractor_linebreak.py"
        ], capture_output=True, text=True, timeout=120)  # 2 minute timeout
        
        if result.returncode == 0:
            print("✅ Main block executed successfully with default paths")
            print("\n📋 Output preview:")
            print(result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout)
            
            # Check if output file was created
            expected_output = Path(output_folder) / "04760587J.article.002_extracted.json"
            if expected_output.exists():
                print(f"✅ Output file created: {expected_output}")
                
                # Check file size
                file_size = expected_output.stat().st_size
                print(f"📊 Output file size: {file_size:,} bytes")
                
                if file_size > 1000:  # At least 1KB
                    print("✅ Output file appears to contain data")
                else:
                    print("⚠️ Output file seems small, may not contain expected data")
                    
            else:
                print(f"⚠️ Expected output file not found: {expected_output}")
                
        else:
            print("❌ Main block execution failed")
            print("Error output:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("⏰ Main block execution timed out (>2 minutes)")
        print("💡 This might indicate the PDF is large or processing is slow")
    except Exception as e:
        print(f"❌ Error running main block: {e}")
        
    return True

def test_demo_mode():
    """Test the demo mode functionality."""
    print("\n🧪 Testing Demo Mode")
    print("-" * 30)
    
    try:
        result = subprocess.run([
            sys.executable, 
            "metaparse/extractors/doc_extractor_linebreak.py", 
            "--demo"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Demo mode works correctly")
            print("📋 Demo output preview:")
            print(result.stdout[:300] + "..." if len(result.stdout) > 300 else result.stdout)
        else:
            print("❌ Demo mode failed")
            print("Error:", result.stderr)
            
    except Exception as e:
        print(f"❌ Error running demo: {e}")

def test_custom_settings():
    """Test with custom linebreak settings."""
    print("\n🧪 Testing Custom Linebreak Settings")
    print("-" * 40)
    
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    
    if not Path(pdf_file).exists():
        print("⚠️ PDF file not found, skipping custom settings test")
        return
    
    try:
        print("🔄 Testing with pipe separators...")
        result = subprocess.run([
            sys.executable, 
            "metaparse/extractors/doc_extractor_linebreak.py",
            "--linebreak-char", " | ",
            "--paragraph-separator", " || "
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ Custom linebreak settings work")
            
            # Check if output file was created with custom settings
            output_folder = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder"
            expected_output = Path(output_folder) / "04760587J.article.002_extracted.json"
            
            if expected_output.exists():
                print("✅ Output file created with custom settings")
                
                # Read a sample to check for pipe characters
                try:
                    import json
                    with open(expected_output, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # Check first page for pipe characters
                    if 'pages' in data and data['pages']:
                        first_page = list(data['pages'].values())[0]
                        sample_text = first_page.get('text', '')[:200]
                        pipe_count = sample_text.count(' | ')
                        
                        print(f"🔍 Pipe separators found in sample: {pipe_count}")
                        if pipe_count > 0:
                            print("✅ Custom linebreak characters applied successfully")
                        else:
                            print("⚠️ Custom linebreak characters may not be applied")
                            
                except Exception as e:
                    print(f"⚠️ Could not analyze output file: {e}")
            else:
                print("⚠️ Output file not found")
                
        else:
            print("❌ Custom settings test failed")
            print("Error:", result.stderr[:200])
            
    except Exception as e:
        print(f"❌ Error testing custom settings: {e}")

def main():
    """Run all tests for your specific configuration."""
    print("🚀 Testing DocExtractor with Your Specific Paths")
    print("=" * 60)
    
    print("\n📋 Your Configuration:")
    print("   PDF file: C:\\Users\\<USER>\\Desktop\\Anand\\MetaParse\\pdf_folder\\04760587J.article.002.pdf")
    print("   Output folder: C:\\Users\\<USER>\\Desktop\\Anand\\MetaParse\\extracted_data_folder")
    
    # Check if we're in the right directory
    if not Path("metaparse/extractors/doc_extractor_linebreak.py").exists():
        print("\n❌ Error: Please run this script from the MetaParse project root directory")
        print("💡 Current directory should contain the 'metaparse' folder")
        return
    
    print("\n✅ Running from correct directory")
    
    # Run tests
    test_default_paths()
    test_demo_mode()
    test_custom_settings()
    
    print("\n🎯 Test Summary")
    print("-" * 30)
    print("✅ Default paths: Tested")
    print("✅ Demo mode: Tested")
    print("✅ Custom settings: Tested")
    
    print("\n📚 Next Steps:")
    print("1. Run the extractor with default settings:")
    print("   python metaparse/extractors/doc_extractor_linebreak.py")
    print("2. Try custom linebreak characters:")
    print("   python metaparse/extractors/doc_extractor_linebreak.py --linebreak-char '|'")
    print("3. Check the output folder for extracted JSON files")
    
    print("\n✨ Testing completed!")

if __name__ == "__main__":
    main()
