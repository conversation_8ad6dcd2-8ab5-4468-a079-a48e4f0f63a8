#!/usr/bin/env python3
"""
Pattern-based PDF extractor that tries to match original linebreak patterns.

This module analyzes text patterns to insert linebreaks where they
would naturally occur in the PDF layout.

Author: Anand Jadhav
Date: 2025-01-27
"""

import json
import re
from pathlib import Path
from docling.document_converter import DocumentConverter


def extract_pdf_with_pattern_linebreaks(pdf_path: str, output_path: str):
    """
    Extract PDF text with pattern-based linebreaks.
    
    Args:
        pdf_path: Path to PDF file
        output_path: Path to save JSON output
    """
    # Convert PDF using Docling
    converter = DocumentConverter()
    result = converter.convert(pdf_path)
    data = result.document.export_to_dict()
    
    # Add linebreaks based on text patterns
    add_pattern_based_linebreaks(data)
    
    # Save to JSON
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)


def add_pattern_based_linebreaks(data):
    """Add linebreaks based on text patterns."""
    
    # Process pages
    if 'pages' in data:
        for page in data['pages'].values():
            if 'text' in page:
                page['text'] = add_smart_linebreaks(page['text'])
    
    # Process text items
    if 'texts' in data:
        for item in data['texts']:
            if 'text' in item:
                item['text'] = add_smart_linebreaks(item['text'])


def add_smart_linebreaks(text):
    """
    Add linebreaks based on intelligent text analysis.
    
    This function tries to detect where linebreaks should occur based on:
    1. Line length patterns
    2. Word boundaries
    3. Common academic paper patterns
    """
    if not text or not isinstance(text, str):
        return text
    
    text = text.strip()
    if len(text) < 50:  # Don't process very short text
        return text
    
    # Method 1: Split long lines at natural break points
    result = split_at_natural_breaks(text)
    
    # Method 2: Handle title patterns (like your example)
    result = handle_title_patterns(result)
    
    return result


def split_at_natural_breaks(text):
    """Split text at natural break points based on line length."""
    
    words = text.split()
    if len(words) < 8:  # Don't split very short text
        return text
    
    lines = []
    current_line = []
    
    for word in words:
        current_line.append(word)
        current_line_text = ' '.join(current_line)
        
        # Check if we should break the line
        should_break = False
        
        # Break at approximately 60-80 characters (typical PDF line length)
        if len(current_line_text) > 60:
            # Look for good break points
            if (word.endswith(('.', '!', '?')) or  # End of sentence
                word in ['in', 'of', 'by', 'and', 'or', 'the', 'a', 'an'] or  # Common prepositions/articles
                len(current_line_text) > 80):  # Force break if too long
                should_break = True
        
        if should_break:
            lines.append(' '.join(current_line))
            current_line = []
    
    # Add remaining words
    if current_line:
        lines.append(' '.join(current_line))
    
    return '\n'.join(lines) if len(lines) > 1 else text


def handle_title_patterns(text):
    """Handle specific patterns common in academic papers."""
    
    # Pattern for your specific example:
    # "Prognostic value of lncRNA LINC01018 in prostate cancer by regulating miR-182-5p (The role of LINC01018 in prostate cancer)"
    
    # Look for patterns like "word word word in word" and break before "in"
    text = re.sub(r'(\w+)\s+(in\s+\w+)', r'\1\n\2', text)
    
    # Look for patterns with parentheses and break before them
    text = re.sub(r'\s+(\([^)]+\))', r'\n\1', text)
    
    # Look for "by regulating" pattern and break before "by"
    text = re.sub(r'\s+(by\s+regulating)', r'\n\1', text)
    
    # Look for long phrases and break at "of" if line is getting long
    lines = text.split('\n')
    processed_lines = []
    
    for line in lines:
        if len(line) > 50 and ' of ' in line:
            # Find the last "of" in the first 60 characters
            first_part = line[:60]
            last_of_pos = first_part.rfind(' of ')
            if last_of_pos > 20:  # Make sure we don't break too early
                before_of = line[:last_of_pos]
                after_of = line[last_of_pos + 1:]  # +1 to keep the space
                processed_lines.extend([before_of, after_of])
            else:
                processed_lines.append(line)
        else:
            processed_lines.append(line)
    
    return '\n'.join(processed_lines)


def main():
    """Main function."""
    
    # Your file paths
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    output_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder\pattern_based_output.json"
    
    # Check if PDF exists
    if not Path(pdf_file).exists():
        print("PDF file not found!")
        return
    
    # Create output directory
    Path(output_file).parent.mkdir(parents=True, exist_ok=True)
    
    # Extract PDF with pattern-based linebreaks
    extract_pdf_with_pattern_linebreaks(pdf_file, output_file)
    
    # Show results
    print(f"Extraction completed: {output_file}")
    
    # Quick check
    with open(output_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    if 'pages' in data and data['pages']:
        first_page = list(data['pages'].values())[0]
        if 'text' in first_page:
            text = first_page['text']
            linebreaks = text.count('\n')
            print(f"Linebreaks in first page: {linebreaks}")
            print(f"Sample text:")
            print(text[:300])


if __name__ == "__main__":
    main()
