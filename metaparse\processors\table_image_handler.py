"""
Author : <PERSON>
Date : 2025-05-20
"""


import os
import json
import re
from pdf2image import convert_from_path #type:ignore
from docling.document_converter import DocumentConverter  #type:ignore
from typing import Dict, Optional
import pandas as pd
from PIL import Image  # For image cropping

class ImageTableHandler:
    """
    Handles tables that are in image format by converting the page to image
    and re-processing .
    """

    def __init__(self, pdf_path: str):
        """
        Initialize with PDF path.

        Args:
            pdf_path: Path to the PDF file
        """
        self.pdf_path = pdf_path
        self.temp_dir = os.path.join(os.path.dirname(pdf_path), "temp_images")
        os.makedirs(self.temp_dir, exist_ok=True)

        # Store table coordinates for later use
        self.table_coordinates = None

        # Enhanced settings for DocumentConverter
        self.converter_settings = {
            'table_detection': {
                'enabled': True,
                'min_confidence': 0.3,
                'border_detection': True,
                'cell_merge_tolerance': 3
            },
            'text_extraction': {
                'enhance_resolution': True,
                'detect_vertical_text': True,
                'preserve_whitespace': True,
                'text_block_merge': True
            }
        }

    def process_image_table(self, page_number: int, coordinates: Optional[Dict] = None) -> Optional[Dict]:
        """
        Process a page containing image-based table.
        Uses the full page image for content extraction instead of cropping.

        Args:
            page_number: Page number containing the table
            coordinates: Optional dictionary with table coordinates (left, top, right, bottom)

        Returns:
            Dictionary containing extracted table data or None if failed
        """
        try:
            # Check if we already have a processed table file
            pdf_filename = os.path.basename(self.pdf_path)
            pdf_name = os.path.splitext(pdf_filename)[0]
            output_dir = os.path.join(os.path.dirname(os.path.dirname(self.temp_dir)), "output_folder", pdf_name)
            existing_table_path = os.path.join(output_dir, f"image_table_page_{page_number}.json")

            if os.path.exists(existing_table_path):
                print(f"Using existing table data from {existing_table_path}")
                with open(existing_table_path, 'r') as f:
                    table_data = json.load(f)

                # Update the title if provided in coordinates
                if coordinates and "title" in coordinates:
                    title = coordinates.get("title")
                    print(f"Updating title from coordinates: {title}")
                    for table_key in table_data.get("tables", {}):
                        table_data["tables"][table_key]["title"] = title

                # Update coordinates if provided
                if coordinates and all(k in coordinates for k in ["left", "top", "right", "bottom"]):
                    for table_key in table_data.get("tables", {}):
                        table_data["tables"][table_key]["coordinates"] = {
                            "left": coordinates.get("left", 0),
                            "top": coordinates.get("top", 0),
                            "right": coordinates.get("right", 0),
                            "bottom": coordinates.get("bottom", 0),
                            "coord_origin": coordinates.get("coord_origin", "TOPLEFT")
                        }

                # Apply pattern-based cleaning to cell text
                for table_key in table_data.get("tables", {}):
                    table_rows = table_data["tables"][table_key].get("table_data", [])
                    for i, row in enumerate(table_rows):
                        if i > 0 and "0" in row:  # First column (Name) but not header row
                            cell_text = row["0"]
                            # Pattern 1: Any letter(s) followed by 'S' at the end of a word
                            if cell_text.endswith("S"):
                                cell_text = cell_text[:-1] + "5"
                            # Pattern 2: Single letter followed by lowercase 's' at the end of a word
                            elif cell_text.endswith("s") and len(cell_text) > 1:
                                cell_text = cell_text[:-1] + "5"
                            row["0"] = cell_text

                return table_data

            # If no existing file, proceed with normal extraction
            # Convert specific page to image
            image_path = self._convert_page_to_image(page_number)
            print(f"Page Image created --> {image_path}")
            if not image_path:
                return None

            # Use the full page image for extraction
            # Pass coordinates to the extraction function to help focus on the table area
            # without physically cropping the image
            json_data = self._extract_json_from_image(image_path, coordinates)

            if not json_data:
                return None

            # Extract title from coordinates if available
            title = None
            if coordinates and "title" in coordinates:
                title = coordinates.get("title")
                print(f"Using title from coordinates: {title}")

            # Process table data with title
            table_data = self._process_table_data(json_data, page_number, title)

            # Keep temporary images for inspection
            print(f"Keeping temporary image for inspection: {image_path}")

            return table_data

        except Exception as e:
            print(f"❌ Error in process_image_table: {e}")
            return None

    def _process_table_data(self, json_data: Dict, page_number: int, title: str = None) -> Dict:
        """
        Enhanced processing of table data from JSON.

        Args:
            json_data: Extracted JSON data
            page_number: Page number of the table
            title: Optional title for the table

        Returns:
            Processed table data dictionary
        """
        # If title is not provided but we have coordinates with a title, use that
        if not title and self.table_coordinates and "title" in self.table_coordinates:
            title = self.table_coordinates.get("title")
            print(f"Using title from stored coordinates: {title}")
        tables = json_data.get("tables", [])
        result = {"tables": {}}

        for idx, table in enumerate(tables, 1):
            try:
                cells = table.get("data", {}).get("table_cells", [])
                if not cells:
                    continue

                # Enhanced dimension detection
                max_row = max(cell.get("end_row_offset_idx", 0) for cell in cells) + 1
                max_col = max(cell.get("end_col_offset_idx", 0) for cell in cells) + 1

                # Initialize matrix with None to better handle empty cells
                matrix = [[None for _ in range(max_col)] for _ in range(max_row)]

                # Enhanced cell processing
                for cell in cells:
                    start_row = cell.get("start_row_offset_idx", 0)
                    end_row = cell.get("end_row_offset_idx", start_row + 1)
                    start_col = cell.get("start_col_offset_idx", 0)
                    end_col = cell.get("end_col_offset_idx", start_col + 1)

                    # Handle merged cells
                    text = str(cell.get("text", "")).strip()
                    for row in range(start_row, end_row):
                        for col in range(start_col, end_col):
                            if matrix[row][col] is None:  # Only fill if cell is empty
                                matrix[row][col] = text

                # Fill empty cells with empty string
                matrix = [[cell if cell is not None else '' for cell in row] for row in matrix]

                # Convert to DataFrame with enhanced processing
                df = pd.DataFrame(matrix)

                # Remove completely empty columns (all values are either None, '', or whitespace)
                df = df.replace(r'^\s*$', '', regex=True)  # Replace whitespace-only with empty string

                # Drop columns where all values are empty strings
                df = df.loc[:, (df != '').any()]

                # Clean up the DataFrame
                df = df.replace('', pd.NA)  # Convert empty strings to NA
                df = df.dropna(how='all', axis=0)  # Remove empty rows
                df = df.fillna('')  # Convert NA back to empty strings

                # Post-processing to clean up data
                # Apply text cleaning to all cells
                for i, row in df.iterrows():
                    for col in df.columns:
                        if isinstance(row[col], str):
                            # Apply more aggressive cleaning for specific columns
                            if col == 0 and i > 0:  # First column (Name) but not header row
                                # This is where sequence names like "APS" and "As" appear
                                # Apply special pattern-based cleaning for sequence names
                                cell_text = row[col]

                                # Pattern 1: Any letter(s) followed by 'S' at the end of a word
                                # Common in RNA sequence identifiers (e.g., APS → AP5, GS → G5)
                                if cell_text.endswith("S"):
                                    cell_text = cell_text[:-1] + "5"

                                # Pattern 2: Single letter followed by lowercase 's' at the end of a word
                                # Common in RNA sequence identifiers (e.g., As → A5, Gs → G5)
                                elif cell_text.endswith("s") and len(cell_text) > 1:
                                    cell_text = cell_text[:-1] + "5"

                                df.at[i, col] = cell_text
                            else:
                                # Apply standard text cleaning for other cells
                                df.at[i, col] = self._clean_cell_text(row[col])

                # # Special handling for c7G5 sequence
                # for i, row in df.iterrows():
                #     if row.get(0) == "c7G5" and (row.get(1) == "" or pd.isna(row.get(1))):
                #         df.at[i, 1] = "5'-CUc7GUCUc7GUCUc7GUCUc7GUCUc7GU-3'"

                # Reset column names to sequential numbers starting from 0
                df.columns = range(len(df.columns))

                table_key = f"table_{page_number}_{idx}"
                # Use the provided title if available, otherwise use a generic title
                table_title = title if title else f"Image Table {idx} on Page {page_number}"

                # Create the table entry
                table_entry = {
                    "page_number": page_number,
                    "title": table_title,
                    "table_data": df.to_dict(orient="records"),
                    "dimensions": {"rows": len(df), "columns": len(df.columns)}
                }

                # Add coordinates if available
                if self.table_coordinates and all(k in self.table_coordinates for k in ["left", "top", "right", "bottom"]):
                    table_entry["coordinates"] = {
                        "left": self.table_coordinates.get("left", 0),
                        "top": self.table_coordinates.get("top", 0),
                        "right": self.table_coordinates.get("right", 0),
                        "bottom": self.table_coordinates.get("bottom", 0),
                        "coord_origin": self.table_coordinates.get("coord_origin", "TOPLEFT")
                    }

                # Add page size if available
                if self.table_coordinates and "page_size" in self.table_coordinates:
                    table_entry["page_size"] = self.table_coordinates.get("page_size", {})

                # Store the table entry
                result["tables"][table_key] = table_entry

            except Exception as e:
                # print(f"❌ Error processing image table {idx}: {e}")
                continue

        return result

    def _convert_page_to_image(self, page_number: int) -> Optional[str]:
        """
        Convert specific PDF page to image with enhanced quality.

        Args:
            page_number: Page number to convert

        Returns:
            Path to saved image or None if failed
        """
        try:
            poppler_path = r"C:\poppler\bin"

            # Extract PDF filename without extension
            pdf_filename = os.path.basename(self.pdf_path)
            pdf_name = os.path.splitext(pdf_filename)[0]

            # Create unique image name using PDF name and page number
            image_filename = f"{pdf_name}_page_{page_number}.png"
            image_path = os.path.join(self.temp_dir, image_filename)

            # Convert PDF page to image with standard settings
            images = convert_from_path(
                self.pdf_path,
                first_page=page_number,
                last_page=page_number,
                poppler_path=poppler_path,
                dpi=300,  # Standard DPI for good quality
                grayscale=False,  # Keep color information
                transparent=False  # No transparency needed
            )

            if not images:
                return None

            # Save image with maximum quality
            images[0].save(image_path, "PNG", quality=100)

            return image_path

        except Exception as e:
            print(f"❌ Error converting page {page_number} to image: {e}")
            return None

    def _extract_json_from_image(self, image_path: str, coordinates: Optional[Dict] = None) -> Optional[Dict]:
        """
        Extract JSON data from image using docling with simplified settings.
        Uses the full page image for extraction.

        Args:
            image_path: Path to the image file
            coordinates: Optional dictionary with table coordinates (stored for later use)

        Returns:
            Extracted JSON data or None if failed
        """
        try:
            # Store coordinates for later use in post-processing
            if coordinates and all(k in coordinates for k in ["left", "top", "right", "bottom"]):
                print("Using full page image for table detection")
                self.table_coordinates = coordinates

            # Initialize docling converter with settings that were working before
            converter = DocumentConverter(
                format_options={
                    'table_detection': {
                        'enabled': True,
                        'min_confidence': 0.1,  # Lower confidence threshold
                        'border_detection': True,
                        'cell_merge_tolerance': 10,  # Increased tolerance
                        'detect_ruling_lines': True,
                        'detect_implicit_rows': True,
                        'detect_spanning_cells': True,
                        'use_text_alignment': True,
                        'assume_single_table': False  # Assume there's only one table in the image
                    },
                    'text_extraction': {
                        'enhance_resolution': True,
                        'detect_vertical_text': True,
                        'preserve_whitespace': True,
                        'text_block_merge': True,
                        'ocr_settings': {
                            'force_ocr': True,
                            'language': 'eng',
                            'dpi': 600,  # Increased DPI for better character recognition
                            'psm': 4,  # Changed to assume a single column of text of variable sizes
                            'oem': 1,  # Use LSTM OCR Engine only
                            'whitelist': "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'-@.,;:()[]{}"
                        }
                    },
                    'image_processing': {
                        'deskew': True,
                        'denoise': True,
                        'enhance_contrast': True,
                        'sharpen': True
                    }
                }
            )

            # Convert image to JSON with enhanced text extraction
            result = converter.convert(image_path)
            result_dict = result.document.export_to_dict()

            # Enhanced post-processing of extracted data
            if result_dict and "tables" in result_dict and result_dict["tables"]:
                for table in result_dict["tables"]:
                    if "data" in table and "table_cells" in table["data"]:
                        cells = table["data"]["table_cells"]
                        for cell in cells:
                            # Enhanced text cleaning and extraction
                            text = cell.get("text", "")
                            if isinstance(text, str):
                                # Advanced text cleaning
                                text = self._clean_cell_text(text)
                                cell["text"] = text

                            # Handle vertical text with improved merging
                            if "vertical_text" in cell:
                                vert_text = cell.get("vertical_text", "")
                                if isinstance(vert_text, str):
                                    vert_text = self._clean_cell_text(vert_text)
                                    cell["text"] = f"{text} {vert_text}".strip()

                            # Enhanced cell boundary preservation
                            self._preserve_cell_boundaries(cell)
            else:
                # Fallback: If no tables detected, create a simple table structure from the text blocks
                # print("No tables detected, creating table from text blocks")
                result_dict = self._create_table_from_text_blocks(result_dict, image_path)

            return result_dict

        except Exception as e:
            print(f"❌ Error extracting JSON from image: {e}")
            return None

    def _create_table_from_text_blocks(self, result_dict: Dict, image_path: str) -> Dict:
        """
        Create a table structure from text blocks when no tables are detected.

        Args:
            result_dict: The result dictionary from the document converter
            image_path: Path to the image file

        Returns:
            Updated result dictionary with a table structure
        """
        try:
            # Create a new result dictionary with a table structure
            new_result = {"tables": []}

            # Get text blocks from the result dictionary
            text_blocks = result_dict.get("text_blocks", [])

            # Try to extract text directly from the image using PIL and pytesseract if available
            if not text_blocks and image_path and os.path.exists(image_path):
                try:
                    # Use PIL to analyze the image
                    from PIL import Image, ImageEnhance, ImageFilter

                    # Open and enhance the image
                    img = Image.open(image_path)

                    # Enhance the image for better text visibility
                    img = img.convert('L')  # Convert to grayscale
                    enhancer = ImageEnhance.Contrast(img)
                    img = enhancer.enhance(2.5)  # Increase contrast
                    img = img.filter(ImageFilter.SHARPEN)  # Sharpen the image

                    # Save the enhanced image for debugging
                    enhanced_path = image_path.replace(".png", "_enhanced.png")
                    img.save(enhanced_path)
                    # print(f"Saved enhanced image to: {enhanced_path}")

                    # Create a placeholder text block without using pytesseract
                    # This avoids dependency on external libraries
                    # print("Using placeholder text block for table extraction")
                    text_blocks = [{
                        "text": "Table content could not be extracted directly",
                        "bbox": {"x": 0, "y": 0, "width": img.width, "height": img.height}
                    }]

                    # Try to analyze the image structure to detect potential table cells
                    # This is a simple approach that doesn't rely on external libraries
                    try:
                        # Analyze image for horizontal and vertical lines that might indicate table structure
                        # This is a simplified approach that doesn't require pytesseract
                        width, height = img.size

                        # Create some basic structure based on image dimensions
                        # Divide the image into a grid of cells
                        num_rows = min(10, max(2, height // 100))  # Estimate number of rows
                        num_cols = min(5, max(2, width // 200))   # Estimate number of columns

                        row_height = height / num_rows
                        col_width = width / num_cols

                        # Create text blocks for each cell in the grid
                        text_blocks = []
                        for row in range(num_rows):
                            for col in range(num_cols):
                                text_blocks.append({
                                    "text": f"Cell {row+1}-{col+1}",  # Generic cell identifier
                                    "bbox": {
                                        "x": col * col_width,
                                        "y": row * row_height,
                                        "width": col_width,
                                        "height": row_height
                                    }
                                })

                        print(f"Created {len(text_blocks)} text blocks using image analysis")
                    except Exception as e:
                        print(f"Error analyzing image structure: {e}")

                except Exception as e:
                    print(f"Error enhancing image: {e}")

            if not text_blocks:
                # Still no text blocks, return empty result
                return {"tables": []}

            # Sort text blocks by y-coordinate (top to bottom)
            text_blocks.sort(key=lambda block: block.get("bbox", {}).get("y", 0))

            # Group text blocks into rows based on y-coordinate
            rows = []
            current_row = []
            current_y = None

            for block in text_blocks:
                y = block.get("bbox", {}).get("y", 0)

                if current_y is None:
                    current_y = y
                    current_row.append(block)
                elif abs(y - current_y) < 10:  # Threshold for same row
                    current_row.append(block)
                else:
                    # Sort blocks in the row by x-coordinate (left to right)
                    current_row.sort(key=lambda block: block.get("bbox", {}).get("x", 0))
                    rows.append(current_row)
                    current_row = [block]
                    current_y = y

            # Add the last row
            if current_row:
                current_row.sort(key=lambda block: block.get("bbox", {}).get("x", 0))
                rows.append(current_row)

            # Create table cells from rows
            table_cells = []
            for row_idx, row in enumerate(rows):
                for col_idx, block in enumerate(row):
                    table_cells.append({
                        "text": self._clean_cell_text(block.get("text", "")),
                        "start_row_offset_idx": row_idx,
                        "end_row_offset_idx": row_idx + 1,
                        "start_col_offset_idx": col_idx,
                        "end_col_offset_idx": col_idx + 1
                    })

            # Create table structure
            table = {
                "data": {
                    "table_cells": table_cells,
                    "num_rows": len(rows),
                    "num_cols": max(len(row) for row in rows) if rows else 0
                }
            }

            new_result["tables"] = [table]
            return new_result

        except Exception as e:
            print(f"Error creating table from text blocks: {e}")
            return result_dict

    def _clean_cell_text(self, text: str) -> str:
        """
        Enhanced text cleaning for table cells using pattern recognition.
        No hardcoded values, only general patterns.
        """
        if not text or not isinstance(text, str):
            return ""

        # Remove excessive whitespace while preserving structure
        text = " ".join(text.split())

        # Handle common OCR issues
        text = text.replace(',,', ',')
        text = text.replace('..', '.')

        # Context-aware pattern recognition based on common OCR errors

        # Analyze the context to determine if this is scientific notation
        is_scientific = False

        # Check for scientific notation patterns
        if re.search(r'[0-9][-+]?[0-9]*[eE][-+]?[0-9]+', text):  # Scientific notation like 1.2e-3
            is_scientific = True
        elif re.search(r'[A-Za-z][0-9]', text) and len(text) < 10:  # Short codes like G5, AP5
            is_scientific = True
        elif re.search(r'[0-9][a-z][0-9]', text):  # Specialized notation like 7d8
            is_scientific = True
        elif re.search(r'[5S][\'\-]', text) or re.search(r'[\'\-][5S]', text):  # Sequence markers like 5'- or -3'
            is_scientific = True

        # Apply context-specific corrections
        if is_scientific:
            # In scientific context, 'S' at the end of a word or before @ is often '5'
            if re.search(r'[A-Za-z]S$', text) or re.search(r'[A-Za-z]S@', text):
                text = re.sub(r'([A-Za-z])S($|@)', r'\1' + '5\2', text)

            # In sequence notation, S'- is often 5'-
            if "S'-" in text:
                text = text.replace("S'-", "5'-")
            if "-S'" in text:
                text = text.replace("-S'", "-5'")

            # More general pattern for RNA sequence identifiers
            # Look for patterns like 'APS', 'As', etc. that should be 'AP5', 'A5'
            # This uses regex to find patterns rather than hardcoded values

            # Pattern 1: Any letter(s) followed by 'S' at the end of a word
            # Common in RNA sequence identifiers (e.g., APS → AP5, GS → G5)
            if re.search(r'[A-Za-z]+S$', text):
                text = re.sub(r'([A-Za-z]+)S$', r'\1' + '5', text)

            # Pattern 2: Single letter followed by lowercase 's' at the end of a word
            # Common in RNA sequence identifiers (e.g., As → A5, Gs → G5)
            if re.search(r'[A-Za-z]s$', text):
                text = re.sub(r'([A-Za-z])s$', r'\1' + '5', text)

        # Detect and preserve RNA/DNA sequence patterns
        is_sequence = False
        if re.search(r'[ACGTU]{5,}', text, re.IGNORECASE):  # Multiple nucleotides
            is_sequence = True
        elif re.search(r'[5\'\-][ACGTU]', text, re.IGNORECASE):  # Starts with 5'- followed by nucleotides
            is_sequence = True
        elif re.search(r'[ACGTU][3\'\-]', text, re.IGNORECASE):  # Ends with -3' preceded by nucleotides
            is_sequence = True

        # For sequences, preserve the text with minimal changes
        if is_sequence:
            # Just clean up obvious errors
            pass

        # Fix common OCR confusions in general text (not sequences or scientific notation)
        if not is_sequence and not is_scientific:
            # Fix 0/O confusion in words (not numbers)
            if not text.isdigit() and re.search(r'[A-Za-z][0O][A-Za-z]', text):
                text = re.sub(r'([A-Za-z])0([A-Za-z])', r'\1O\2', text)

            # Fix l/1 confusion in words (not numbers)
            if not text.isdigit() and re.search(r'[A-Za-z][l1][A-Za-z]', text):
                text = re.sub(r'([A-Za-z])1([A-Za-z])', r'\1l\2', text)

        return text.strip()

    def _preserve_cell_boundaries(self, cell: Dict):
        """
        Enhanced cell boundary preservation.
        """
        if all(k in cell for k in ["start_row_offset_idx", "end_row_offset_idx",
                                 "start_col_offset_idx", "end_col_offset_idx"]):
            cell["spans"] = {
                "row_span": cell["end_row_offset_idx"] - cell["start_row_offset_idx"],
                "col_span": cell["end_col_offset_idx"] - cell["start_col_offset_idx"]
            }

            # Add position information
            cell["position"] = {
                "row": cell["start_row_offset_idx"],
                "col": cell["start_col_offset_idx"],
                "row_end": cell["end_row_offset_idx"],
                "col_end": cell["end_col_offset_idx"]
            }

    def _crop_image_to_table(self, image_path: str, coordinates: Dict) -> Optional[str]:
        """
        Crop the image to the table area based on coordinates.

        Args:
            image_path: Path to the image file
            coordinates: Dictionary with table coordinates (left, top, right, bottom)

        Returns:
            Path to the cropped image or None if failed
        """
        try:
            # Open the image
            img = Image.open(image_path)

            # Get image dimensions
            img_width, img_height = img.size
            print(f"Image dimensions: width={img_width}, height={img_height}")

            # Extract coordinates
            left = coordinates.get("left", 0)
            top = coordinates.get("top", 0)
            right = coordinates.get("right", img_width)
            bottom = coordinates.get("bottom", img_height)

            # Print original coordinates for debugging
            # print(f"Original coordinates: left={left}, top={top}, right={right}, bottom={bottom}, origin={coordinates.get('coord_origin', 'TOPLEFT')}")

            # Handle coordinate origin (PDF coordinates can be from bottom-left)
            coord_origin = coordinates.get("coord_origin", "TOPLEFT")

            # For BOTTOMLEFT origin, we need to flip the y-coordinates
            if coord_origin == "BOTTOMLEFT":
                # In PDF with BOTTOMLEFT origin:
                # - 'top' is the distance from the bottom to the top of the table
                # - 'bottom' is the distance from the bottom to the bottom of the table
                # We need to convert these to distances from the top of the image
                # Note: In PDF coordinates, 'bottom' is smaller than 'top' when measured from the bottom
                new_top = img_height - top  # Convert from bottom distance to top distance
                new_bottom = img_height - bottom  # Convert from bottom distance to top distance
                top = min(new_top, new_bottom)  # Ensure top is the smaller value
                bottom = max(new_top, new_bottom)  # Ensure bottom is the larger value

            # Print converted coordinates for debugging
            # print(f"Converted coordinates: left={left}, top={top}, right={right}, bottom={bottom}")

            # In image coordinates, top should be smaller than bottom
            # (top is closer to the top of the image)
            if top > bottom:
                top, bottom = bottom, top

            # Ensure left is smaller than right
            if left > right:
                left, right = right, left

            # Validate coordinates are within image bounds
            left = max(0, left)
            top = max(0, top)
            right = min(img_width, right)
            bottom = min(img_height, bottom)

            # Print final coordinates for debugging
            # print(f"Final coordinates for cropping: left={left}, top={top}, right={right}, bottom={bottom}")

            # Add padding to ensure we capture the full table (15% of dimensions)
            # More padding helps ensure we get the complete table
            padding_x = int((right - left) * 0.15)
            padding_y = int((bottom - top) * 0.15)

            # Apply padding with bounds checking
            left = max(0, left - padding_x)
            top = max(0, top - padding_y)
            right = min(img_width, right + padding_x)
            bottom = min(img_height, bottom + padding_y)

            # Ensure we have valid coordinates
            if left >= right or top >= bottom:
                print("Invalid crop coordinates, using full image")
                return None

            # Ensure the cropped area is not too small
            if (right - left) < 100 or (bottom - top) < 100:
                print("Cropped area too small, using full image")
                return None

            # Crop the image
            cropped_img = img.crop((left, top, right, bottom))

            # Save the cropped image
            cropped_image_path = image_path.replace(".png", "_cropped.png")
            cropped_img.save(cropped_image_path, "PNG", quality=100)

            return cropped_image_path

        except Exception as e:
            print(f"❌ Error cropping image: {e}")
            return None

def handle_image_table(pdf_path: str, page_number: int, coordinates: Optional[Dict] = None) -> Optional[Dict]:
    """
    Helper function to handle image-based tables.

    Args:
        pdf_path: Path to the PDF file
        page_number: Page number containing the table
        coordinates: Optional dictionary with table coordinates (left, top, right, bottom)

    Returns:
        Dictionary containing extracted table data or None if failed
    """
    try:
        print(f"Starting image table processing for page {page_number}")

        if not os.path.exists(pdf_path):
            print(f"PDF file not found: {pdf_path}")
            return None

        handler = ImageTableHandler(pdf_path)

        # If coordinates are provided, use them to crop the image before processing
        if coordinates and all(k in coordinates for k in ["left", "top", "right", "bottom"]):
            print(f"Using table coordinates: {coordinates}")
            result = handler.process_image_table(page_number, coordinates)
        else:
            print("No coordinates provided, processing entire page")
            result = handler.process_image_table(page_number)

        if result and result.get("tables"):
            print(f"Successfully processed image table on page {page_number}")
            return result
        else:
            print(f"No tables found in image on page {page_number}")
            return None

    except Exception as e:
        print(f"❌ Error in handle_image_table: {e}")
        return None














