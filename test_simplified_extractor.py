#!/usr/bin/env python3
"""
Simple test script for the simplified DocExtractor with linebreak preservation.

This script tests the core functionality without complex argument parsing.

Author: <PERSON> Jadhav
Date: 2025-01-27
"""

import os
import json
import sys
from pathlib import Path

def test_simplified_extractor():
    """Test the simplified DocExtractor."""
    
    print("🧪 Testing Simplified DocExtractor with Linebreak Preservation")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path("metaparse/extractors/doc_extractor_linebreak.py").exists():
        print("❌ Error: Please run this script from the MetaParse project root directory")
        print("💡 Current directory should contain the 'metaparse' folder")
        return False
    
    try:
        # Import the simplified extractor
        sys.path.insert(0, '.')
        from metaparse.extractors.doc_extractor_linebreak import DocExtractor
        
        # Your PDF file
        pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
        output_folder = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder"
        
        if not Path(pdf_file).exists():
            print(f"❌ PDF file not found: {pdf_file}")
            print("💡 Please ensure the PDF file exists at the specified location")
            return False
        
        print(f"📄 Testing with: {Path(pdf_file).name}")
        print(f"📁 Output folder: {output_folder}")
        
        # Create extractor with default settings (linebreak preservation enabled)
        print("\n🔄 Creating DocExtractor with default linebreak preservation...")
        
        extractor = DocExtractor(
            pdf_file,
            optimization_level='quality',
            use_cache=False
        )
        
        print("✅ DocExtractor created successfully")
        
        # Extract text
        print("\n🔄 Extracting text...")
        result = extractor.extract_text_optimized()
        
        print("✅ Text extraction completed")
        
        # Save result
        Path(output_folder).mkdir(parents=True, exist_ok=True)
        output_file = Path(output_folder) / f"{Path(pdf_file).stem}_simplified_test.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Output saved to: {output_file}")
        
        # Analyze the results
        analyze_results(result, output_file)
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_results(result: dict, output_file: Path):
    """Analyze the extraction results."""
    
    print(f"\n📊 Analysis Results:")
    print("-" * 30)
    
    # Basic stats
    file_size = output_file.stat().st_size
    print(f"📁 Output file size: {file_size:,} bytes")
    
    if 'pages' in result and result['pages']:
        total_pages = len(result['pages'])
        print(f"📄 Total pages: {total_pages}")
        
        # Analyze first page
        first_page_key = list(result['pages'].keys())[0]
        first_page = result['pages'][first_page_key]
        
        if 'text' in first_page:
            text = first_page['text']
            
            # Count various elements
            total_chars = len(text)
            linebreak_count = text.count('\n')
            word_count = len(text.split())
            
            print(f"\n📝 First Page Text Analysis:")
            print(f"   Total characters: {total_chars:,}")
            print(f"   Word count: {word_count:,}")
            print(f"   Linebreaks (\\n): {linebreak_count}")
            
            if linebreak_count > 0:
                print(f"   ✅ SUCCESS: Linebreaks are preserved!")
                
                # Calculate linebreak density
                linebreak_density = (linebreak_count / total_chars) * 100 if total_chars > 0 else 0
                print(f"   📊 Linebreak density: {linebreak_density:.2f}%")
                
            else:
                print(f"   ❌ ISSUE: No linebreaks found")
            
            # Check for preservation flags
            if first_page.get('linebreaks_preserved'):
                print(f"   ✅ Linebreak preservation flag: True")
                
                if 'elements_processed' in first_page:
                    print(f"   🔧 Elements processed: {first_page['elements_processed']}")
                
                if 'paragraph_count' in first_page:
                    print(f"   📄 Paragraphs detected: {first_page['paragraph_count']}")
                    
            else:
                print(f"   ⚠️ Linebreak preservation flag: Missing or False")
            
            # Show sample text
            print(f"\n📖 Sample Text (first 200 characters):")
            sample_text = text[:200]
            
            # Show with visible linebreaks
            display_text = sample_text.replace('\n', '\\n\n   ')
            print(f"   '{display_text}...'")
            
            # Count linebreaks in sample
            sample_linebreaks = sample_text.count('\n')
            print(f"\n🔍 Linebreaks in sample: {sample_linebreaks}")
            
            if sample_linebreaks > 0:
                print(f"   ✅ Linebreaks are visible in the sample text!")
            else:
                print(f"   ❌ No linebreaks in the sample text")
            
        else:
            print("⚠️ No text found in first page")
    
    else:
        print("❌ No pages found in extracted data")
    
    # Check other sections
    sections = ['texts', 'tables', 'pictures']
    for section in sections:
        if section in result and result[section]:
            count = len(result[section])
            print(f"📋 {section.capitalize()}: {count} items")

def main():
    """Main function to run the simplified test."""
    
    print("🚀 Simplified DocExtractor Test")
    print("=" * 40)
    
    print("\n💡 This test verifies that the simplified DocExtractor")
    print("   with default linebreak preservation is working correctly.")
    
    success = test_simplified_extractor()
    
    if success:
        print(f"\n🎉 Test completed successfully!")
        
        print(f"\n📚 What to check:")
        print("✅ Look for \\n characters in the sample text above")
        print("✅ Check that 'linebreaks_preserved': true")
        print("✅ Verify 'elements_processed' and 'paragraph_count' fields")
        print("✅ Open the JSON file to see the full extracted text")
        
        print(f"\n🔧 To run the main extractor:")
        print("   python metaparse/extractors/doc_extractor_linebreak.py")
        
    else:
        print(f"\n❌ Test failed. Please check the error messages above.")
    
    print(f"\n✨ Test completed!")

if __name__ == "__main__":
    main()
