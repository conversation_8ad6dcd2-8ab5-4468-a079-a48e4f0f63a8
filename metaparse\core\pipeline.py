"""
Core pipeline module for the MetaParse package.

This module provides the main document extraction pipeline that orchestrates
the extraction and processing of document content.
Author : Anand Jadhav
Date : 2025-05-20
"""

import os
import time
import json
import pandas as pd
import asyncio
import aiofiles
import concurrent.futures
from typing import List, Optional

from metaparse.extractors.doc_extractor import DocExtractor
from metaparse.utils.file_manager import FileManager
from metaparse.utils.docx_to_pdf import DocxToPdfConverter
from metaparse.processors.doc_segmenter import DocSegmenter
from metaparse.processors.image_processor import ImageExtractor
# from metaparse.processors.image_processor_correct import ImageExtractor
from metaparse.processors.ligature_processor import LigatureProcessor
from metaparse.processors.table_processor import TableProcessor


class DocExtractionPipeline:
    """
    Main pipeline for document extraction and processing.

    This class orchestrates the extraction of text, tables, and images from documents,
    and the processing of this content into structured data.
    """

    def __init__(self, doc_path: str, save_path: str, optimization_level: str = 'balanced', use_cache: bool = True):
        """
        Initialize the document extraction pipeline.

        Args:
            doc_path: Path to the document file
            save_path: Path to save extracted data
            optimization_level: Level of optimization ('speed', 'balanced', or 'quality')
            use_cache: Whether to use caching
        """
        # Get absolute path
        doc_path = os.path.abspath(doc_path)
        save_path = os.path.abspath(save_path)

        # Check if the file is a DOCX file
        self.original_doc_path = doc_path
        self.temp_pdf_path = None
        self.docx_converter = None

        if doc_path.lower().endswith('.docx'):
            print(f"DOCX file detected: {doc_path}")
            print("Converting DOCX to PDF for extraction...")

            # Create a DocxToPdfConverter instance with speed optimization
            print("Creating optimized DOCX to PDF converter...")
            self.docx_converter = DocxToPdfConverter(use_temp_dir=True, cleanup_temp=True)

            # Convert DOCX to PDF with timing
            print("Starting DOCX to PDF conversion...")
            start_time = time.time()
            pdf_path = self.docx_converter.convert(doc_path)
            conversion_time = time.time() - start_time
            print(f"DOCX to PDF conversion took {conversion_time:.2f} seconds")

            if pdf_path:
                print(f"✅ Successfully converted to PDF: {pdf_path}")
                # Use the PDF path for extraction
                self.temp_pdf_path = pdf_path
                doc_path = pdf_path
            else:
                print("⚠️ DOCX to PDF conversion failed. Proceeding with direct DOCX extraction.")
                print("Note: This may result in base64-encoded images in the output.")

        self.doc_path = doc_path
        self.save_path = save_path
        self.optimization_level = optimization_level
        self.use_cache = use_cache

        # Initialize optimized document extractor
        self.doc_extractor = DocExtractor(
            doc_path,
            optimization_level=optimization_level,
            use_cache=use_cache
        )

        self.text_processor = None
        self.file_manager = FileManager(save_path)

    def run_pipeline(self):
        """Execute the full pipeline to extract and save JSON data"""
        start_json = time.time()

        # Use optimized extraction method
        result_json = self.doc_extractor.extract_text_optimized()

        if result_json:
            json_file_path = f"{self.file_manager.save_path}\\extracted_data.json"
            json_file_path_segmented = f"{self.file_manager.save_path}\\extracted_data_segmented.json"
            json_file_path_image_data = f"{self.file_manager.save_path}\\extracted_data_imagesinfo.json"

            ### Process ligatures in JSON data
            start_ligature = time.time()
            ligature_processor = LigatureProcessor(result_json)
            processed_json = ligature_processor.process_json()

            ### Save processed JSON data
            with open(json_file_path, "w", encoding="utf-8") as json_file:
                json.dump(processed_json, json_file, indent=2, ensure_ascii=False)

            print(f"✅ Complete JSON data extracted and saved at: {json_file_path}")
            print(f"⏱️ Full JSON extraction time: {round(time.time() - start_json, 2)} sec")
            print(f"⏱️ Ligature processing time: {round(time.time() - start_ligature, 2)} sec")

            ### Get segmented text as per section headers
            start_segmented = time.time()
            segmenter = DocSegmenter(processed_json)
            section_text_data = segmenter.run_segmentation()
            with open(json_file_path_segmented, "w", encoding="utf-8") as json_file:
                json.dump(section_text_data, json_file, indent=2, ensure_ascii=False)
            print(f"✅  Segmented JSON file saved successfully at: {json_file_path_segmented}")
            print(f"⏱️ Segmented JSON extraction time: {round(time.time() - start_segmented, 2)} sec")

            # ## Extract Images
            start_images = time.time()
            image_extractor = ImageExtractor(processed_json, self.doc_path, self.file_manager)
            # Step 1: Extract raw metadata and images
            raw_image_metadata = image_extractor.save_images_from_result_dict()
            # Step 2: Clean and filter metadata
            image_metadata = image_extractor.filter_and_save_clean_image_metadata(raw_image_metadata)
            # Step 3: Save clean metadata to JSON
            with open(json_file_path_image_data, "w", encoding="utf-8") as json_file:
                json.dump(image_metadata, json_file, indent=2, ensure_ascii=False)
            print(f"✅  Image data JSON file saved successfully at: {json_file_path_image_data}")
            # print(f"⏱️ Images and JSON extraction time: {round(time.time() - start_images, 2)} sec")

            ## Table data extraction
            start_tables = time.time()
            table_processor = TableProcessor(processed_json, doc_path=self.doc_path)
            table_data = table_processor.process_tables(doc_path=self.doc_path)
            # Save table data to JSON
            json_file_path_table_data = f"{self.file_manager.save_path}\\table_data.json"
            with open(json_file_path_table_data, "w", encoding="utf-8") as json_file:
                # Create a copy of table_data without DataFrame objects for JSON serialization
                json_safe_data = {"tables": {}}
                for table_key, table_info in table_data["tables"].items():
                    json_safe_data["tables"][table_key] = {
                        "page_number": table_info["page_number"],
                        "title": table_info["title"],
                        "table_data": table_info["table_data"],
                        "coordinates": table_info["coordinates"],
                        "page_size": table_info["page_size"],
                    }
                json.dump(json_safe_data, json_file, indent=2, ensure_ascii=False)

            # Save tables to Excel
            for table_key, table_info in table_data["tables"].items():
                excel_path = os.path.join(self.file_manager.save_path, f"{table_key}.xlsx")
                # Convert table_data to DataFrame before saving to Excel
                df = pd.DataFrame(table_info["table_data"])
                df.to_excel(excel_path, index=False, header=False)

            print(f"✅ Table data extracted and saved at: {json_file_path_table_data}")
            # print(f"⏱️ Table extraction time: {round(time.time() - start_tables, 2)} sec")
        else:
            print(f"❌ Failed to extract JSON from document: {self.doc_path}.")

        # Clean up temporary files if a DOCX converter was used
        if self.docx_converter:
            try:
                self.docx_converter.cleanup()
                print(f"✅ Temporary files cleaned up")
            except Exception as e:
                print(f"⚠️ Warning: Could not clean up temporary files: {e}")

        print("Document Extraction Completed.")

    async def run_pipeline_async(self):
        """Execute the full pipeline asynchronously to extract and save JSON data"""
        start_json = time.time()

        # Run CPU-bound extraction in a thread pool
        loop = asyncio.get_event_loop()
        result_json = await loop.run_in_executor(
            None, self.doc_extractor.extract_text_optimized
        )

        if result_json:
            json_file_path = f"{self.file_manager.save_path}\\extracted_data.json"
            json_file_path_segmented = f"{self.file_manager.save_path}\\extracted_data_segmented.json"
            json_file_path_image_data = f"{self.file_manager.save_path}\\extracted_data_imagesinfo.json"

            ### Process ligatures in JSON data asynchronously
            start_ligature = time.time()
            ligature_processor = LigatureProcessor(result_json)

            # Run ligature processing in thread pool as it's CPU-bound
            processed_json = await loop.run_in_executor(
                None, ligature_processor.process_json
            )

            ### Save processed JSON data asynchronously
            async with aiofiles.open(json_file_path, "w", encoding="utf-8") as json_file:
                await json_file.write(json.dumps(processed_json, indent=2, ensure_ascii=False))

            print(f"✅ Complete JSON data extracted and saved at: {json_file_path}")
            print(f"⏱️ Full JSON extraction time: {round(time.time() - start_json, 2)} sec")
            print(f"⏱️ Ligature processing time: {round(time.time() - start_ligature, 2)} sec")

            ### Get segmented text as per section headers
            start_segmented = time.time()
            # Run segmentation in thread pool as it's CPU-bound
            segmenter = DocSegmenter(processed_json)
            section_text_data = await loop.run_in_executor(
                None, segmenter.run_segmentation
            )

            # Save segmented data asynchronously
            async with aiofiles.open(json_file_path_segmented, "w", encoding="utf-8") as json_file:
                await json_file.write(json.dumps(section_text_data, indent=2, ensure_ascii=False))

            print(f"✅  Segmented JSON file saved successfully at: {json_file_path_segmented}")
            print(f"⏱️ Segmented JSON extraction time: {round(time.time() - start_segmented, 2)} sec")

            # ## Extract Images
            start_images = time.time()
            image_extractor = ImageExtractor(processed_json, self.doc_path, self.file_manager)

            # Run image extraction in thread pool
            raw_image_metadata = await loop.run_in_executor(
                None, image_extractor.save_images_from_result_dict
            )

            # Clean and filter metadata in thread pool
            image_metadata = await loop.run_in_executor(
                None, lambda: image_extractor.filter_and_save_clean_image_metadata(raw_image_metadata)
            )

            # Save clean metadata to JSON asynchronously
            async with aiofiles.open(json_file_path_image_data, "w", encoding="utf-8") as json_file:
                await json_file.write(json.dumps(image_metadata, indent=2, ensure_ascii=False))

            print(f"✅  Image data JSON file saved successfully at: {json_file_path_image_data}")
            # print(f"⏱️ Images and JSON extraction time: {round(time.time() - start_images, 2)} sec")

            ## Table data extraction
            start_tables = time.time()
            table_processor = TableProcessor(processed_json, doc_path=self.doc_path)

            # Process tables in thread pool
            # Create a lambda function to pass the doc_path parameter
            table_data = await loop.run_in_executor(
                None, lambda: table_processor.process_tables(self.doc_path)
            )

            # Save table data to JSON asynchronously
            json_file_path_table_data = f"{self.file_manager.save_path}\\table_data.json"

            # Create a copy of table_data without DataFrame objects for JSON serialization
            json_safe_data = {"tables": {}}
            for table_key, table_info in table_data["tables"].items():
                json_safe_data["tables"][table_key] = {
                    "page_number": table_info["page_number"],
                    "title": table_info["title"],
                    "table_data": table_info["table_data"],
                    "coordinates": table_info["coordinates"],
                    "page_size": table_info["page_size"],
                }

            async with aiofiles.open(json_file_path_table_data, "w", encoding="utf-8") as json_file:
                await json_file.write(json.dumps(json_safe_data, indent=2, ensure_ascii=False))

            # Save tables to Excel (this needs to be done sequentially as pandas doesn't support async)
            for table_key, table_info in table_data["tables"].items():
                excel_path = os.path.join(self.file_manager.save_path, f"{table_key}.xlsx")

                # Define a function to convert and save the table data
                def save_excel_file(table_data, path):
                    # Convert table_data to DataFrame before saving to Excel
                    df = pd.DataFrame(table_data)
                    df.to_excel(path, index=False, header=False)

                # Execute the function in a thread pool
                await loop.run_in_executor(
                    None,
                    save_excel_file,
                    table_info["table_data"],
                    excel_path
                )

            print(f"✅ Table data extracted and saved at: {json_file_path_table_data}")
            print(f"⏱️ Table extraction time: {round(time.time() - start_tables, 2)} sec")
        else:
            print(f"❌ Failed to extract JSON from document: {self.doc_path}.")

        # Clean up temporary files if a DOCX converter was used
        if self.docx_converter:
            try:
                self.docx_converter.cleanup()
                print(f"✅ Temporary files cleaned up")
            except Exception as e:
                print(f"⚠️ Warning: Could not clean up temporary files: {e}")

        print("Async Document Extraction Completed.")

    @staticmethod
    async def process_batch_async(doc_paths, save_paths, optimization_level='quality', use_cache=True, concurrency_limit=3):
        """
        Process a batch of documents asynchronously with concurrency control.

        Args:
            doc_paths: List of document file paths
            save_paths: List of save paths
            optimization_level: Level of optimization ('speed', 'balanced', or 'quality')
            use_cache: Whether to use caching
            concurrency_limit: Maximum number of concurrent tasks

        Returns:
            List of dictionaries with processing results
        """
        if len(doc_paths) != len(save_paths):
            raise ValueError("Number of document files must match number of save paths")

        results = []
        semaphore = asyncio.Semaphore(concurrency_limit)

        async def process_with_semaphore(doc_path, save_path):
            async with semaphore:
                print(f"\nProcessing: {os.path.basename(doc_path)}")
                pipeline = DocExtractionPipeline(
                    doc_path,
                    save_path,
                    optimization_level=optimization_level,
                    use_cache=use_cache
                )
                try:
                    await pipeline.run_pipeline_async()
                    return {
                        'success': True,
                        'doc_path': doc_path,
                        'save_path': save_path
                    }
                except Exception as e:
                    print(f"❌ Error processing {os.path.basename(doc_path)}: {e}")
                    return {
                        'success': False,
                        'doc_path': doc_path,
                        'save_path': save_path,
                        'error': str(e)
                    }

        # Create tasks for all files
        tasks = [
            process_with_semaphore(doc_file, save_path)
            for doc_file, save_path in zip(doc_paths, save_paths)
        ]

        # Process all tasks and collect results
        total = len(tasks)
        for i, task_result in enumerate(asyncio.as_completed(tasks), 1):
            result = await task_result
            results.append(result)
            print(f"Progress: {i}/{total} ({round(i/total*100)}%)")

        return results

    @staticmethod
    def process_batch_parallel(doc_paths, save_paths, optimization_level='speed', use_cache=True, max_workers=None):
        """
        Process a batch of documents in parallel using multiple processes.

        Args:
            doc_paths: List of document file paths
            save_paths: List of save paths
            optimization_level: Level of optimization ('speed', 'balanced', or 'quality')
            use_cache: Whether to use caching
            max_workers: Maximum number of worker processes

        Returns:
            List of dictionaries with processing results
        """
        if len(doc_paths) != len(save_paths):
            raise ValueError("Number of document files must match number of save paths")

        def process_single(args):
            doc_path, save_path = args
            try:
                print(f"\nProcessing: {os.path.basename(doc_path)}")
                pipeline = DocExtractionPipeline(
                    doc_path,
                    save_path,
                    optimization_level=optimization_level,
                    use_cache=use_cache
                )
                pipeline.run_pipeline()
                return {
                    'success': True,
                    'doc_path': doc_path,
                    'save_path': save_path
                }
            except Exception as e:
                print(f"❌ Error processing {os.path.basename(doc_path)}: {e}")
                return {
                    'success': False,
                    'doc_path': doc_path,
                    'save_path': save_path,
                    'error': str(e)
                }

        results = []
        with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit all processing tasks
            future_to_doc = {
                executor.submit(process_single, (doc_file, save_path)): doc_file
                for doc_file, save_path in zip(doc_paths, save_paths)
            }

            # Process results as they complete
            total = len(future_to_doc)
            for i, future in enumerate(concurrent.futures.as_completed(future_to_doc), 1):
                doc_file = future_to_doc[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    print(f"❌ Error in parallel processing for {os.path.basename(doc_file)}: {e}")
                    results.append({
                        'success': False,
                        'doc_path': doc_file,
                        'error': str(e)
                    })

                # Show progress
                print(f"Progress: {i}/{total} ({round(i/total*100)}%)")

        return results


if __name__ == "__main__":
    import sys
    import os
    import argparse

    # Create argument parser for command line usage
    parser = argparse.ArgumentParser(description="Extract content from documents using the DocExtractionPipeline")
    parser.add_argument("--input", required=True, help="Path to the input document file (PDF, DOCX, etc.)")
    parser.add_argument("--output", default="./output", help="Directory to save extracted data (default: ./output)")
    parser.add_argument("--optimization", choices=["speed", "balanced", "quality"], default="balanced",
                        help="Optimization level (default: balanced)")
    parser.add_argument("--cache", type=lambda x: x.lower() in ['true', 'yes', '1', 't', 'y'], default=True,
                        help="Whether to use caching (default: true)")

    args = parser.parse_args()

    # Validate input file exists
    if not os.path.exists(args.input):
        print(f"❌ Error: Input document not found: {args.input}")
        sys.exit(1)

    # Create output directory if it doesn't exist
    os.makedirs(args.output, exist_ok=True)

    print(f"Starting document extraction pipeline:")
    print(f"  Input document: {args.input}")
    print(f"  Output directory: {args.output}")
    print(f"  Optimization level: {args.optimization}")
    print(f"  Using cache: {args.cache}")

    # Create and run the pipeline
    try:
        start_time = time.time()
        pipeline = DocExtractionPipeline(
            doc_path=args.input,
            save_path=args.output,
            optimization_level=args.optimization,
            use_cache=args.cache
        )

        pipeline.run_pipeline()

        total_time = time.time() - start_time
        print(f"\n✅ Document extraction completed successfully")
        print(f"⏱️ Total processing time: {round(total_time, 2)} seconds")

    except Exception as e:
        print(f"❌ Error during document extraction: {e}")
        sys.exit(1)