#!/usr/bin/env python3
"""
Simple markdown-based PDF extractor.

Uses Docling's markdown export to get text with linebreaks,
then puts it in the JSON structure you need.

Author: Anand Jadhav
Date: 2025-01-27
"""

import json
import re
from pathlib import Path
from docling.document_converter import DocumentConverter


def extract_pdf_with_markdown_linebreaks(pdf_path: str, output_path: str):
    """
    Extract PDF using markdown to preserve linebreaks.
    
    Args:
        pdf_path: Path to PDF file
        output_path: Path to save JSON output
    """
    # Step 1: Convert PDF using Docling
    converter = DocumentConverter()
    result = converter.convert(pdf_path)
    
    # Step 2: Get both dictionary structure and markdown text
    data = result.document.export_to_dict()
    markdown_text = result.document.export_to_markdown()
    
    # Step 3: Clean markdown text to get plain text with linebreaks
    clean_text = clean_markdown_to_plain_text(markdown_text)
    
    # Step 4: Update the JSON structure with the markdown text
    update_json_with_markdown_text(data, clean_text)
    
    # Step 5: Save to JSON
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    return data


def clean_markdown_to_plain_text(markdown_text):
    """Convert markdown to plain text while keeping linebreaks."""
    
    if not markdown_text:
        return ""
    
    text = markdown_text
    
    # Remove markdown formatting
    text = re.sub(r'^#{1,6}\s+', '', text, flags=re.MULTILINE)  # Headers
    text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)  # Bold
    text = re.sub(r'\*([^*]+)\*', r'\1', text)  # Italic
    text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)  # Links
    text = re.sub(r'```[^`]*```', '', text, flags=re.DOTALL)  # Code blocks
    text = re.sub(r'`([^`]+)`', r'\1', text)  # Inline code
    
    # Clean up spacing but keep linebreaks
    text = re.sub(r' +', ' ', text)  # Multiple spaces to single space
    text = re.sub(r'\n{3,}', '\n\n', text)  # Max 2 consecutive linebreaks
    
    return text.strip()


def update_json_with_markdown_text(data, markdown_text):
    """Update the JSON data with markdown text that has linebreaks."""
    
    # Update page text
    if 'pages' in data and data['pages']:
        # Put the markdown text in the first page
        first_page_key = list(data['pages'].keys())[0]
        data['pages'][first_page_key]['text'] = markdown_text
        
        # Mark that we used markdown
        data['pages'][first_page_key]['source'] = 'markdown_with_linebreaks'
    
    # Optionally update other text fields too
    # For now, we'll focus on the page text which is the main requirement


def main():
    """Main function."""
    
    # Your file paths
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    output_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder\simple_markdown_output.json"
    
    # Check if PDF exists
    if not Path(pdf_file).exists():
        print("PDF file not found!")
        return
    
    # Create output directory
    Path(output_file).parent.mkdir(parents=True, exist_ok=True)
    
    # Extract PDF using markdown linebreaks
    data = extract_pdf_with_markdown_linebreaks(pdf_file, output_file)
    
    print(f"Extraction completed: {output_file}")
    
    # Show results
    if 'pages' in data and data['pages']:
        first_page = list(data['pages'].values())[0]
        if 'text' in first_page:
            text = first_page['text']
            linebreaks = text.count('\n')
            
            print(f"Text length: {len(text)} characters")
            print(f"Linebreaks: {linebreaks}")
            
            if linebreaks > 0:
                print("✅ SUCCESS: Linebreaks preserved from markdown!")
                print(f"Sample: {text[:150]}...")
            else:
                print("❌ No linebreaks found")


if __name__ == "__main__":
    main()
