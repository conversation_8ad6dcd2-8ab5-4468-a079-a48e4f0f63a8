#!/usr/bin/env python3
"""
Debug script to analyze Docling text elements and their positions.

This script helps understand how text elements are positioned in the PDF
so we can reconstruct the original linebreaks correctly.

Author: <PERSON> Jadhav
Date: 2025-01-27
"""

import json
from pathlib import Path
from docling.document_converter import DocumentConverter


def debug_text_elements(pdf_path: str):
    """
    Debug text elements to understand their structure and positioning.
    
    Args:
        pdf_path: Path to PDF file
    """
    print("Debugging Text Elements")
    print("=" * 30)
    
    # Convert PDF using Docling
    converter = DocumentConverter()
    result = converter.convert(pdf_path)
    data = result.document.export_to_dict()
    
    # Analyze the structure
    print(f"Document sections: {list(data.keys())}")
    
    if 'pages' in data:
        print(f"Pages: {len(data['pages'])}")
        
        # Show first page raw text
        first_page_key = list(data['pages'].keys())[0]
        first_page = data['pages'][first_page_key]
        if 'text' in first_page:
            raw_text = first_page['text']
            print(f"\nFirst page raw text ({len(raw_text)} chars):")
            print(f"'{raw_text[:200]}...'")
            print(f"Raw linebreaks: {raw_text.count(chr(10))}")
    
    if 'texts' in data:
        print(f"\nText elements: {len(data['texts'])}")
        
        # Analyze first page text elements
        first_page_elements = []
        for element in data['texts']:
            if (element.get('prov') and 
                len(element['prov']) > 0 and
                str(element['prov'][0].get('page_no', '')) == '1'):
                first_page_elements.append(element)
        
        print(f"First page text elements: {len(first_page_elements)}")
        
        # Show first few elements with their positions
        print("\nFirst 10 text elements:")
        for i, element in enumerate(first_page_elements[:10]):
            text = element.get('text', '')
            prov = element.get('prov', [{}])[0]
            bbox = prov.get('bbox', {})
            
            print(f"\nElement {i+1}:")
            print(f"  Text: '{text}'")
            print(f"  Position: top={bbox.get('t', 'N/A')}, bottom={bbox.get('b', 'N/A')}")
            print(f"  Position: left={bbox.get('l', 'N/A')}, right={bbox.get('r', 'N/A')}")
        
        # Try to reconstruct text from elements
        print("\nReconstructing text from elements:")
        reconstructed = reconstruct_from_elements(first_page_elements)
        print(f"Reconstructed text:")
        print(f"'{reconstructed[:200]}...'")
        print(f"Reconstructed linebreaks: {reconstructed.count(chr(10))}")
    
    # Save debug data
    debug_file = "debug_text_elements.json"
    with open(debug_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    print(f"\nFull data saved to: {debug_file}")


def reconstruct_from_elements(elements):
    """Reconstruct text from positioned elements."""
    
    if not elements:
        return ""
    
    # Sort by position
    sorted_elements = sorted(elements, key=lambda x: (
        x.get('prov', [{}])[0].get('bbox', {}).get('t', 0),  # top
        x.get('prov', [{}])[0].get('bbox', {}).get('l', 0)   # left
    ))
    
    text_parts = []
    last_bottom = None
    
    for element in sorted_elements:
        text = element.get('text', '').strip()
        if not text:
            continue
        
        bbox = element.get('prov', [{}])[0].get('bbox', {})
        current_top = bbox.get('t', 0)
        
        # Add linebreak if there's a vertical gap
        if last_bottom is not None:
            vertical_gap = current_top - last_bottom
            if vertical_gap > 3:  # Threshold for line break
                text_parts.append('\n')
        
        text_parts.append(text)
        last_bottom = bbox.get('b', current_top)
    
    return ' '.join(text_parts)


def main():
    """Main function."""
    
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    
    if not Path(pdf_file).exists():
        print("PDF file not found!")
        return
    
    debug_text_elements(pdf_file)


if __name__ == "__main__":
    main()
