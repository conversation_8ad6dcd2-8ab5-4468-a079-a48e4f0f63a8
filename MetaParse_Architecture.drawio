<mxfile host="app.diagrams.net" modified="2023-07-15T12:00:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" etag="abc123" version="14.8.5" type="device">
  <diagram id="prtHgNgQTEPvFCAcTncT" name="MetaParse Architecture">
    <mxGraphModel dx="1422" dy="798" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Title -->
        <mxCell id="2" value="MetaParse System Architecture" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="164" y="20" width="500" height="40" as="geometry" />
        </mxCell>
        
        <!-- Input Layer -->
        <mxCell id="3" value="Input Layer" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="40" y="120" width="160" height="130" as="geometry" />
        </mxCell>
        <mxCell id="4" value="- PDF Files&#xa;- DOCX Files&#xa;- Image Files&#xa;- Batch Folder Input" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="3">
          <mxGeometry y="26" width="160" height="104" as="geometry" />
        </mxCell>
        
        <!-- Core Pipeline -->
        <mxCell id="5" value="Core Pipeline" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="334" y="120" width="160" height="130" as="geometry" />
        </mxCell>
        <mxCell id="6" value="- DocExtractionPipeline&#xa;- Orchestration&#xa;- Format Detection&#xa;- Optimization Control&#xa;- Process Management" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="5">
          <mxGeometry y="26" width="160" height="104" as="geometry" />
        </mxCell>
        
        <!-- Extraction Layer -->
        <mxCell id="7" value="Extraction Layer" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="40" y="320" width="160" height="130" as="geometry" />
        </mxCell>
        <mxCell id="8" value="- Document Extractor&#xa;- Text Extraction&#xa;- Structure Recognition&#xa;- Format Converters&#xa;- DOCX to PDF" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="7">
          <mxGeometry y="26" width="160" height="104" as="geometry" />
        </mxCell>
        
        <!-- Processing Layer -->
        <mxCell id="9" value="Processing Layer" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="334" y="320" width="160" height="130" as="geometry" />
        </mxCell>
        <mxCell id="10" value="- Document Segmenter&#xa;- Table Processor&#xa;- Image Processor&#xa;- Content Analysis&#xa;- Metadata Generation" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="9">
          <mxGeometry y="26" width="160" height="104" as="geometry" />
        </mxCell>
        
        <!-- Output Layer -->
        <mxCell id="11" value="Output Layer" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="40" y="520" width="160" height="130" as="geometry" />
        </mxCell>
        <mxCell id="12" value="- File Manager&#xa;- JSON Output&#xa;- Excel Files&#xa;- Image Files&#xa;- Summary Generator" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="11">
          <mxGeometry y="26" width="160" height="104" as="geometry" />
        </mxCell>
        
        <!-- Interface Layer -->
        <mxCell id="13" value="Interface Layer" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="334" y="520" width="160" height="130" as="geometry" />
        </mxCell>
        <mxCell id="14" value="- Command Line Interface&#xa;- Processing Commands&#xa;- Batch Operations&#xa;- Configuration Options&#xa;- API Interface" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="13">
          <mxGeometry y="26" width="160" height="104" as="geometry" />
        </mxCell>
        
        <!-- Connections -->
        <!-- Input to Core -->
        <mxCell id="15" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="4" target="6">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Core to Extraction -->
        <mxCell id="16" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.75;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="6" target="7">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <Array as="points">
              <mxPoint x="120" y="228" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- Extraction to Core -->
        <mxCell id="17" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.25;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="8" target="5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <Array as="points">
              <mxPoint x="374" y="385" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- Core to Processing -->
        <mxCell id="18" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="5" target="9">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Processing to Core -->
        <mxCell id="19" value="" style="endArrow=classic;html=1;exitX=0.75;exitY=0;exitDx=0;exitDy=0;entryX=0.75;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="9" target="5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <Array as="points">
              <mxPoint x="454" y="290" />
              <mxPoint x="454" y="290" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- Processing to Output -->
        <mxCell id="20" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.75;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="10" target="11">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <Array as="points">
              <mxPoint x="120" y="424" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- Core to Output -->
        <mxCell id="21" value="" style="endArrow=classic;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.25;entryDx=0;entryDy=0;" edge="1" parent="1" source="9" target="11">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
            <Array as="points">
              <mxPoint x="374" y="480" />
              <mxPoint x="240" y="480" />
              <mxPoint x="240" y="553" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- Core to Interface -->
        <mxCell id="22" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="9" target="13">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Interface to Core -->
        <mxCell id="23" value="" style="endArrow=classic;html=1;exitX=0.75;exitY=0;exitDx=0;exitDy=0;entryX=0.75;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="13" target="9">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Output to Interface -->
        <mxCell id="24" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="12" target="14">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Legend -->
        <mxCell id="25" value="Legend" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
          <mxGeometry x="600" y="120" width="160" height="130" as="geometry" />
        </mxCell>
        <mxCell id="26" value="→ Data Flow&#xa;&#xa;Colors represent different&#xa;functional components&#xa;of the system" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="25">
          <mxGeometry y="26" width="160" height="104" as="geometry" />
        </mxCell>
        
        <!-- External Systems -->
        <mxCell id="27" value="External Systems" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="600" y="320" width="160" height="130" as="geometry" />
        </mxCell>
        <mxCell id="28" value="- LibreOffice&#xa;- Microsoft Word&#xa;- File System&#xa;- User Applications" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="27">
          <mxGeometry y="26" width="160" height="104" as="geometry" />
        </mxCell>
        
        <!-- External Systems Connections -->
        <mxCell id="29" value="" style="endArrow=classic;startArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="10" target="28">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="430" as="sourcePoint" />
            <mxPoint x="440" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Document Flow Title -->
        <mxCell id="30" value="Document Processing Flow" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="164" y="70" width="500" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
