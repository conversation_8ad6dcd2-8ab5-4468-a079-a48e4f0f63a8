#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run the enhanced linebreak preservation extraction.

This script runs the enhanced DocExtractor with your specific PDF file
and demonstrates the improved linebreak preservation functionality.

Author: <PERSON> Jadhav
Date: 2025-01-27
"""

import subprocess
import sys
import json
from pathlib import Path

def run_enhanced_extraction():
    """Run the enhanced extraction with linebreak preservation."""
    
    print("🚀 Running Enhanced Linebreak Preservation Extraction")
    print("=" * 60)
    
    # Your file paths
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    output_folder = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder"
    
    print(f"📄 PDF file: {Path(pdf_file).name}")
    print(f"📁 Output folder: {output_folder}")
    
    # Check prerequisites
    if not Path(pdf_file).exists():
        print(f"\n❌ PDF file not found: {pdf_file}")
        print("💡 Please ensure the PDF file exists at the specified location")
        return False
    
    if not Path("metaparse/extractors/doc_extractor_linebreak.py").exists():
        print("\n❌ DocExtractor module not found")
        print("💡 Please run this script from the MetaParse project root directory")
        return False
    
    print("\n✅ Prerequisites met!")
    
    # Run the enhanced extraction
    print("\n🔄 Running enhanced extraction...")
    print("   This may take a few moments depending on the PDF size...")
    
    try:
        # Run the main block with default settings (linebreak preservation enabled)
        cmd = [sys.executable, "metaparse/extractors/doc_extractor_linebreak.py"]
        
        print(f"\n🚀 Command: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)  # 5 minute timeout
        
        if result.returncode == 0:
            print("\n✅ Extraction completed successfully!")
            
            # Show output
            print("\n📋 Extraction Output:")
            print(result.stdout)
            
            # Check the output file
            expected_output = Path(output_folder) / "04760587J.article.002_extracted.json"
            
            if expected_output.exists():
                print(f"\n📁 Output file created: {expected_output}")
                
                # Analyze the output
                analyze_output_file(expected_output)
                
            else:
                print(f"\n⚠️ Expected output file not found: {expected_output}")
                
        else:
            print("\n❌ Extraction failed!")
            print("Error output:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("\n⏰ Extraction timed out (>5 minutes)")
        print("💡 The PDF might be very large or complex")
        return False
    except Exception as e:
        print(f"\n❌ Error running extraction: {e}")
        return False
    
    return True

def analyze_output_file(output_file: Path):
    """Analyze the output file to check linebreak preservation."""
    
    print(f"\n🔍 Analyzing Output File: {output_file.name}")
    print("-" * 40)
    
    try:
        with open(output_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        file_size = output_file.stat().st_size
        print(f"📊 File size: {file_size:,} bytes")
        
        # Check pages
        if 'pages' in data and data['pages']:
            total_pages = len(data['pages'])
            print(f"📄 Total pages extracted: {total_pages}")
            
            # Analyze first page
            first_page_key = list(data['pages'].keys())[0]
            first_page = data['pages'][first_page_key]
            
            if 'text' in first_page:
                text = first_page['text']
                
                # Count various elements
                total_chars = len(text)
                linebreak_count = text.count('\n')
                word_count = len(text.split())
                
                print(f"\n📝 First Page Analysis:")
                print(f"   Total characters: {total_chars:,}")
                print(f"   Word count: {word_count:,}")
                print(f"   Linebreaks (\\n): {linebreak_count}")
                
                if linebreak_count > 0:
                    print(f"   ✅ Linebreaks are preserved!")
                    
                    # Calculate linebreak density
                    linebreak_density = (linebreak_count / total_chars) * 100
                    print(f"   📊 Linebreak density: {linebreak_density:.2f}%")
                    
                else:
                    print(f"   ❌ No linebreaks found - preservation may not be working")
                
                # Check for preservation flags
                if first_page.get('linebreaks_preserved'):
                    print(f"   ✅ Linebreak preservation flag: True")
                    
                    if 'elements_processed' in first_page:
                        print(f"   🔧 Elements processed: {first_page['elements_processed']}")
                    
                    if 'paragraph_count' in first_page:
                        print(f"   📄 Paragraphs detected: {first_page['paragraph_count']}")
                        
                else:
                    print(f"   ⚠️ Linebreak preservation flag not found")
                
                # Show sample text with linebreaks highlighted
                print(f"\n📖 Sample Text (first 300 characters):")
                sample_text = text[:300]
                
                # Replace \n with visible representation for display
                display_text = sample_text.replace('\n', '\\n\n   ')
                print(f"   '{display_text}...'")
                
                # Count linebreaks in sample
                sample_linebreaks = sample_text.count('\n')
                print(f"\n🔍 Linebreaks in sample: {sample_linebreaks}")
                
            else:
                print("⚠️ No text found in first page")
        
        else:
            print("❌ No pages found in extracted data")
        
        # Check other sections
        sections = ['texts', 'tables', 'pictures']
        for section in sections:
            if section in data and data[section]:
                count = len(data[section])
                print(f"📋 {section.capitalize()}: {count} items")
        
    except Exception as e:
        print(f"❌ Error analyzing output file: {e}")

def show_comparison_tip():
    """Show tip for comparing results."""
    
    print(f"\n💡 Comparison Tip:")
    print("To see the difference, you can:")
    print("1. Run with linebreaks disabled:")
    print("   python metaparse/extractors/doc_extractor_linebreak.py --no-linebreaks")
    print("2. Compare the two output files to see the difference")
    print("3. Look for \\n characters in the text fields")

def main():
    """Main function."""
    
    print("🎯 Enhanced Linebreak Preservation Extraction")
    print("=" * 60)
    
    print("\n💡 This script runs the enhanced DocExtractor with your PDF file")
    print("   and analyzes the output to verify linebreak preservation.")
    
    success = run_enhanced_extraction()
    
    if success:
        print(f"\n🎉 Success! Enhanced linebreak preservation is working.")
        show_comparison_tip()
        
        print(f"\n📚 What to look for in the output:")
        print("✅ Linebreaks (\\n characters) in the text")
        print("✅ 'linebreaks_preserved': true flag")
        print("✅ 'paragraph_count' and 'elements_processed' fields")
        print("✅ Proper text structure with preserved formatting")
        
    else:
        print(f"\n❌ Extraction failed. Please check the error messages above.")
    
    print(f"\n✨ Script completed!")

if __name__ == "__main__":
    main()
