## Project Structure

The MetaParse project follows a well-organized modular structure that separates concerns and promotes maintainability. Below is the project structure based on the imports used in the codebase:

```
metaparse/
│
├── __init__.py                  # Package initialization
├── cli.py                       # Command-line interface
├── core/                        # Core components
│   ├── __init__.py
│   └── pipeline.py              # Main document extraction pipeline
├── extractors/                  # Document extraction components
│   ├── __init__.py
│   └── doc_extractor.py         # Document text extraction
├── processors/                  # Content processors
│   ├── __init__.py
│   ├── doc_segmenter.py         # Document segmentation
│   ├── image_processor.py       # Image extraction and processing
│   └── table_processor.py       # Table extraction and processing
└── utils/                       # Utility modules
    ├── __init__.py
    ├── file_manager.py          # File operations management
    └── docx_to_pdf.py           # DOCX to PDF conversion
```

### Module Dependencies

The diagram below shows the dependencies between the main modules in the MetaParse project:

```
                                 ┌─────────────────┐
                                 │                 │
                                 │     cli.py      │
                                 │                 │
                                 └────────┬────────┘
                                          │
                                          │ imports
                                          ▼
┌─────────────────┐            ┌─────────────────────┐
│                 │            │                     │
│  pandas, glob   │◄───imports─┤  pipeline.py        │
│                 │            │  (DocExtractionPipe-│
└─────────────────┘            │  line)              │
                               │                     │
                               └─────────┬───────────┘
                                         │
                 ┌─────────────────┬─────┴─────┬─────────────────┐
                 │                 │           │                 │
                 │ imports         │ imports   │ imports         │
                 ▼                 ▼           ▼                 ▼
    ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
    │                 │  │                 │  │                 │  │                 │
    │ doc_extractor.py│  │ file_manager.py │  │ docx_to_pdf.py  │  │ doc_segmenter.py│
    │                 │  │                 │  │                 │  │                 │
    └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘
                                                                    
                                                                    
    ┌─────────────────┐  ┌─────────────────┐
    │                 │  │                 │
    │image_processor.py  │table_processor.py
    │                 │  │                 │
    └─────────────────┘  └─────────────────┘
```

### Key Dependencies

- **cli.py**: Depends on pipeline.py for document processing functionality
- **pipeline.py**: Central module that orchestrates the entire extraction process
  - Imports doc_extractor.py for text extraction
  - Imports file_manager.py for file operations
  - Imports docx_to_pdf.py for document conversion
  - Imports doc_segmenter.py for content segmentation
  - Imports image_processor.py for image extraction
  - Imports table_processor.py for table extraction

This modular structure allows for easy maintenance and extension of the codebase, as each component has a specific responsibility and can be modified independently of the others.