# DocExtractor Main Block Usage Guide

## Overview

The `doc_extractor_linebreak.py` module now includes a comprehensive main block that provides a command-line interface for testing and using the linebreak preservation functionality.

## Quick Start

### Basic Usage

```bash
# Extract text from a single PDF with linebreak preservation (default)
python metaparse/extractors/doc_extractor_linebreak.py document.pdf

# Extract text without linebreak preservation
python metaparse/extractors/doc_extractor_linebreak.py document.pdf --no-linebreaks

# Use custom linebreak character
python metaparse/extractors/doc_extractor_linebreak.py document.pdf --linebreak-char "|"

# Specify output directory
python metaparse/extractors/doc_extractor_linebreak.py document.pdf --output-dir ./output
```

### Batch Processing

```bash
# Process multiple files in batch mode
python metaparse/extractors/doc_extractor_linebreak.py *.pdf --batch

# Batch processing with custom output directory
python metaparse/extractors/doc_extractor_linebreak.py doc1.pdf doc2.pdf doc3.pdf --batch --output-dir ./batch_output
```

### Demo Mode

```bash
# Run demonstration to see available configurations
python metaparse/extractors/doc_extractor_linebreak.py --demo
```

## Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `files` | PDF file(s) to process | Required |
| `--no-linebreaks` | Disable linebreak preservation | Enabled |
| `--linebreak-char` | Character to use for linebreaks | `\n` |
| `--paragraph-separator` | Character(s) for paragraph separation | `\n\n` |
| `--output-dir` | Output directory for extracted files | Current directory |
| `--batch` | Process multiple files in batch mode | Single file mode |
| `--optimization-level` | Extraction optimization level | `quality` |
| `--no-cache` | Disable caching | Caching enabled |
| `--demo` | Run demonstration mode | Normal mode |

## Examples

### Example 1: Basic Extraction with Linebreaks

```bash
python metaparse/extractors/doc_extractor_linebreak.py research_paper.pdf
```

**Output:**
```
🚀 MetaParse DocExtractor with Linebreak Preservation
============================================================

📋 Configuration:
   Linebreak preservation: ✅ Enabled
   Linebreak character: '\n'
   Paragraph separator: '\n\n'
   Optimization level: quality
   Cache: ✅ Enabled

📄 Processing: research_paper.pdf
✅ Extraction completed!
📁 Output saved to: research_paper_extracted.json
📊 Pages extracted: 15

📝 Sample text (first 200 chars):
'Abstract\nThis paper presents a novel approach to...\nThe methodology involves...\n\nIntroduction\nIn recent years, there has been...'
🔍 Linebreaks found: 5
✅ Linebreaks preserved successfully
📄 Paragraphs detected: 3
```

### Example 2: Custom Linebreak Characters

```bash
python metaparse/extractors/doc_extractor_linebreak.py document.pdf --linebreak-char " | " --paragraph-separator " || "
```

This will use pipe characters instead of newlines for better compatibility with certain text processing pipelines.

### Example 3: Batch Processing

```bash
python metaparse/extractors/doc_extractor_linebreak.py *.pdf --batch --output-dir ./extracted_docs
```

**Output:**
```
📚 Processing 5 files in batch mode...
✅ doc1.pdf: 10 pages
✅ doc2.pdf: 8 pages
✅ doc3.pdf: 12 pages
✅ doc4.pdf: 6 pages
✅ doc5.pdf: 15 pages

📊 Batch Summary: 5/5 files processed successfully
```

### Example 4: Disable Linebreak Preservation

```bash
python metaparse/extractors/doc_extractor_linebreak.py document.pdf --no-linebreaks
```

This will extract text using the traditional method (spaces instead of linebreaks).

## Output Format

The extracted text is saved as JSON files with the following structure:

```json
{
  "schema_name": "MetaParseDocument",
  "pages": {
    "1": {
      "text": "First paragraph line 1.\nFirst paragraph line 2.\n\nSecond paragraph starts here.\nSecond paragraph continues.",
      "linebreaks_preserved": true,
      "paragraph_count": 2
    }
  },
  "texts": [...],
  "tables": [...],
  "pictures": [...]
}
```

## Integration with Existing Code

You can also use the enhanced DocExtractor programmatically:

```python
from metaparse.extractors.doc_extractor_linebreak import DocExtractor

# Create extractor with linebreak preservation
extractor = DocExtractor(
    doc_path="document.pdf",
    optimization_level='quality',
    custom_settings={
        'text_processing': {
            'preserve_linebreaks': True,
            'linebreak_character': '\n',
            'paragraph_separator': '\n\n'
        }
    }
)

# Extract text
result = extractor.extract_text_optimized()

# Access extracted text with preserved linebreaks
for page_num, page_data in result['pages'].items():
    text_with_linebreaks = page_data['text']
    print(f"Page {page_num}: {text_with_linebreaks[:100]}...")
```

## Troubleshooting

### Common Issues

1. **"No valid PDF files found!"**
   - Ensure the file paths are correct
   - Check that files have `.pdf` extension
   - Verify files exist and are accessible

2. **"Error processing file"**
   - Check if the PDF is corrupted
   - Try with `--no-cache` option
   - Verify sufficient disk space for output

3. **Unexpected linebreak behavior**
   - Check the `--linebreak-char` setting
   - Verify `--no-linebreaks` is not set when you want linebreaks
   - Review the sample output to confirm settings

### Performance Tips

- Use `--optimization-level speed` for faster processing
- Enable caching for repeated processing of same files
- Use batch mode for processing multiple files efficiently

## Related Files

- `LINEBREAK_PRESERVATION_GUIDE.md` - Comprehensive guide
- `test_linebreak_preservation.py` - Test script
- `example_linebreak_usage.py` - Usage examples

## Support

For issues or questions about linebreak preservation:
1. Check the documentation files
2. Run the demo mode: `python doc_extractor_linebreak.py --demo`
3. Test with the provided example scripts
