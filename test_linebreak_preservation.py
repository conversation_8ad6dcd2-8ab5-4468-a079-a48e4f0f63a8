#!/usr/bin/env python3
"""
Test script to demonstrate linebreak preservation in Docling text extraction.

This script shows how to use the enhanced DocExtractor with linebreak preservation
to extract text from PDF files while maintaining paragraph structure.

Author: Anand Jadhav
Date: 2025-01-27
"""

import os
import json
from metaparse.extractors.doc_extractor_linebreak import DocExtractor, DocExtractorConfig

def test_linebreak_preservation():
    """Test linebreak preservation functionality."""
    
    # Test with a sample PDF file (you can replace this with your own PDF)
    test_pdf_path = "sample_document.pdf"  # Replace with actual PDF path
    
    if not os.path.exists(test_pdf_path):
        print(f"⚠️ Test PDF not found: {test_pdf_path}")
        print("Please provide a valid PDF file path to test the functionality.")
        return
    
    print("🔍 Testing Docling linebreak preservation...")
    print(f"📄 Processing: {test_pdf_path}")
    
    # Test 1: Default settings (with linebreak preservation enabled)
    print("\n1️⃣ Testing with linebreak preservation ENABLED:")
    
    config_with_linebreaks = DocExtractorConfig(
        optimization_level='quality',
        custom_settings={
            'text_processing': {
                'preserve_linebreaks': True,
                'linebreak_character': '\n',
                'paragraph_separator': '\n\n'
            }
        }
    )
    
    extractor_with_linebreaks = DocExtractor(
        test_pdf_path,
        optimization_level='quality',
        use_cache=False,
        custom_settings=config_with_linebreaks.settings
    )
    
    try:
        result_with_linebreaks = extractor_with_linebreaks.extract_text_optimized()
        
        # Save result to file
        output_with_linebreaks = "output_with_linebreaks.json"
        with open(output_with_linebreaks, 'w', encoding='utf-8') as f:
            json.dump(result_with_linebreaks, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Extraction completed with linebreaks preserved")
        print(f"📁 Output saved to: {output_with_linebreaks}")
        
        # Show sample text from first page
        if result_with_linebreaks.get('pages'):
            first_page = list(result_with_linebreaks['pages'].values())[0]
            sample_text = first_page.get('text', '')[:300]
            print(f"\n📝 Sample text (first 300 chars):")
            print(f"'{sample_text}...'")
            print(f"\n🔍 Linebreak count in sample: {sample_text.count(chr(10))}")  # Count \n characters
            
    except Exception as e:
        print(f"❌ Error with linebreak preservation: {e}")
    
    # Test 2: Without linebreak preservation
    print("\n2️⃣ Testing with linebreak preservation DISABLED:")
    
    config_without_linebreaks = DocExtractorConfig(
        optimization_level='quality',
        custom_settings={
            'text_processing': {
                'preserve_linebreaks': False,
                'linebreak_character': '\n',
                'paragraph_separator': '\n\n'
            }
        }
    )
    
    extractor_without_linebreaks = DocExtractor(
        test_pdf_path,
        optimization_level='quality',
        use_cache=False,
        custom_settings=config_without_linebreaks.settings
    )
    
    try:
        result_without_linebreaks = extractor_without_linebreaks.extract_text_optimized()
        
        # Save result to file
        output_without_linebreaks = "output_without_linebreaks.json"
        with open(output_without_linebreaks, 'w', encoding='utf-8') as f:
            json.dump(result_without_linebreaks, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Extraction completed without linebreak preservation")
        print(f"📁 Output saved to: {output_without_linebreaks}")
        
        # Show sample text from first page
        if result_without_linebreaks.get('pages'):
            first_page = list(result_without_linebreaks['pages'].values())[0]
            sample_text = first_page.get('text', '')[:300]
            print(f"\n📝 Sample text (first 300 chars):")
            print(f"'{sample_text}...'")
            print(f"\n🔍 Linebreak count in sample: {sample_text.count(chr(10))}")  # Count \n characters
            
    except Exception as e:
        print(f"❌ Error without linebreak preservation: {e}")
    
    print("\n🎯 Comparison Summary:")
    print("- With linebreak preservation: Text maintains paragraph structure with \\n characters")
    print("- Without linebreak preservation: Text is joined with spaces, losing line structure")
    print("\n💡 Check the generated JSON files to see the difference in text formatting!")

def demonstrate_custom_settings():
    """Demonstrate different linebreak preservation settings."""
    
    print("\n🛠️ Demonstrating custom linebreak settings:")
    
    # Example 1: Custom linebreak character
    custom_settings_1 = {
        'text_processing': {
            'preserve_linebreaks': True,
            'linebreak_character': ' | ',  # Use pipe separator instead of newline
            'paragraph_separator': ' || '
        }
    }
    
    # Example 2: Different paragraph separator
    custom_settings_2 = {
        'text_processing': {
            'preserve_linebreaks': True,
            'linebreak_character': '\n',
            'paragraph_separator': '\n---\n'  # Use horizontal rule as paragraph separator
        }
    }
    
    print("📋 Available custom settings:")
    print("1. preserve_linebreaks: True/False - Enable/disable linebreak preservation")
    print("2. linebreak_character: String - Character(s) to use for line breaks (default: '\\n')")
    print("3. paragraph_separator: String - Character(s) to use between paragraphs (default: '\\n\\n')")
    
    print("\n💡 Example configurations:")
    print("Config 1 - Pipe separators:")
    print(json.dumps(custom_settings_1, indent=2))
    
    print("\nConfig 2 - Horizontal rule separators:")
    print(json.dumps(custom_settings_2, indent=2))

if __name__ == "__main__":
    print("🚀 Docling Linebreak Preservation Test")
    print("=" * 50)
    
    # Run the main test
    test_linebreak_preservation()
    
    # Show custom settings examples
    demonstrate_custom_settings()
    
    print("\n✨ Test completed!")
    print("📚 For more information, check the DocExtractor class documentation.")
