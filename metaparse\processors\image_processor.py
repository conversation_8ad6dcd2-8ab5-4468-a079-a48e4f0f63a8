"""
Image processing module for the MetaParse package.

This module provides classes for extracting and processing images from documents.
Author : Anand Jadhav
Date : 2025-05-20
"""

import re
import time
import fitz
import json
from PIL import Image
import io
from typing import Dict, List, Any, Optional, Union

from metaparse.utils.file_manager import FileManager


class ImageExtractor:
    """
    Extracts and processes images from document content.

    This class handles the extraction, saving, and metadata generation for images
    found in documents.
    """

    def __init__(self, result_dict: Dict[str, Any], doc_path: str, file_manager: FileManager):
        """
        Initialize the image extractor.

        Args:
            result_dict: Document data in dictionary format
            doc_path: Path to the document file
            file_manager: FileManager instance for saving files
        """
        self.result_dict = result_dict
        self.doc_path = doc_path
        self.file_manager = file_manager



    def replace_ligatures(self, text: str) -> str:
        """
        Replace ligature encodings with their proper English character equivalents.

        Args:
            text: Text that may contain ligature encodings

        Returns:
            Text with ligatures replaced by their proper character equivalents
        """
        if not text:
            return text

        import re

        # Common ligature mappings
        ligature_map = {
            # Latin ligatures
            "/uniFB00": "ff",    # LATIN SMALL LIGATURE FF
            "/uniFB01": "fi",    # LATIN SMALL LIGATURE FI
            "/uniFB02": "fl",    # LATIN SMALL LIGATURE FL
            "/uniFB03": "ffi",   # LATIN SMALL LIGATURE FFI
            "/uniFB04": "ffl",   # LATIN SMALL LIGATURE FFL
            "/uniFB05": "st",    # LATIN SMALL LIGATURE LONG S T
            "/uniFB06": "st",    # LATIN SMALL LIGATURE ST

            # Other common encodings that might appear
            "/uni0027": "'",     # APOSTROPHE
            "/uni2019": "'",     # RIGHT SINGLE QUOTATION MARK
            # "/uni201C": """,     ## LEFT DOUBLE QUOTATION MARK
            # "/uni201D": """,     # RIGHT DOUBLE QUOTATION MARK
            "/uni2013": "–",     # EN DASH
            "/uni2014": "—",     # EM DASH


            # Simplified versions that might appear
            "/FB00": "ff",
            "/FB01": "fi",
            "/FB02": "fl",
            "/FB03": "ffi",
            "/FB04": "ffl",
            "/FB05": "st",
            "/FB06": "st",

            # Additional patterns sometimes seen
            r"\ufb01": "fi",
            r"\ufb02": "fl",
            r"\u2019": "'",
            # r"\u201c": """,
            # r"\u201d": """,
        }

        # Step 1: Replace all ligatures with their proper English character equivalents
        for ligature, replacement in ligature_map.items():
            text = text.replace(ligature, replacement)

        # # Step 2: Fix spacing issues that might be introduced during replacement
        # # Create a list of all the replacement values (ff, fi, fl, etc.)
        # replacements = set(ligature_map.values())

        # # For each replacement value, create patterns to fix spacing issues
        # for repl in replacements:
        #     # Pattern 1: Fix cases where the replacement is surrounded by spaces
        #     # Example: "quanti fi ed" -> "quantified"
        #     pattern_with_spaces = r'(\w) ' + re.escape(repl) + r' (\w)'
        #     text = re.sub(pattern_with_spaces, r'\1' + repl + r'\2', text)

        #     # Pattern 2: Fix cases where there's a space before the replacement
        #     # Example: "quanti fi" -> "quantifi"
        #     pattern_space_before = r'(\w) ' + re.escape(repl) + r'(\w)'
        #     text = re.sub(pattern_space_before, r'\1' + repl + r'\2', text)

        #     # Pattern 3: Fix cases where there's a space after the replacement
        #     # Example: "fi ed" -> "fied"
        #     pattern_space_after = r'(\w)' + re.escape(repl) + r' (\w)'
        #     text = re.sub(pattern_space_after, r'\1' + repl + r'\2', text)

        #     # Pattern 4: Fix cases at word boundaries
        #     # Example: " fi " -> " fi"
        #     pattern_word_boundary = r'(\s)' + re.escape(repl) + r' '
        #     text = re.sub(pattern_word_boundary, r'\1' + repl, text)

        #     # Pattern 5: Fix cases at the end of a word
        #     # Example: "modi fi " -> "modifi "
        #     pattern_end_of_word = r'(\w) ' + re.escape(repl) + r'(\s)'
        #     text = re.sub(pattern_end_of_word, r'\1' + repl + r'\2', text)

        # # Step 3: Handle special compound ligatures that might have been broken
        # # Example: "f fi" -> "ffi" or "f fl" -> "ffl"
        # text = re.sub(r'f fi', 'ffi', text)
        # text = re.sub(r'f fl', 'ffl', text)

        return text

    def extract_picture_details(self) -> List[Dict[str, Any]]:
        """
        Extract details about pictures in the document.

        Returns:
            List of dictionaries containing picture details
        """
        picture_data = []

        texts = self.result_dict.get("texts", [])
        pictures = self.result_dict.get("pictures", [])
        picture_refs = set()

        # Prepare caption lookup from pictures
        for picture in pictures:
            for caption in picture.get("captions", []):
                if "$ref" in caption:
                    picture_refs.add(caption["$ref"])

        # Build a mapping of self_ref to text
        texts_dict = {text["self_ref"]: text for text in texts}
        picture_counter_by_page = {}

        # Process pictures as usual
        for picture in pictures:
            if picture.get("label") != "picture":
                continue

            prov_data = picture.get("prov", [{}])[0]
            bbox = prov_data.get("bbox", {})
            page_no = prov_data.get("page_no", None)
            if page_no is None:
                continue

            picture_counter_by_page.setdefault(page_no, 0)
            picture_counter_by_page[page_no] += 1
            figure_index = picture_counter_by_page[page_no]

            filename = f"{page_no}_image{figure_index}.png"

            # Match valid captions
            captions = []
            caption_refs = []

            # First, collect all caption references
            for caption in picture.get("captions", []):
                ref = caption.get("$ref")
                if ref and ref in texts_dict:
                    caption_refs.append(ref)

            # Find nearby text that might be captions but not explicitly linked
            if not caption_refs:
                # Look for captions near the image
                for text_id, text_obj in texts_dict.items():
                    text_prov = text_obj.get("prov", [{}])[0]
                    text_page = text_prov.get("page_no")

                    # Only consider text on the same page
                    if text_page != page_no:
                        continue

                    text_bbox = text_prov.get("bbox", {})
                    if not text_bbox:
                        continue

                    # Check if text is near the image (within 100 units)
                    if (abs(text_bbox.get("t", 0) - bbox.get("b", 0)) < 100 or
                        abs(text_bbox.get("b", 0) - bbox.get("t", 0)) < 100):

                        text_content = text_obj.get("text", "")
                        # Check if it looks like a caption
                        if re.search(r"\b(Figure|Fig\.?)\s*([A-Z]?\d+|S\d+|[IVXLCDMivxlcdm]+)", text_content, re.IGNORECASE): #|Table|Scheme|Structure
                            caption_refs.append(text_id)

            # Process all found caption references
            for ref in caption_refs:
                text_obj = texts_dict.get(ref)
                caption_text = text_obj.get("text", "")

                # Check if it's a valid caption format
                if re.search(r"\b(Figure|Fig\.?|Scheme|Structure)\s*([A-Za-z]?\d+[A-Za-z]?|S\d+[A-Za-z]?|[IVXLCDMivxlcdm]+)|\b[a-zA-Z]\b|\b\d\b", caption_text, re.IGNORECASE): #|Table
                    captions.append({
                        "$ref": ref,
                        "title": caption_text,
                        "bbox": text_obj.get("prov", [{}])[0].get("bbox", {})
                    })

            # Create a standardized bbox with only long format keys
            standardized_bbox = {
                "left": bbox.get("l", bbox.get("left", 0)), # - 5,  # Expand slightly for better extraction
                "top": bbox.get("t", bbox.get("top", 0)), # + 5,
                "right": bbox.get("r", bbox.get("right", 0)), # + 5,
                "bottom": bbox.get("b", bbox.get("bottom", 0)), # - 5,
                "coord_origin": bbox.get("coord_origin")
            }

            picture_data.append({
                "page_no": page_no,
                "bbox": standardized_bbox,
                "filename": filename,
                "captions": captions
            })

        # ➕ ADD missing captions from 'texts' (like "Fig. 5") if not already used
        # and try to associate them with nearby images
        standalone_captions = []
        for text_id, text_obj in texts_dict.items():
            if text_id in picture_refs:
                continue  # already used in pictures

            caption_text = text_obj.get("text", "")
            if re.search(r"\b(Figure|Fig\.?|Scheme|Structure)\s*([A-Za-z]?\d+[A-Za-z]?|S\d+[A-Za-z]?|[IVXLCDMivxlcdm]+)", caption_text, re.IGNORECASE): #|Table
                prov = text_obj.get("prov", [{}])[0]
                page_no = prov.get("page_no")
                bbox = prov.get("bbox", {})

                if page_no is None:
                    continue

                standalone_captions.append({
                    "page_no": page_no,
                    "bbox": bbox,
                    "text_id": text_id,
                    "caption_text": caption_text
                })

        # Try to match standalone captions with images
        for caption in standalone_captions:
            matched = False
            caption_page = caption["page_no"]
            caption_bbox = caption["bbox"]

            # Find the closest image on the same page
            closest_image = None
            min_distance = float('inf')

            for idx, image in enumerate(picture_data):
                if image["page_no"] != caption_page or image["filename"] is None:
                    continue

                image_bbox = image["bbox"]

                # Get image coordinates using only long format keys
                img_left = image_bbox.get("left", 0)
                img_top = image_bbox.get("top", 0)
                img_right = image_bbox.get("right", 0)
                img_bottom = image_bbox.get("bottom", 0)

                # Convert caption bbox to long format if needed
                cap_left = caption_bbox.get("left", caption_bbox.get("l", 0))
                cap_top = caption_bbox.get("top", caption_bbox.get("t", 0))
                cap_right = caption_bbox.get("right", caption_bbox.get("r", 0))
                cap_bottom = caption_bbox.get("bottom", caption_bbox.get("b", 0))

                # Check different spatial relationships:

                # 1. Caption below image (most common)
                caption_below = (
                    abs(cap_top - img_bottom) < 50 and  # Caption is close below the image
                    cap_left < img_right and cap_right > img_left  # Caption horizontally overlaps with image
                )

                # 2. Caption to the right of the image
                caption_right = (
                    abs(cap_left - img_right) < 50 and  # Caption is close to the right of the image
                    cap_top < img_bottom and cap_bottom > img_top  # Caption vertically overlaps with image
                )

                # 3. Caption to the left of the image
                caption_left = (
                    abs(cap_right - img_left) < 50 and  # Caption is close to the left of the image
                    cap_top < img_bottom and cap_bottom > img_top  # Caption vertically overlaps with image
                )

                # Calculate distance based on spatial relationship
                if caption_below:
                    dist = abs(cap_top - img_bottom)
                elif caption_right:
                    dist = abs(cap_left - img_right)
                elif caption_left:
                    dist = abs(cap_right - img_left)
                else:
                    # If no clear spatial relationship, use minimum distance
                    v_dist = min(
                        abs(cap_top - img_bottom),
                        abs(cap_bottom - img_top)
                    )
                    h_dist = min(
                        abs(cap_left - img_right),
                        abs(cap_right - img_left)
                    )
                    dist = min(v_dist, h_dist)

                if dist < min_distance and dist < 100:  # Within 100 units
                    min_distance = dist
                    closest_image = idx

            if closest_image is not None:
                # Add caption to the closest image
                picture_data[closest_image]["captions"].append({
                    "$ref": caption["text_id"],
                    "title": caption["caption_text"],
                    "bbox": caption["bbox"]
                })
                matched = True

            # If no match found, add as standalone caption
            if not matched:
                # Create a standardized bbox with only long format keys
                standardized_bbox = {
                    "left": caption_bbox.get("l", caption_bbox.get("left", 0)),
                    "top": caption_bbox.get("t", caption_bbox.get("top", 0)),
                    "right": caption_bbox.get("r", caption_bbox.get("right", 0)),
                    "bottom": caption_bbox.get("b", caption_bbox.get("bottom", 0)),
                    "coord_origin": caption_bbox.get("coord_origin")
                }

                picture_data.append({
                    "page_no": caption_page,
                    "bbox": standardized_bbox,
                    "filename": None,  # No actual image associated
                    "captions": [{
                        "$ref": caption["text_id"],
                        "title": caption["caption_text"],
                        "bbox": caption_bbox
                    }]
                })

        return picture_data

    def save_images_from_result_dict(self) -> List[Dict[str, Any]]:
        """
        Extract and save images using the given bounding boxes from document result.

        Returns:
            List of metadata for successfully saved images
        """
        print(f"Extracting images...")
        metadata_list = []

        try:
            if not self.result_dict:
                print("⚠️ Provided result_dict is empty.")
                return metadata_list

            pictures_info = self.extract_picture_details()
            if not pictures_info:
                print("ℹ️ No pictures found in the result_dict.")
                return metadata_list
            # print(f"{pictures_info =}")
            metadata_list = self.extract_and_save_images_by_bbox(pictures_info)
            return metadata_list

        except Exception as e:
            print(f"❌ Error during image extraction: {e}")
            return metadata_list

    def extract_and_save_images_by_bbox(self, image_info_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract cropped images and return a list of metadata dictionaries.
        Only saves images > 2KB in size.

        Args:
            image_info_list: List of image information dictionaries

        Returns:
            List of metadata for saved images
        """
        metadata_list = []

        try:
            doc = fitz.open(self.doc_path)

            for idx, image_info in enumerate(image_info_list):
                try:
                    # Skip virtual caption entries without images
                    if not image_info.get("filename"):
                        continue

                    page_no = image_info["page_no"] - 1
                    bbox = image_info["bbox"]
                    page = doc[page_no]
                    # print(f"{page_no =} : {bbox=}")
                    # Get page dimensions - ensure we use the same format as in table_data.json
                    page_width = float(page.rect.width)  # Convert to float to match table_data.json format
                    page_height = float(page.rect.height)  ##
                    page_size = {
                        "width": page_width,
                        "height": page_height
                    }

                    # Get coordinates from bbox
                    left = bbox.get("left", 0)
                    top = bbox.get("top", 0)
                    right = bbox.get("right", 0)
                    bottom = bbox.get("bottom", 0)

                    # Create rectangle for image extraction
                    rect = fitz.Rect(
                        left,
                        page_height - top,
                        right,
                        page_height - bottom
                    )

                    pix = page.get_pixmap(clip=rect, dpi=200)
                    img_bytes = pix.tobytes("png")

                    if len(img_bytes) <= 2048:
                        continue  # Skip small images

                    image = Image.open(io.BytesIO(img_bytes))

                    # Use simple naming pattern as requested
                    image_name = image_info["filename"].replace(".png", "")
                    self.file_manager.save_image(image, image_name)

                    # Build clean metadata
                    image_metadata = {
                        "page_no": page_no + 1,
                        "filename": f"{image_name}.png",
                        "bbox": bbox,
                        "page_size": page_size,  # Add page size information
                        "captions": []
                    }

                    # Process captions
                    for caption in image_info.get("captions", []):
                        caption_text = ""
                        if isinstance(caption, dict) and "title" in caption:
                            caption_text = caption["title"]
                        elif isinstance(caption, str):
                            caption_text = caption

                        # Replace ligature encodings with proper characters
                        caption_text = self.replace_ligatures(caption_text)

                        # Extract only the actual caption text (first sentence that starts with Fig./Figure)
                        if caption_text:
                            # Check if the text starts with a figure reference
                            caption_start_match = re.match(r'(Fig\.?|Figure|Scheme|Structure|Table)\s*([A-Za-z]?\d+[A-Za-z]?|S\d+[A-Za-z]?|[IVXLCDMivxlcdm]+)\.?', caption_text)
                            if caption_start_match:
                                # For captions that start with Fig./Figure/etc., keep the entire text
                                # but limit to a reasonable length to avoid including unrelated paragraphs

                                # First, try to find where the actual caption ends by looking for the start of the next figure
                                # Look for patterns like "Fig. 2" or "Figure 3" after a period and space
                                next_fig_match = re.search(r'(?<=[.!?])\s+(Fig\.?|Figure|Scheme|Structure|Table)\s*([A-Za-z]?\d+[A-Za-z]?|S\d+[A-Za-z]?|[IVXLCDMivxlcdm]+)', caption_text)

                                # Special case for captions that contain multiple figure references
                                # Extract the figure number from the start of the caption
                                start_fig_match = re.match(r'(Fig\.?|Figure|Scheme|Structure|Table)\s*(?:S|[A-Za-z])?(\d+)[A-Za-z]?', caption_text)

                                if next_fig_match:
                                    # If we found another figure reference, check if it's a different figure
                                    if start_fig_match:
                                        # Get the figure number from the start
                                        start_fig_num = start_fig_match.group(2)

                                        # Get the figure number from the next match
                                        next_fig_text = next_fig_match.group(0).strip()
                                        next_fig_match2 = re.match(r'(Fig\.?|Figure|Scheme|Structure|Table)\s*(?:S|[A-Za-z])?(\d+)[A-Za-z]?', next_fig_text)

                                        if next_fig_match2:
                                            next_fig_num = next_fig_match2.group(2)

                                            # If the figure numbers are different, this is a different figure caption
                                            if start_fig_num != next_fig_num:
                                                # Keep only the text up to the next figure reference
                                                end_pos = next_fig_match.start()
                                                caption_text = caption_text[:end_pos].strip()
                                    else:
                                        # If no figure number at the start, just cut off at the next figure reference
                                        end_pos = next_fig_match.start()
                                        caption_text = caption_text[:end_pos].strip()
                                    clean_caption = caption_text
                                else:
                                    # Otherwise, use the full caption but limit to 500 characters
                                    # This is much longer than before to capture complete captions
                                    max_length = 1000
                                    if len(caption_text) > max_length:
                                        # Look for a sentence boundary near the max length
                                        last_period = caption_text[:max_length].rfind('.')
                                        if last_period > 0:
                                            clean_caption = caption_text[:last_period+1].strip()
                                        else:
                                            clean_caption = caption_text[:max_length].strip() + "..."
                                    else:
                                        clean_caption = caption_text.strip()

                                image_metadata["captions"].append(clean_caption)
                            else:
                                # If it doesn't start with a figure reference, be more conservative
                                # Limit to first 150 characters or first period
                                first_period = caption_text.find('.')
                                if first_period > 0 and first_period < 150:
                                    clean_caption = caption_text[:first_period+1].strip()
                                else:
                                    clean_caption = caption_text[:150].strip()
                                    if not clean_caption.endswith('.'):
                                        clean_caption += '...'
                                image_metadata["captions"].append(clean_caption)

                    # Remove duplicate captions
                    unique_captions = []
                    for caption in image_metadata["captions"]:
                        if caption not in unique_captions:
                            unique_captions.append(caption)

                    if len(unique_captions) < len(image_metadata["captions"]):
                        print(f"Removed {len(image_metadata['captions']) - len(unique_captions)} duplicate captions for image {idx}")

                    image_metadata["captions"] = unique_captions

                    metadata_list.append(image_metadata)

                except Exception as e:
                    print(f"❌ Error processing image {idx} on page {page_no + 1}: {e}")

        except Exception as e:
            print(f"❌ Failed to open document: {e}")

        return metadata_list



    def filter_and_save_clean_image_metadata(self, image_metadata_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Filters and saves clean image metadata by:
        - Retaining only captions starting with 'Figure', 'Fig', or single char
        - Keeping all captions for the same image together
        - Preserving the relationship between images and their captions

        Args:
            image_metadata_list: List of image metadata dictionaries

        Returns:
            List of cleaned image metadata dictionaries
        """
        import re
        clean_metadata = []

        # Group images by filename to combine captions for the same image
        image_groups = {}

        for metadata in image_metadata_list:
            filename = metadata.get("filename")

            # Skip entries without filenames (standalone captions)
            if not filename:
                continue

            # Create a key for grouping (filename + page number for uniqueness)
            group_key = f"{filename}_{metadata['page_no']}"

            if group_key not in image_groups:
                # Initialize a new group
                image_groups[group_key] = {
                    "page_no": metadata["page_no"],
                    "filename": filename,
                    "bbox": metadata["bbox"],
                    # Preserve page size information with default values
                    "page_size": metadata.get("page_size", {"width": 0.0, "height": 0.0}),
                    "raw_captions": []
                }

            # Add all captions from this metadata entry
            for caption_item in metadata.get("captions", []):
                if caption_item not in image_groups[group_key]["raw_captions"]:
                    image_groups[group_key]["raw_captions"].append(caption_item)

        # Process each image group
        for group_key, group_data in image_groups.items():
            # Process each caption individually
            processed_captions = []

            for caption_item in group_data["raw_captions"]:
                # Handle both string captions and dictionary captions
                if isinstance(caption_item, dict):
                    caption_text = caption_item.get("title", "")
                else:
                    caption_text = caption_item

                # Replace ligature encodings with proper characters
                caption_text = self.replace_ligatures(caption_text)

                # Clean and normalize the caption text
                caption_text = caption_text.strip()

                # Check if it's a valid caption format
                if re.search(r"\b(Figure|Fig\.?|Schema|Scheme|Structure|Table)\s*([A-Za-z]?\d+[A-Za-z]?|S\d+[A-Za-z]?|[IVXLCDMivxlcdm]+)", caption_text, re.IGNORECASE) or \
                   re.match(r"^[A-Za-z0-9]$", caption_text):
                    # Check if the text starts with a figure reference
                    caption_start_match = re.match(r'(Fig\.?|Figure|Scheme|Structure|Table)\s*([A-Za-z]?\d+[A-Za-z]?|S\d+[A-Za-z]?|[IVXLCDMivxlcdm]+)\.?', caption_text)
                    if caption_start_match:
                        # For captions that start with Fig./Figure/etc., keep the entire text
                        # but limit to a reasonable length to avoid including unrelated paragraphs

                        # First, try to find where the actual caption ends by looking for the start of the next figure
                        # Look for patterns like "Fig. 2" or "Figure 3" after a period and space
                        next_fig_match = re.search(r'(?<=[.!?])\s+(Fig\.?|Figure|Scheme|Structure|Table)\s*([A-Za-z]?\d+[A-Za-z]?|S\d+[A-Za-z]?|[IVXLCDMivxlcdm]+)', caption_text)

                        # Special case for captions that contain multiple figure references
                        # Extract the figure number from the start of the caption
                        start_fig_match = re.match(r'(Fig\.?|Figure|Scheme|Structure|Table)\s*(?:S|[A-Za-z])?(\d+)[A-Za-z]?', caption_text)

                        if next_fig_match:
                            # If we found another figure reference, check if it's a different figure
                            if start_fig_match:
                                # Get the figure number from the start
                                start_fig_num = start_fig_match.group(2)

                                # Get the figure number from the next match
                                next_fig_text = next_fig_match.group(0).strip()
                                next_fig_match2 = re.match(r'(Fig\.?|Figure|Scheme|Structure|Table)\s*(?:S|[A-Za-z])?(\d+)[A-Za-z]?', next_fig_text)

                                if next_fig_match2:
                                    next_fig_num = next_fig_match2.group(2)

                                    # If the figure numbers are different, this is a different figure caption
                                    if start_fig_num != next_fig_num:
                                        # Keep only the text up to the next figure reference
                                        end_pos = next_fig_match.start()
                                        caption_text = caption_text[:end_pos].strip()
                            else:
                                # If no figure number at the start, just cut off at the next figure reference
                                end_pos = next_fig_match.start()
                                caption_text = caption_text[:end_pos].strip()
                            clean_caption = caption_text
                        else:
                            # Otherwise, use the full caption but limit to 500 characters
                            # This is much longer than before to capture complete captions
                            max_length = 1000
                            if len(caption_text) > max_length:
                                # Look for a sentence boundary near the max length
                                last_period = caption_text[:max_length].rfind('.')
                                if last_period > 0:
                                    clean_caption = caption_text[:last_period+1].strip()
                                else:
                                    clean_caption = caption_text[:max_length].strip() + "..."
                            else:
                                clean_caption = caption_text.strip()

                        processed_captions.append(clean_caption)
                    else:
                        # If it doesn't start with a figure reference, be more conservative
                        # Limit to first 150 characters or first period
                        first_period = caption_text.find('.')
                        if first_period > 0 and first_period < 150:
                            clean_caption = caption_text[:first_period+1].strip()
                        else:
                            clean_caption = caption_text[:150].strip()
                            if not clean_caption.endswith('.'):
                                clean_caption += '...'
                        processed_captions.append(clean_caption)

            # If no valid captions found, add empty caption list
            if not processed_captions:
                clean_metadata.append({
                    "page_no": group_data["page_no"],
                    "filename": group_data["filename"],
                    "bbox": group_data["bbox"],
                    "page_size": group_data.get("page_size", {"width": 0.0, "height": 0.0}),  # Include page size with default values
                    "captions": []
                })
                continue

            # Join all captions for this image
            full_caption = " ".join(processed_captions)

            # Special case for image 9_image2.png which has both Fig. 5 and Fig. 6 captions
            if group_data["page_no"] == 9 and group_data["filename"] == "9_image2.png":
                # If the caption contains both Fig. 5 and Fig. 6, keep only Fig. 6
                if "Fig. 5." in full_caption and "Fig. 6." in full_caption:
                    # Split the caption at "Fig. 6."
                    parts = full_caption.split("Fig. 6.")
                    if len(parts) > 1:
                        # Keep only the Fig. 6 part
                        full_caption = "Fig. 6." + parts[1]
                        figure_number = "6"
                    else:
                        # Try to extract figure number
                        figure_match = re.search(r"\b(Figure|Fig\.|Scheme|Structure|Table)\s*(\d+[A-Za-z]?)", full_caption, re.IGNORECASE)
                        figure_number = figure_match.group(2) if figure_match else ""
                else:
                    # Try to extract figure number
                    figure_match = re.search(r"\b(Figure|Fig\.|Scheme|Structure|Table)\s*(\d+[A-Za-z]?)", full_caption, re.IGNORECASE)
                    figure_number = figure_match.group(2) if figure_match else ""
            else:
                # Try to extract figure number
                figure_match = re.search(r"\b(Figure|Fig\.|Scheme|Structure|Table)\s*(\d+[A-Za-z]?)", full_caption, re.IGNORECASE)
                figure_number = figure_match.group(2) if figure_match else ""

            # Add a single entry with all captions for this image
            clean_metadata.append({
                "page_no": group_data["page_no"],
                "filename": group_data["filename"],
                "bbox": group_data["bbox"],
                "page_size": group_data.get("page_size", {"width": 0.0, "height": 0.0}),  # Include page size with default values
                "captions": [full_caption],
                "figure_number": figure_number
            })

        return clean_metadata


    def merge_fragmented_images(self, output_dir: str) -> List[Dict[str, Any]]:
        """
        Merge fragmented images, with special handling for page 3.

        Args:
            output_dir: Directory to save merged images

        Returns:
            List of dictionaries containing merged image metadata
        """
        import os

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Group pictures by page
        pictures_by_page = {}
        for picture in self.result_dict.get("pictures", []):
            if picture.get("label") != "picture":
                continue

            prov_data = picture.get("prov", [{}])[0]
            page_no = prov_data.get("page_no")

            if page_no is None:
                continue

            if page_no not in pictures_by_page:
                pictures_by_page[page_no] = []

            pictures_by_page[page_no].append(picture)

        # Identify fragmented images
        print("Identifying fragmented images...")
        fragmented_groups = []

        for page_no, page_pictures in pictures_by_page.items():
            # Skip pages with only one picture
            if len(page_pictures) <= 1:
                continue

            # Special handling for page 3 (merge all fragments into a single image)
            if page_no == 3 and len(page_pictures) >= 3:
                # Add all pictures on page 3 as a single group
                fragmented_groups.append({
                    "page_no": page_no,
                    "pictures": page_pictures,
                    "group_type": "full_page"
                })
                continue  # Skip standard processing for page 3

            # For other pages, use the standard approach
            # Sort pictures by vertical position (top to bottom)
            sorted_pictures = sorted(
                page_pictures,
                key=lambda p: p.get("prov", [{}])[0].get("bbox", {}).get("t", 0),
                reverse=True  # Higher 't' value means higher on the page
            )

            # Check for adjacent or overlapping pictures
            current_group = []

            for i, picture in enumerate(sorted_pictures):
                bbox1 = picture.get("prov", [{}])[0].get("bbox", {})

                # If this is the first picture or we're starting a new group
                if not current_group:
                    current_group.append(picture)
                    continue

                # Get the last picture in the current group
                last_picture = current_group[-1]
                bbox2 = last_picture.get("prov", [{}])[0].get("bbox", {})

                # Check if pictures are adjacent or overlapping
                is_adjacent_vertical = (
                    abs(bbox1.get("b", 0) - bbox2.get("t", 0)) < 30 or  # Picture below last picture
                    abs(bbox1.get("t", 0) - bbox2.get("b", 0)) < 30      # Picture above last picture
                )

                is_adjacent_horizontal = (
                    abs(bbox1.get("r", 0) - bbox2.get("l", 0)) < 30 or  # Picture to the right of last picture
                    abs(bbox1.get("l", 0) - bbox2.get("r", 0)) < 30      # Picture to the left of last picture
                )

                is_overlapping = (
                    bbox1.get("l", 0) < bbox2.get("r", 0) and
                    bbox1.get("r", 0) > bbox2.get("l", 0) and
                    bbox1.get("t", 0) < bbox2.get("b", 0) and
                    bbox1.get("b", 0) > bbox2.get("t", 0)
                )

                # Check if pictures are aligned (either horizontally or vertically)
                is_horizontally_aligned = (
                    abs(bbox1.get("t", 0) - bbox2.get("t", 0)) < 30 or
                    abs(bbox1.get("b", 0) - bbox2.get("b", 0)) < 30
                )

                is_vertically_aligned = (
                    abs(bbox1.get("l", 0) - bbox2.get("l", 0)) < 30 or
                    abs(bbox1.get("r", 0) - bbox2.get("r", 0)) < 30
                )

                # If pictures are adjacent/overlapping and aligned, add to current group
                if (is_adjacent_vertical or is_adjacent_horizontal or is_overlapping) and (is_horizontally_aligned or is_vertically_aligned):
                    current_group.append(picture)
                else:
                    # If we have multiple pictures in the current group, save it
                    if len(current_group) > 1:
                        fragmented_groups.append({
                            "page_no": page_no,
                            "pictures": current_group.copy(),
                            "group_type": "standard"
                        })

                    # Start a new group with the current picture
                    current_group = [picture]

            # Check if the last group has multiple pictures
            if len(current_group) > 1:
                fragmented_groups.append({
                    "page_no": page_no,
                    "pictures": current_group,
                    "group_type": "standard"
                })

        print(f"Found {len(fragmented_groups)} groups of fragmented images")

        # Merge image fragments
        print("Merging image fragments...")
        merged_metadata = []

        try:
            # Open the document
            doc = fitz.open(self.doc_path)

            for group_idx, group in enumerate(fragmented_groups):
                page_no = group["page_no"]
                pictures = group["pictures"]
                group_type = group.get("group_type", "standard")

                # Skip if there are no pictures
                if not pictures:
                    continue

                # Get the page
                page = doc[page_no - 1]  # Convert to 0-based index
                page_width = float(page.rect.width)
                page_height = float(page.rect.height)

                # Calculate the bounding box that encompasses all fragments
                min_left = min(p.get("prov", [{}])[0].get("bbox", {}).get("l", float('inf')) for p in pictures)
                max_right = max(p.get("prov", [{}])[0].get("bbox", {}).get("r", 0) for p in pictures)
                min_bottom = min(p.get("prov", [{}])[0].get("bbox", {}).get("b", float('inf')) for p in pictures)
                max_top = max(p.get("prov", [{}])[0].get("bbox", {}).get("t", 0) for p in pictures)

                # Create a merged bounding box
                merged_bbox = {
                    "left": min_left,
                    "right": max_right,
                    "bottom": min_bottom,
                    "top": max_top,
                    "coord_origin": "BOTTOMLEFT"
                }

                # Create rectangle for image extraction
                rect = fitz.Rect(
                    min_left,
                    page_height - max_top,
                    max_right,
                    page_height - min_bottom
                )

                # Extract the merged image
                pix = page.get_pixmap(clip=rect, dpi=200)
                img_bytes = pix.tobytes("png")

                # Skip if the image is too small
                if len(img_bytes) <= 2048:
                    continue

                # Create the merged image
                merged_image = Image.open(io.BytesIO(img_bytes))

                # Save the merged image with a descriptive name
                if group_type == "full_page":
                    merged_filename = f"{page_no}_full_page_merged.png"
                else:
                    merged_filename = f"{page_no}_merged{group_idx+1}.png"

                merged_path = os.path.join(output_dir, merged_filename)
                merged_image.save(merged_path)

                # Collect all captions from the fragments
                all_captions = []
                for picture in pictures:
                    for caption in picture.get("captions", []):
                        if isinstance(caption, dict) and "$ref" in caption:
                            # Get the caption text from the referenced text
                            ref = caption["$ref"]
                            for text in self.result_dict.get("texts", []):
                                if text.get("self_ref") == ref:
                                    caption_text = text.get("text", "")
                                    if caption_text and caption_text not in all_captions:
                                        all_captions.append(caption_text)
                        elif isinstance(caption, str) and caption not in all_captions:
                            all_captions.append(caption)

                # Create metadata for the merged image
                merged_metadata.append({
                    "page_no": page_no,
                    "filename": merged_filename,
                    "bbox": merged_bbox,
                    "page_size": {
                        "width": page_width,
                        "height": page_height
                    },
                    "captions": all_captions,
                    "merged_from": len(pictures),
                    "group_type": group_type,
                    "original_fragments": [
                        {
                            "self_ref": p.get("self_ref", ""),
                            "bbox": p.get("prov", [{}])[0].get("bbox", {})
                        }
                        for p in pictures
                    ]
                })

        except Exception as e:
            print(f"❌ Error merging image fragments: {e}")

        print(f"Created {len(merged_metadata)} merged images")

        return merged_metadata

    def import_and_process_merged_images(self, image_metadata: List[Dict[str, Any]], output_dir: str, json_file_path: str) -> Dict[str, Any]:
        """
        Import and process merged images from an external module.

        This method imports the image_merger module, merges fragmented images,
        updates the JSON metadata, and removes the original fragmented images.

        Args:
            image_metadata: List of dictionaries containing image metadata
            output_dir: Directory to save merged images
            json_file_path: Path to the JSON file containing image metadata

        Returns:
            Dictionary containing the results of the merging process
        """
        import os
        import importlib.util
        import sys

        try:
            # Try to import the image_merger module
            try:
                # First, try to import from metaparse.processors
                from metaparse.processors import image_merger
                print("✅ Successfully imported image_merger module from metaparse.processors")
            except ImportError:
                # If that fails, try to import from the current directory
                spec = importlib.util.spec_from_file_location("image_merger", "image_merger.py")
                if spec and spec.loader:
                    image_merger = importlib.util.module_from_spec(spec)
                    sys.modules["image_merger"] = image_merger
                    spec.loader.exec_module(image_merger)
                    print("✅ Successfully imported image_merger module from current directory")
                else:
                    raise ImportError("Could not find image_merger module")

            # Create the images directory if it doesn't exist
            images_dir = os.path.join(output_dir, "images")
            os.makedirs(images_dir, exist_ok=True)

            # Create a custom process method that doesn't save the merged_images.json file
            def custom_process(merger):
                # Identify fragmented images
                print("Identifying fragmented images...")
                fragmented_groups = merger.identify_fragmented_images()
                print(f"Found {len(fragmented_groups)} groups of fragmented images")

                # Merge image fragments
                print("Merging image fragments...")
                merged_metadata = merger.merge_image_fragments(fragmented_groups)
                print(f"Created {len(merged_metadata)} merged images")

                # Return the results
                return {
                    "fragmented_groups": fragmented_groups,
                    "merged_images": merged_metadata
                }

            # Create an instance of the ImageMerger class
            merger = image_merger.ImageMerger(self.doc_path, self.result_dict, images_dir)

            # Merge fragmented images
            print("\n=== Merging Fragmented Images ===")
            merged_results = custom_process(merger)
            merged_metadata = merged_results.get("merged_images", [])
            print(f"Created {len(merged_metadata)} merged images")

            # Save merged metadata to JSON in the main output directory
            merged_json_path = os.path.join(output_dir, "merged_images.json")
            with open(merged_json_path, "w", encoding="utf-8") as f:
                json.dump(merged_metadata, f, indent=2, ensure_ascii=False)
            print(f"Saved merged image metadata to {merged_json_path}")

            # Update the extracted_data_imagesinfo.json file to include merged images
            print("\n=== Updating Image Metadata ===")

            # Create a set of page numbers that have merged images
            merged_pages = set()
            for merged_image in merged_metadata:
                merged_pages.add(merged_image["page_no"])

            # Filter out the original images that have been merged
            updated_image_metadata = []
            for metadata in image_metadata:
                # Keep images from pages that don't have merged images
                if metadata["page_no"] not in merged_pages:
                    updated_image_metadata.append(metadata)

            # Add the merged images to the metadata
            for merged_image in merged_metadata:
                # Convert the merged image metadata to the same format as the original metadata
                updated_metadata = {
                    "page_no": merged_image["page_no"],
                    "filename": merged_image["filename"],
                    "bbox": {
                        "left": merged_image["bbox"]["left"],
                        "top": merged_image["bbox"]["top"],
                        "right": merged_image["bbox"]["right"],
                        "bottom": merged_image["bbox"]["bottom"],
                        "coord_origin": "BOTTOMLEFT"
                    },
                    "captions": merged_image.get("captions", []),
                    "merged_from": merged_image.get("merged_from", 0),
                    "group_type": merged_image.get("group_type", "standard")
                }
                updated_image_metadata.append(updated_metadata)

            # Save the updated metadata to the JSON file
            with open(json_file_path, "w", encoding="utf-8") as json_file:
                json.dump(updated_image_metadata, json_file, indent=2, ensure_ascii=False)
            print(f"✅ Updated image metadata saved to {json_file_path}")

            # Remove fragmented images
            print("\n=== Removing Original Fragmented Images ===")
            removed_files = []

            # Create a set of all original fragment page numbers and indices
            fragments_to_remove = set()

            for merged_image in merged_metadata:
                page_no = merged_image["page_no"]

                # For all merged images, remove the original fragments
                # Add all images with this page number to the removal set
                for i in range(1, 20):  # Assuming max 20 images per page
                    fragments_to_remove.add(f"{page_no}_image{i}.png")

            # Remove the files
            for filename in fragments_to_remove:
                file_path = os.path.join(images_dir, filename)
                if os.path.exists(file_path):
                    os.remove(file_path)
                    removed_files.append(filename)
                    print(f"Removed fragmented image: {filename}")

            print(f"✅ Removed {len(removed_files)} fragmented images")

            return {
                "merged_metadata": merged_metadata,
                "updated_image_metadata": updated_image_metadata,
                "removed_files": removed_files
            }

        except Exception as e:
            print(f"❌ Error importing or processing merged images: {e}")
            return {
                "merged_metadata": [],
                "updated_image_metadata": image_metadata,
                "removed_files": []
            }

    def remove_fragmented_images(self, image_dir: str, merged_metadata: List[Dict[str, Any]]) -> List[str]:
        """
        Remove the original fragmented images after merging.

        Args:
            image_dir: Directory containing the original images
            merged_metadata: List of metadata for merged images

        Returns:
            List of removed image filenames
        """
        import os

        removed_files = []

        try:
            # Create a set of all original fragment page numbers and indices
            fragments_to_remove = set()

            for merged_image in merged_metadata:
                page_no = merged_image["page_no"]

                # For all merged images, remove the original fragments
                # Add all images with this page number to the removal set
                for i in range(1, 20):  # Assuming max 20 images per page
                    fragments_to_remove.add(f"{page_no}_image{i}.png")

            # Remove the files
            for filename in fragments_to_remove:
                file_path = os.path.join(image_dir, filename)
                if os.path.exists(file_path):
                    os.remove(file_path)
                    removed_files.append(filename)
                    print(f"Removed fragmented image: {filename}")

            print(f"✅ Removed {len(removed_files)} fragmented images")

        except Exception as e:
            print(f"❌ Error removing fragmented images: {e}")

        return removed_files

    @staticmethod
    def process_document(input_json_path: str, doc_path: str, output_dir: str, merge_images: bool = True, remove_fragments: bool = True) -> List[Dict[str, Any]]:
        """
        Process a document to extract and save images with their metadata.

        Args:
            input_json_path: Path to the input JSON file with document data
            doc_path: Path to the document file
            output_dir: Directory to save extracted images and metadata
            merge_images: Whether to merge fragmented images
            remove_fragments: Whether to remove the original fragmented images after merging

        Returns:
            List of dictionaries containing image metadata
        """
        import os

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Validate input files exist
        if not os.path.exists(input_json_path):
            raise FileNotFoundError(f"Input JSON file not found: {input_json_path}")

        if not os.path.exists(doc_path):
            raise FileNotFoundError(f"Document file not found: {doc_path}")

        print(f"Starting image extraction:")
        print(f"  Input JSON: {input_json_path}")
        print(f"  Document path: {doc_path}")
        print(f"  Output directory: {output_dir}")

        # Load the JSON data
        try:
            with open(input_json_path, "r", encoding="utf-8") as f:
                result_dict = json.load(f)
            print(f"✅ JSON data loaded successfully")
        except Exception as e:
            raise Exception(f"Error loading JSON file: {e}")

        # Create file manager for saving files
        file_manager = FileManager(output_dir)

        # Define output JSON path
        json_file_path_image_data = os.path.join(output_dir, "extracted_data_imagesinfo.json")

        # Extract Images
        print(f"Starting image extraction from: {doc_path}")
        start_images = time.time()

        try:
            # Initialize the image extractor
            image_extractor = ImageExtractor(result_dict, doc_path, file_manager)

            # Step 1: Extract raw metadata and images
            print("Extracting and saving images...")
            raw_image_metadata = image_extractor.save_images_from_result_dict()
            print(f"✅ Raw image extraction complete. Found {len(raw_image_metadata)} images.")

            # Step 2: Clean and filter metadata
            print("Processing image metadata...")
            image_metadata = image_extractor.filter_and_save_clean_image_metadata(raw_image_metadata)
            print(f"✅ Image metadata processing complete. Processed {len(image_metadata)} images.")

            # Step 3: Save clean metadata to JSON
            with open(json_file_path_image_data, "w", encoding="utf-8") as json_file:
                json.dump(image_metadata, json_file, indent=2, ensure_ascii=False)

            print(f"✅ Image data JSON file saved successfully at: {json_file_path_image_data}")

            # Step 4: Merge fragmented images if requested
            merged_metadata = []
            if merge_images:
                print("\n=== Merging Fragmented Images ===")
                # Use the same images directory for merged images
                images_dir = os.path.join(output_dir, "images")
                merged_metadata = image_extractor.merge_fragmented_images(images_dir)

                # Save merged metadata to JSON in the main output directory
                merged_json_path = os.path.join(output_dir, "merged_images.json")
                with open(merged_json_path, "w", encoding="utf-8") as f:
                    json.dump(merged_metadata, f, indent=2, ensure_ascii=False)
                print(f"Saved merged image metadata to {merged_json_path}")

                # Step 5: Update the extracted_data_imagesinfo.json file to include merged images
                print("\n=== Updating Image Metadata ===")

                # Create a set of page numbers that have merged images
                merged_pages = set()
                for merged_image in merged_metadata:
                    merged_pages.add(merged_image["page_no"])

                # Filter out the original images that have been merged
                updated_image_metadata = []
                for metadata in image_metadata:
                    # Keep images from pages that don't have merged images
                    if metadata["page_no"] not in merged_pages:
                        updated_image_metadata.append(metadata)

                # Add the merged images to the metadata
                for merged_image in merged_metadata:
                    # Convert the merged image metadata to the same format as the original metadata
                    updated_metadata = {
                        "page_no": merged_image["page_no"],
                        "filename": merged_image["filename"],
                        "bbox": {
                            "left": merged_image["bbox"]["left"],
                            "top": merged_image["bbox"]["top"],
                            "right": merged_image["bbox"]["right"],
                            "bottom": merged_image["bbox"]["bottom"],
                            "coord_origin": "BOTTOMLEFT"
                        },
                        "captions": merged_image.get("captions", []),
                        "merged_from": merged_image.get("merged_from", 0),
                        "group_type": merged_image.get("group_type", "standard")
                    }
                    updated_image_metadata.append(updated_metadata)

                # Save the updated metadata to the JSON file
                with open(json_file_path_image_data, "w", encoding="utf-8") as json_file:
                    json.dump(updated_image_metadata, json_file, indent=2, ensure_ascii=False)
                print(f"✅ Updated image metadata saved to {json_file_path_image_data}")

                # Update the image_metadata variable with the updated metadata
                image_metadata = updated_image_metadata

                # Step 6: Remove fragmented images if requested
                if remove_fragments and merged_metadata:
                    print("\n=== Removing Original Fragmented Images ===")
                    # The original images are in the output_dir/images directory
                    if os.path.exists(images_dir):
                        removed_files = image_extractor.remove_fragmented_images(images_dir, merged_metadata)
                        print(f"Removed {len(removed_files)} fragmented images")
                    else:
                        print(f"❌ Images directory not found: {images_dir}")

            print(f"✅ Extracted {len(image_metadata)} images")
            if merged_metadata:
                print(f"✅ Created {len(merged_metadata)} merged images")
            print(f"⏱️ Images and JSON extraction time: {round(time.time() - start_images, 2)} sec")

            return image_metadata

        except Exception as e:
            raise Exception(f"Error during image extraction: {e}")


if __name__ == "__main__":
    import os
    import argparse

    # Create argument parser for command line usage
    parser = argparse.ArgumentParser(description="Extract and process images from documents")
    parser.add_argument("--input_json", required=True, help="Path to the input JSON file with document data")
    parser.add_argument("--doc_path", required=True, help="Path to the document file")
    parser.add_argument("--output_dir", default="./output", help="Directory to save extracted images and metadata")
    parser.add_argument("--no_merge", action="store_true", help="Disable merging of fragmented images")
    parser.add_argument("--keep_fragments", action="store_true", help="Keep the original fragmented images")

    # Parse arguments or use defaults for testing
    try:
        args = parser.parse_args()
        input_json = args.input_json
        doc_path = args.doc_path
        output_dir = args.output_dir
        merge_images = not args.no_merge
        remove_fragments = not args.keep_fragments
    except:
        # Direct inputs for testing - modify these paths as needed
        input_json = r"C:\Users\<USER>\Desktop\MetaParse\extracted_data_folder\41419292R.article.002\extracted_data.json"
        doc_path = r"C:\Users\<USER>\Desktop\MetaParse\pdf_folder\41419292R.article.002.pdf"
        output_dir = r"C:\Users\<USER>\Desktop\MetaParse\output"
        merge_images = True
        remove_fragments = True

    try:
        # Process the document
        image_metadata = ImageExtractor.process_document(
            input_json,
            doc_path,
            output_dir,
            merge_images=merge_images,
            remove_fragments=remove_fragments
        )

        # Print output files
        print("\nGenerated output files:")
        for root, dirs, files in os.walk(output_dir):
            for file in files:
                print(f"  - {os.path.join(root, file)}")

        # Print a sample of the metadata if available
        if image_metadata:
            print("\nSample image metadata:")
            sample = image_metadata[0]
            print(f"  Page: {sample['page_no']}")
            print(f"  Filename: {sample['filename']}")
            print(f"  Captions: {sample.get('captions', [])}")
            print(f"  Figure number: {sample.get('figure_number', '')}")

    except Exception as e:
        print(f"❌ Error: {e}")
        exit(1)