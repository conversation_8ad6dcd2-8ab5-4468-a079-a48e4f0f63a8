#!/usr/bin/env python
"""
DOCX to PDF Converter Module

This module provides a class-based approach to convert DOCX files to PDF format.
It supports multiple conversion methods including LibreOffice and Microsoft Word.
Author : Anand Jadhav
Date : 2025-05-20
"""

import os
import sys
import time
import platform
import subprocess
import tempfile
import logging
from typing import Optional, Tuple, List

# Set up logging
logger = logging.getLogger(__name__)


class DocxToPdfConverter:
    """
    Class for converting DOCX files to PDF format.

    This class provides methods to convert DOCX files to PDF using various
    conversion methods such as LibreOffice and Microsoft Word.
    """

    def _setup_conversion_methods(self):
        """
        Set up the conversion methods based on preferences and available tools.
        """
        self.methods = []

        # Add methods based on preference
        if self.prefer_method == 'word' and self.has_comtypes:
            self.methods.append(("Microsoft Word", self.convert_with_word))
        elif self.prefer_method == 'libreoffice' and self.libreoffice_path:
            self.methods.append(("LibreOffice", self.convert_with_libreoffice))
        elif self.prefer_method == 'auto':
            # In auto mode, try Word first (faster) then LibreOffice
            if self.has_comtypes:
                self.methods.append(("Microsoft Word", self.convert_with_word))
            if self.libreoffice_path:
                self.methods.append(("LibreOffice", self.convert_with_libreoffice))

        # If no methods are available, log a warning
        if not self.methods:
            logger.warning("No conversion methods available. Install LibreOffice or Microsoft Word with comtypes.")

    def __init__(self, use_temp_dir: bool = True, cleanup_temp: bool = True,
                 optimize_for_speed: bool = True, prefer_method: str = 'word'):
        """
        Initialize the DOCX to PDF converter.

        Args:
            use_temp_dir: Whether to use a temporary directory for output if none is specified
            cleanup_temp: Whether to clean up temporary files after conversion
            optimize_for_speed: Whether to optimize for speed over quality
            prefer_method: Preferred conversion method ('libreoffice', 'word', or 'auto')
        """
        self.use_temp_dir = use_temp_dir
        self.cleanup_temp = cleanup_temp
        self.optimize_for_speed = optimize_for_speed
        self.prefer_method = prefer_method.lower() if prefer_method else 'auto'
        self.temp_dirs = []

        # Only initialize the methods we'll actually use based on preference
        self.libreoffice_path = self._find_libreoffice_path() if self.prefer_method in ['libreoffice', 'auto'] else None
        self.has_comtypes = self._check_comtypes() if self.prefer_method in ['word', 'auto'] else False

        # Set up conversion methods based on preference
        self.methods = []
        if self.prefer_method == 'word' and self.has_comtypes:
            self.methods.append(("Microsoft Word", self.convert_with_word))
        elif self.prefer_method == 'libreoffice' and self.libreoffice_path:
            self.methods.append(("LibreOffice", self.convert_with_libreoffice))
        elif self.prefer_method == 'auto':
            # In auto mode, try Word first (faster) then LibreOffice
            if self.has_comtypes:
                self.methods.append(("Microsoft Word", self.convert_with_word))
            if self.libreoffice_path:
                self.methods.append(("LibreOffice", self.convert_with_libreoffice))

        # If no methods are available, log a warning
        if not self.methods:
            logger.warning("No conversion methods available. Install LibreOffice or Microsoft Word with comtypes.")

    def _find_libreoffice_path(self) -> Optional[str]:
        """
        Find the LibreOffice executable path based on the operating system.

        Returns:
            Path to the LibreOffice executable, or None if not found
        """
        system = platform.system()

        if system == "Windows":
            # Check common installation paths on Windows
            possible_paths = [
                r"C:\Program Files\LibreOffice\program\soffice.exe",
                r"C:\Program Files (x86)\LibreOffice\program\soffice.exe",
                r"C:\Program Files\LibreOffice 7\program\soffice.exe"
            ]
            for path in possible_paths:
                if os.path.exists(path):
                    return path

            # Try to find it in the PATH
            try:
                result = subprocess.run(["where", "soffice"], capture_output=True, text=True, check=True)
                if result.stdout.strip():
                    return result.stdout.strip().split("\n")[0]
            except (subprocess.SubprocessError, FileNotFoundError):
                pass

        elif system == "Darwin":  # macOS
            # Check common installation paths on macOS
            possible_paths = [
                "/Applications/LibreOffice.app/Contents/MacOS/soffice",
                "/Applications/LibreOffice.app/Contents/MacOS/soffice.bin"
            ]
            for path in possible_paths:
                if os.path.exists(path):
                    return path

        else:  # Linux and other Unix-like systems
            # Try to find it in the PATH
            try:
                result = subprocess.run(["which", "soffice"], capture_output=True, text=True, check=True)
                if result.stdout.strip():
                    return result.stdout.strip()
            except (subprocess.SubprocessError, FileNotFoundError):
                pass

        return None

    def _check_comtypes(self) -> bool:
        """
        Check if the comtypes package is available for Microsoft Word conversion.

        Returns:
            True if comtypes is available, False otherwise
        """
        if platform.system() != "Windows":
            return False

        try:
            import comtypes.client
            return True
        except ImportError:
            return False

    def _get_output_dir(self, output_dir: Optional[str] = None) -> str:
        """
        Get the output directory, creating a temporary one if needed.

        Args:
            output_dir: Specified output directory, or None to use a temporary directory

        Returns:
            Path to the output directory
        """
        if output_dir is not None:
            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)
            return output_dir

        if self.use_temp_dir:
            # Create a temporary directory
            temp_dir = tempfile.mkdtemp(prefix="docx_to_pdf_")
            self.temp_dirs.append(temp_dir)
            return temp_dir

        # Use current directory
        return os.getcwd()

    def convert_with_libreoffice(self, input_path: str, output_dir: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Convert DOCX to PDF using LibreOffice.

        Args:
            input_path: Path to the input DOCX file
            output_dir: Directory to save the PDF file (optional)

        Returns:
            Tuple of (success, pdf_path)
        """
        if not self.libreoffice_path:
            logger.warning("LibreOffice not found. Please install LibreOffice or specify its path.")
            return False, None

        # Get output directory
        output_dir = self._get_output_dir(output_dir)

        # Get the base filename without extension
        base_name = os.path.splitext(os.path.basename(input_path))[0]
        pdf_path = os.path.join(output_dir, f"{base_name}.pdf")

        try:
            # Run LibreOffice in headless mode to convert the file
            cmd = [
                self.libreoffice_path,
                "--headless",
                "--convert-to", "pdf",
                "--outdir", output_dir,
                input_path
            ]

            logger.info(f"Running conversion command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)

            # Check if the PDF was created
            if os.path.exists(pdf_path):
                logger.info(f"Successfully converted to PDF: {pdf_path}")
                return True, pdf_path
            else:
                logger.error(f"Conversion failed. LibreOffice output: {result.stdout}")
                logger.error(f"Error: {result.stderr}")
                return False, None

        except subprocess.SubprocessError as e:
            logger.error(f"Error running LibreOffice: {e}")
            return False, None

    def convert_with_word(self, input_path: str, output_dir: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Convert DOCX to PDF using Microsoft Word via COM interface (Windows only).

        Args:
            input_path: Path to the input DOCX file
            output_dir: Directory to save the PDF file (optional)

        Returns:
            Tuple of (success, pdf_path)
        """
        if not self.has_comtypes:
            logger.warning("COM interface is not available. Install comtypes with: pip install comtypes")
            return False, None

        # Get output directory
        output_dir = self._get_output_dir(output_dir)

        # Import comtypes here to avoid import errors on non-Windows platforms
        import comtypes.client

        # Get absolute paths
        input_path_abs = os.path.abspath(input_path)

        # Get the base filename without extension
        base_name = os.path.splitext(os.path.basename(input_path))[0]
        pdf_path = os.path.join(output_dir, f"{base_name}.pdf")
        pdf_path_abs = os.path.abspath(pdf_path)

        word = None
        try:
            # Create Word application object
            word = comtypes.client.CreateObject("Word.Application")
            word.Visible = False

            # Speed optimizations
            if hasattr(self, 'optimize_for_speed') and self.optimize_for_speed:
                # Disable screen updating and alerts
                word.DisplayAlerts = False
                word.ScreenUpdating = False
                word.EnableEvents = False
                word.Options.SavePropertiesPrompt = False

            # Open the document with read-only and no-window options for speed
            doc = word.Documents.Open(
                input_path_abs,
                ReadOnly=True,
                Visible=False,
                AddToRecentFiles=False
            )

            # Save as PDF with optimized settings
            if hasattr(self, 'optimize_for_speed') and self.optimize_for_speed:
                # Use minimum quality settings for speed
                try:
                    # Try to use ExportAsFixedFormat for better speed control
                    doc.ExportAsFixedFormat(
                        OutputFileName=pdf_path_abs,
                        ExportFormat=17,  # PDF format
                        OptimizeFor=1,  # wdExportOptimizeForSpeed
                        BitmapMissingFonts=True,
                        UseISO19005_1=False  # Not PDF/A compliant (faster)
                    )
                except:
                    # Fall back to SaveAs if ExportAsFixedFormat is not available
                    doc.SaveAs(pdf_path_abs, FileFormat=17)  # 17 = PDF format
            else:
                # Use standard quality settings
                doc.SaveAs(pdf_path_abs, FileFormat=17)  # 17 = PDF format

            # Close the document
            doc.Close()

            if os.path.exists(pdf_path):
                logger.info(f"Successfully converted to PDF: {pdf_path}")
                return True, pdf_path
            else:
                logger.error("Conversion failed. PDF file not created.")
                return False, None

        except Exception as e:
            logger.error(f"Error using Microsoft Word COM interface: {e}")
            return False, None
        finally:
            # Ensure Word is closed
            if word:
                try:
                    word.Quit()
                except:
                    pass



    def convert(self, input_path: str, output_dir: Optional[str] = None) -> Optional[str]:
        """
        Convert DOCX to PDF using available methods.

        Args:
            input_path: Path to the input DOCX file
            output_dir: Directory to save the PDF file (optional)

        Returns:
            Path to the converted PDF file, or None if conversion failed
        """
        start_time = time.time()
        logger.info(f"Converting DOCX to PDF: {input_path}")

        # Validate input file
        if not os.path.exists(input_path):
            logger.error(f"Input file not found: {input_path}")
            return None

        # Check file extension
        if not input_path.lower().endswith('.docx'):
            logger.warning(f"Input file does not have a .docx extension: {input_path}")

        # Try conversion methods in order
        for method_name, method_func in self.methods:
            logger.info(f"Trying conversion with {method_name}...")
            success, pdf_path = method_func(input_path, output_dir)
            if success and pdf_path:
                elapsed = time.time() - start_time
                logger.info(f"Conversion completed in {elapsed:.2f} seconds")
                return pdf_path

        logger.error("All conversion methods failed.")
        return None

    def batch_convert(self, input_paths: List[str], output_dir: Optional[str] = None) -> List[Tuple[str, Optional[str]]]:
        """
        Convert multiple DOCX files to PDF.

        Args:
            input_paths: List of paths to input DOCX files
            output_dir: Directory to save the PDF files (optional)

        Returns:
            List of tuples (input_path, pdf_path) where pdf_path is None if conversion failed
        """
        results = []
        for input_path in input_paths:
            pdf_path = self.convert(input_path, output_dir)
            results.append((input_path, pdf_path))
        return results

    def cleanup(self):
        """Clean up temporary directories created during conversion."""
        if not self.cleanup_temp:
            return

        for temp_dir in self.temp_dirs:
            try:
                # Remove all files in the temporary directory
                for filename in os.listdir(temp_dir):
                    file_path = os.path.join(temp_dir, filename)
                    if os.path.isfile(file_path):
                        os.remove(file_path)

                # Remove the directory
                os.rmdir(temp_dir)
                logger.info(f"Cleaned up temporary directory: {temp_dir}")
            except Exception as e:
                logger.warning(f"Error cleaning up temporary directory {temp_dir}: {e}")

        # Clear the list of temporary directories
        self.temp_dirs = []

    def __enter__(self):
        """Context manager entry point."""
        return self

    def __exit__(self, *_):
        """Context manager exit point."""
        self.cleanup()


def convert_docx_to_pdf(input_path: str, output_dir: Optional[str] = None,
                     optimize_for_speed: bool = True, prefer_method: str = 'word') -> Optional[str]:
    """
    Convenience function to convert a DOCX file to PDF.

    Args:
        input_path: Path to the input DOCX file
        output_dir: Directory to save the PDF file (optional)
        optimize_for_speed: Whether to optimize for speed over quality
        prefer_method: Preferred conversion method ('libreoffice', 'word', or 'auto')

    Returns:
        Path to the converted PDF file, or None if conversion failed
    """
    start_time = time.time()
    converter = DocxToPdfConverter(optimize_for_speed=optimize_for_speed, prefer_method=prefer_method)
    try:
        result = converter.convert(input_path, output_dir)
        if result:
            elapsed = time.time() - start_time
            logger.info(f"Total conversion time: {elapsed:.2f} seconds")
        return result
    finally:
        converter.cleanup()


if __name__ == "__main__":
    start = time.time()
    # Set up logging for command-line usage
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

    # Simple command-line interface
    if len(sys.argv) < 2:
        print("Usage: python docx_to_pdf.py input.docx [output_dir]")
        sys.exit(1)

    input_file = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else None

    # Use the converter as a context manager with speed optimization
    with DocxToPdfConverter(optimize_for_speed=True, prefer_method='word') as converter:
        print("Starting DOCX to PDF conversion...")
        pdf_path = converter.convert(input_file, output_dir)
        if pdf_path:
            conversion_time = time.time() - start
            print(f"Successfully converted to PDF: {pdf_path}")
            print(f"Time required for conversion: {round(conversion_time, 2)} seconds")
            sys.exit(0)
        else:
            print("Conversion failed.")
            sys.exit(1)



#python docx_to_pdf.py "C:\Users\<USER>\Desktop\MetaParse\pdf_folder\sample_document.docx" "C:\Users\<USER>\Desktop\MetaParse\output"