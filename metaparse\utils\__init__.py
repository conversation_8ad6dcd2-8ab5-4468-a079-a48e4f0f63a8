"""
Utility modules for the MetaParse package.

This module contains utility classes and functions used across the package.
"""

# Import utility components for easier access
from metaparse.utils.file_manager import FileManager
from metaparse.utils.docx_to_pdf import DocxToPdfConverter, convert_docx_to_pdf

# Define what's available when importing from this module
__all__ = ['FileManager', 'DocxToPdfConverter', 'convert_docx_to_pdf']
