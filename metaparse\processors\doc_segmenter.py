"""
Document segmentation module for the MetaParse package.

This module provides classes for segmenting document content into sections.
Author : Anand Jadhav
Date : 2025-05-20
"""

import re
from typing import Dict, List, Any


class DocSegmenter:
    """
    Segments document content into logical sections based on headers.

    This class analyzes document structure to identify section headers and
    groups text content under each section.
    """

    def __init__(self, doc_json: Dict[str, Any]):
        """
        Initialize the segmenter with document JSON data.

        Args:
            doc_json: Document data in JSON format from the extractor
        """
        self.doc_json = doc_json

    def get_children_references_by_category(self) -> Dict[str, List[str]]:
        """
        Extract children references from each section.

        Returns:
            Dictionary mapping categories to lists of references
        """
        excluded_refs = {'pictures': []}  # "tables": [], "groups": []

        # Extract children references from each section
        for section in excluded_refs.keys():
            for item in self.doc_json.get(section, []):
                for child in item.get("children", []):
                    ref = child.get("$ref")

                    if ref:
                        excluded_refs[section].append(ref)
        return excluded_refs

    def extract_page_sizes(self) -> Dict[str, Dict[str, Any]]:
        """
        Extract page size information from the document JSON.

        Returns:
            Dictionary containing page size information
        """
        pages = {}

        # First try to get page sizes from the original JSON if available
        if "pages" in self.doc_json:
            return self.doc_json["pages"]

        # If not available, extract page size information from texts
        for text in self.doc_json.get("texts", []):
            if "prov" in text and text["prov"]:
                for prov in text["prov"]:
                    if "page_no" in prov:
                        page_no = prov["page_no"]
                        if str(page_no) not in pages:
                            page_info = {"page_no": page_no, "size": {}}

                            # Try to extract page dimensions
                            if "page_width" in prov and "page_height" in prov:
                                page_info["size"] = {
                                    "width": prov["page_width"],
                                    "height": prov["page_height"]
                                }

                            pages[str(page_no)] = page_info

        return pages

    def extract_text_by_section(self) -> Dict[str, Any]:
        """
        Extract and group text by section headers.

        Returns:
            Dictionary containing text data grouped by sections and page information
        """
        texts = self.doc_json.get("texts", [])

        # Exclude certain references (pictures, tables, etc.)
        excluded_reference_dict = self.get_children_references_by_category()
        excluded_ref_list = [ref for references in excluded_reference_dict.values() for ref in references]

        # Regex pattern to match image-like parent references
        picture_pattern = re.compile(r"#/pictures/\d+")

        # Step 1: Collect potential section headers
        section_headers = []

        for obj in texts:
            label = obj.get("label", "")
            text = obj.get("text", "").replace("\u00a0", " ").strip()
            self_ref = obj.get("self_ref", "")

            # For document-style headers
            if label == "section_header" and text:
                section_headers.append({"self_ref": self_ref, "text": text})

            # For DOCX-style headers: use heuristic (short & bold-looking titles)
            elif label == "paragraph" and text and len(text) < 80:
                # Optional: use more heuristics like font-size, uppercase, etc. if available
                section_headers.append({"self_ref": self_ref, "text": text})

        # Step 2: Group text under each section
        text_data = []
        current_section = None
        current_texts = []

        for text_obj in texts:
            label = text_obj.get("label", "")
            self_ref = text_obj.get("self_ref", "")
            parent_ref = text_obj.get("parent", {}).get("$ref", "")
            text = text_obj.get("text", "").replace("\u00a0", " ").strip()

            if not text:
                continue

            # Is this a section header?
            if any(header["self_ref"] == self_ref for header in section_headers):
                # Save previous section
                if current_section:
                    text_data.append({
                        "self_ref": current_section["self_ref"],
                        "header": current_section["text"],
                        "section_text": current_texts
                    })
                # Start new section
                current_section = next(h for h in section_headers if h["self_ref"] == self_ref)
                current_texts = []
            else:
                # Skip excluded or irrelevant content
                if self_ref in excluded_ref_list or \
                label in ["caption", "list_item", "picture", "footnote", "page_header", "page_footer", "formula"] or \
                picture_pattern.match(parent_ref):
                    continue

                if label in ["text", "paragraph"] and current_section:
                    # Extract bbox information and page number from the prov field if available
                    bbox_info = None
                    page_no = None
                    if "prov" in text_obj and text_obj["prov"]:
                        # Get the first prov entry's bbox and page_no
                        prov_entry = text_obj["prov"][0]
                        if "bbox" in prov_entry:
                            bbox_info = prov_entry["bbox"]
                        if "page_no" in prov_entry:
                            page_no = prov_entry["page_no"]

                    # Create text entry with bbox information and page number
                    text_entry = {
                        "text": text,
                        "bbox": bbox_info,
                        "page_no": page_no
                    }

                    current_texts.append({self_ref: text_entry})

        # Final section
        if current_section:
            text_data.append({
                "self_ref": current_section["self_ref"],
                "header": current_section["text"],
                "section_text": current_texts
            })

        # Extract page size information
        pages = self.extract_page_sizes()

        # Return both text data and page information
        return {
            "text_data": text_data,
            "pages": pages
        }

    def run_segmentation(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Run the segmentation process.

        Returns:
            Dictionary containing segmented text data
        """
        return self.extract_text_by_section()


if __name__ == "__main__":
    import argparse
    import json
    import os

    # Create argument parser
    parser = argparse.ArgumentParser(description="Segment document content into sections")
    parser.add_argument("--input_json", required=True, help="Path to the input JSON file")
    parser.add_argument("--output_json", required=True, help="Path to the output JSON file")

    # Parse arguments
    args = parser.parse_args()

    # Check if input file exists
    if not os.path.exists(args.input_json):
        print(f"Error: Input file {args.input_json} not found")
        exit(1)

    # Load input JSON
    with open(args.input_json, "r", encoding="utf-8") as f:
        doc_json = json.load(f)

    # Create segmenter and run segmentation
    segmenter = DocSegmenter(doc_json)
    segmented_data = segmenter.run_segmentation()

    # Save output JSON
    with open(args.output_json, "w", encoding="utf-8") as f:
        json.dump(segmented_data, f, indent=2, ensure_ascii=False)

    print(f"Segmentation complete. Output saved to {args.output_json}")
