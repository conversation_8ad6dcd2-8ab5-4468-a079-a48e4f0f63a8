#!/usr/bin/env python3
"""
Test raw Docling output to see if it provides linebreaks.

This script bypasses all our custom processing and shows exactly
what <PERSON><PERSON> provides by default.

Author: <PERSON>hav
Date: 2025-01-27
"""

import json
import sys
from pathlib import Path

def test_raw_docling():
    """Test raw Docling output without any custom processing."""
    
    print("🔍 Testing Raw Docling Output")
    print("=" * 40)
    
    try:
        # Import Docling directly
        from docling.document_converter import DocumentConverter
        
        # Your PDF file
        pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
        
        if not Path(pdf_file).exists():
            print(f"❌ PDF file not found: {pdf_file}")
            return False
        
        print(f"📄 Testing with: {Path(pdf_file).name}")
        
        # Create basic Docling converter
        print(f"\n🔄 Creating basic DocumentConverter...")
        converter = DocumentConverter()
        
        # Convert document
        print(f"🔄 Converting document...")
        result = converter.convert(pdf_file)
        
        # Get raw dictionary
        print(f"🔄 Exporting to dictionary...")
        raw_dict = result.document.export_to_dict()
        
        print(f"✅ Conversion completed")
        
        # Analyze raw output
        print(f"\n📊 Raw Docling Analysis:")
        print(f"   Document sections: {list(raw_dict.keys())}")
        
        # Check pages
        if 'pages' in raw_dict and raw_dict['pages']:
            pages_count = len(raw_dict['pages'])
            print(f"   Pages: {pages_count}")
            
            # Analyze first page
            first_page_key = list(raw_dict['pages'].keys())[0]
            first_page = raw_dict['pages'][first_page_key]
            
            print(f"\n📄 First Page Analysis:")
            print(f"   Page fields: {list(first_page.keys())}")
            
            if 'text' in first_page:
                text = first_page['text']
                print(f"   Text length: {len(text):,} characters")
                print(f"   Linebreaks (\\n): {text.count(chr(10))}")
                print(f"   Carriage returns (\\r): {text.count(chr(13))}")
                print(f"   Spaces: {text.count(' ')}")
                print(f"   Words: {len(text.split())}")
                
                # Show sample with visible linebreaks
                sample = text[:300]
                print(f"\n📝 Sample text (first 300 chars):")
                print(f"   Raw: '{sample}...'")
                
                # Show with visible linebreak characters
                visible_sample = sample.replace('\n', '\\n').replace('\r', '\\r')
                print(f"   Visible: '{visible_sample}...'")
                
                if text.count('\n') > 0:
                    print(f"   ✅ Raw Docling DOES provide linebreaks!")
                else:
                    print(f"   ❌ Raw Docling does NOT provide linebreaks")
            else:
                print(f"   ⚠️ No 'text' field in first page")
        
        # Check texts section
        if 'texts' in raw_dict and raw_dict['texts']:
            texts_count = len(raw_dict['texts'])
            print(f"\n📝 Texts Section Analysis:")
            print(f"   Text items: {texts_count}")
            
            # Analyze first few text items
            for i, text_item in enumerate(raw_dict['texts'][:3]):
                print(f"\n   Text item {i+1}:")
                if 'text' in text_item:
                    item_text = text_item['text']
                    print(f"     Content: '{item_text[:50]}...'")
                    print(f"     Length: {len(item_text)} chars")
                    print(f"     Linebreaks: {item_text.count(chr(10))}")
                    
                    # Check provenance for position info
                    if 'prov' in text_item and text_item['prov']:
                        prov = text_item['prov'][0]
                        print(f"     Page: {prov.get('page_no', 'N/A')}")
                        if 'bbox' in prov:
                            bbox = prov['bbox']
                            print(f"     Position: top={bbox.get('t', 'N/A')}, bottom={bbox.get('b', 'N/A')}")
                else:
                    print(f"     ⚠️ No text content")
        
        # Check tables
        if 'tables' in raw_dict and raw_dict['tables']:
            tables_count = len(raw_dict['tables'])
            print(f"\n📊 Tables Section Analysis:")
            print(f"   Tables: {tables_count}")
            
            # Check first table
            first_table = raw_dict['tables'][0]
            if 'cells' in first_table:
                cells_count = len(first_table['cells'])
                print(f"   First table cells: {cells_count}")
                
                # Check first cell
                if first_table['cells']:
                    first_cell = first_table['cells'][0]
                    if 'text' in first_cell:
                        cell_text = first_cell['text']
                        print(f"   First cell: '{cell_text[:30]}...'")
                        print(f"   Cell linebreaks: {cell_text.count(chr(10))}")
        
        # Save raw output for inspection
        output_file = Path("raw_docling_output.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(raw_dict, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Raw output saved to: {output_file}")
        
        # Test markdown export
        print(f"\n🔄 Testing markdown export...")
        try:
            markdown_text = result.document.export_to_markdown()
            markdown_linebreaks = markdown_text.count('\n')
            print(f"   Markdown linebreaks: {markdown_linebreaks}")
            print(f"   Markdown sample: '{markdown_text[:100]}...'")
            
            # Save markdown
            with open("raw_docling_markdown.md", 'w', encoding='utf-8') as f:
                f.write(markdown_text)
            print(f"   Markdown saved to: raw_docling_markdown.md")
            
        except Exception as e:
            print(f"   ❌ Markdown export failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing raw Docling: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    
    print("🚀 Raw Docling Output Test")
    print("=" * 30)
    
    print("\n💡 This script tests raw Docling output to see if")
    print("   linebreaks are provided by Docling itself.")
    
    success = test_raw_docling()
    
    if success:
        print(f"\n🎯 Raw Docling test completed!")
        
        print(f"\n📚 Files created:")
        print("   - raw_docling_output.json (full raw output)")
        print("   - raw_docling_markdown.md (markdown export)")
        
        print(f"\n🔍 What to check:")
        print("   1. Look at the linebreak counts in the analysis above")
        print("   2. Open raw_docling_output.json and search for \\n")
        print("   3. Check if markdown export has better formatting")
        print("   4. Compare with your current JSON output")
        
    else:
        print(f"\n❌ Raw Docling test failed.")
    
    print(f"\n✨ Test completed!")

if __name__ == "__main__":
    main()
