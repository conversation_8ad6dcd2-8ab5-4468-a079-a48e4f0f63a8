<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MetaParse Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        h1 {
            font-size: 2.5em;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 0.3em;
        }
        h2 {
            font-size: 1.8em;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 0.3em;
        }
        h3 {
            font-size: 1.4em;
        }
        h4 {
            font-size: 1.2em;
        }
        p, ul, ol {
            margin-bottom: 1em;
        }
        code {
            font-family: SF<PERSON><PERSON>-<PERSON>, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, monospace;
            background-color: rgba(27, 31, 35, 0.05);
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 85%;
        }
        pre {
            background-color: #f6f8fa;
            border-radius: 3px;
            padding: 16px;
            overflow: auto;
            line-height: 1.45;
        }
        pre code {
            background-color: transparent;
            padding: 0;
            border-radius: 0;
            font-size: 100%;
        }
        a {
            color: #0366d6;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 1em;
        }
        table, th, td {
            border: 1px solid #dfe2e5;
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f6f8fa;
        }
        blockquote {
            margin: 0;
            padding: 0 1em;
            color: #6a737d;
            border-left: 0.25em solid #dfe2e5;
        }
        hr {
            height: 0.25em;
            padding: 0;
            margin: 24px 0;
            background-color: #e1e4e8;
            border: 0;
        }
        .toc {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 20px;
        }
        .toc li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>MetaParse Documentation</h1>

    <h2>Overview</h2>
    <p>MetaParse is a comprehensive document processing and data extraction tool designed to extract structured information from various document formats including PDF, DOCX, and image files. The package provides a robust pipeline for document processing, with capabilities for extracting text, tables, and images, and generating structured output in JSON and Excel formats.</p>
    <p>This documentation provides a detailed overview of the MetaParse package, its architecture, components, and usage examples.</p>

    <div class="toc">
        <h2>Table of Contents</h2>
        <ul>
            <li><a href="#architecture-overview">Architecture Overview</a></li>
            <li><a href="#core-components">Core Components</a>
                <ul>
                    <li><a href="#document-extraction-pipeline">Document Extraction Pipeline</a></li>
                    <li><a href="#document-extractor">Document Extractor</a></li>
                    <li><a href="#processors">Processors</a></li>
                    <li><a href="#utilities">Utilities</a></li>
                </ul>
            </li>
            <li><a href="#command-line-interface">Command-Line Interface</a></li>
            <li><a href="#usage-examples">Usage Examples</a>
                <ul>
                    <li><a href="#single-document-processing">Single Document Processing</a></li>
                    <li><a href="#batch-processing">Batch Processing</a></li>
                    <li><a href="#asynchronous-processing">Asynchronous Processing</a></li>
                </ul>
            </li>
            <li><a href="#output-structure">Output Structure</a>
                <ul>
                    <li><a href="#json-output">JSON Output</a></li>
                    <li><a href="#tables">Tables</a></li>
                    <li><a href="#images">Images</a></li>
                    <li><a href="#summary-sheet">Summary Sheet</a></li>
                </ul>
            </li>
            <li><a href="#advanced-features">Advanced Features</a>
                <ul>
                    <li><a href="#docx-to-pdf-conversion">DOCX to PDF Conversion</a></li>
                    <li><a href="#optimization-levels">Optimization Levels</a></li>
                    <li><a href="#caching">Caching</a></li>
                </ul>
            </li>
            <li><a href="#project-structure">Project Structure</a>
                <ul>
                    <li><a href="#module-dependencies">Module Dependencies</a></li>
                    <li><a href="#key-dependencies">Key Dependencies</a></li>
                </ul>
            </li>
        </ul>
    </div>

    <h2 id="architecture-overview">Architecture Overview</h2>
    <p>MetaParse follows a modular architecture with a pipeline-based approach to document processing. The main components include:</p>
    <ol>
        <li><strong>Core Pipeline</strong>: Orchestrates the entire extraction process</li>
        <li><strong>Extractors</strong>: Handle the initial document parsing and text extraction</li>
        <li><strong>Processors</strong>: Process specific content types (text, tables, images)</li>
        <li><strong>Utilities</strong>: Provide supporting functionality like file management and format conversion</li>
        <li><strong>CLI</strong>: Provides a command-line interface for easy usage</li>
    </ol>
    <p>The package is designed to be extensible, allowing for customization of each component to suit specific needs.</p>

    <h2 id="core-components">Core Components</h2>

    <h3 id="document-extraction-pipeline">Document Extraction Pipeline</h3>
    <p>The <code>DocExtractionPipeline</code> class is the central component that orchestrates the entire extraction process. It coordinates the various extractors and processors to extract and process document content.</p>
    <pre><code>from metaparse.core.pipeline import DocExtractionPipeline

# Initialize the pipeline
pipeline = DocExtractionPipeline(
    doc_path="path/to/document.pdf",
    save_path="path/to/output",
    optimization_level="balanced",
    use_cache=True
)

# Run the pipeline
pipeline.run_pipeline()</code></pre>
    <p>The pipeline handles:</p>
    <ul>
        <li>Document format detection and conversion (e.g., DOCX to PDF)</li>
        <li>Text extraction</li>
        <li>Document segmentation</li>
        <li>Table extraction and processing</li>
        <li>Image extraction and processing</li>
        <li>Output generation in various formats (JSON, Excel, PNG)</li>
    </ul>
    <p>The pipeline also provides static methods for batch processing:</p>
    <pre><code># Process documents in parallel
results = DocExtractionPipeline.process_batch_parallel(
    doc_paths=["path/to/doc1.pdf", "path/to/doc2.docx"],
    save_paths=["path/to/output1", "path/to/output2"],
    optimization_level="speed",
    max_workers=4
)

# Process documents asynchronously
import asyncio

async def main():
    results = await DocExtractionPipeline.process_batch_async(
        doc_paths=["path/to/doc1.pdf", "path/to/doc2.docx"],
        save_paths=["path/to/output1", "path/to/output2"],
        optimization_level="quality",
        concurrency_limit=3
    )
    return results

results = asyncio.run(main())</code></pre>

    <h3 id="document-extractor">Document Extractor</h3>
    <p>The <code>DocExtractor</code> class is responsible for extracting text and structural information from documents. It supports various optimization levels and caching for improved performance.</p>
    <pre><code>from metaparse.extractors.doc_extractor import DocExtractor

# Initialize the extractor
extractor = DocExtractor(
    doc_path="path/to/document.pdf",
    optimization_level="balanced",
    use_cache=True
)

# Extract text and structure
result_json = extractor.extract_text_optimized()</code></pre>
    <p>The extractor provides several methods for text extraction with different optimization strategies:</p>
    <ul>
        <li><code>extract_text_optimized()</code>: Uses the optimal extraction method based on the optimization level</li>
        <li><code>extract_text_standard()</code>: Uses the standard extraction method</li>
        <li><code>extract_text_streaming()</code>: Uses a streaming approach for large documents</li>
    </ul>
    <p>The extractor also handles caching to improve performance when processing the same document multiple times.</p>

    <h3 id="processors">Processors</h3>
    <p>MetaParse includes several processors for handling different types of content:</p>

    <h4>Document Segmenter</h4>
    <p>The <code>DocSegmenter</code> class segments document content into logical sections based on headers.</p>
    <pre><code>from metaparse.processors.doc_segmenter import DocSegmenter

# Initialize the segmenter
segmenter = DocSegmenter(doc_json)

# Run segmentation
segmented_data = segmenter.run_segmentation()</code></pre>

    <h4>Image Extractor</h4>
    <p>The <code>ImageExtractor</code> class extracts and processes images from document content.</p>
    <pre><code>from metaparse.processors.image_processor import ImageExtractor
from metaparse.utils.file_manager import FileManager

# Initialize the image extractor
file_manager = FileManager("path/to/output")
image_extractor = ImageExtractor(result_json, "path/to/document.pdf", file_manager)

# Extract and save images
raw_image_metadata = image_extractor.save_images_from_result_dict()
image_metadata = image_extractor.filter_and_save_clean_image_metadata(raw_image_metadata)</code></pre>

    <h4>Table Processor</h4>
    <p>The <code>TableProcessor</code> class extracts and processes tables from document content.</p>
    <pre><code>from metaparse.processors.table_processor import TableProcessor

# Initialize the table processor
table_processor = TableProcessor(result_json, doc_path="path/to/document.pdf")

# Process tables
table_data = table_processor.process_tables(doc_path="path/to/document.pdf")</code></pre>
    <p>The table processor can handle both regular tables and image-based tables, using the <code>ImageTableHandler</code> for the latter.</p>

    <h3 id="utilities">Utilities</h3>
    <p>MetaParse includes several utility classes for supporting functionality:</p>

    <h4>File Manager</h4>
    <p>The <code>FileManager</code> class handles file operations for document extraction results.</p>
    <pre><code>from metaparse.utils.file_manager import FileManager

# Initialize the file manager
file_manager = FileManager("path/to/output")

# Create output folder
file_manager.create_output_folder()

# Save text
file_manager.save_text("Text content", "output.txt")

# Save JSON
file_manager.save_json({"key": "value"}, "output.json")

# Save image
from PIL import Image
image = Image.new("RGB", (100, 100))
file_manager.save_image(image, "image_name")</code></pre>

    <h4>DOCX to PDF Converter</h4>
    <p>The <code>DocxToPdfConverter</code> class converts DOCX files to PDF format for processing.</p>
    <pre><code>from metaparse.utils.docx_to_pdf import DocxToPdfConverter

# Initialize the converter
converter = DocxToPdfConverter(
    use_temp_dir=True,
    cleanup_temp=True,
    optimize_for_speed=True,
    prefer_method="auto"
)

# Convert DOCX to PDF
pdf_path = converter.convert("path/to/document.docx", "path/to/output")</code></pre>
    <p>The converter supports multiple conversion methods, including LibreOffice and Microsoft Word, and will automatically select the best available method.</p>

    <h2 id="command-line-interface">Command-Line Interface</h2>
    <p>MetaParse provides a command-line interface (CLI) for easy usage. The CLI is implemented in the <code>cli.py</code> module and provides various options for document processing.</p>

    <h3>Basic Usage</h3>
    <pre><code>python -m metaparse.cli --input "path/to/document.pdf" --output "path/to/output" --optimization quality</code></pre>

    <h3>Batch Processing</h3>
    <pre><code>python -m metaparse.cli --input "path/to/documents" --output "path/to/output" --batch --parallel --optimization speed</code></pre>

    <h3>Asynchronous Processing</h3>
    <pre><code>python -m metaparse.cli --input "path/to/documents" --output "path/to/output" --batch --async --workers 4</code></pre>

    <h3>CLI Options</h3>
    <ul>
        <li><code>--input</code>, <code>-i</code>: Input document file or folder (required)</li>
        <li><code>--output</code>, <code>-o</code>: Output base folder (required)</li>
        <li><code>--batch</code>, <code>-b</code>: Process all documents in input folder</li>
        <li><code>--parallel</code>, <code>-p</code>: Use parallel processing for batch mode</li>
        <li><code>--async</code>, <code>-a</code>: Use async processing</li>
        <li><code>--workers</code>, <code>-w</code>: Number of worker processes/threads</li>
        <li><code>--optimization</code>, <code>-opt</code>: Optimization level ('speed', 'balanced', or 'quality')</li>
        <li><code>--no-cache</code>: Disable caching</li>
    </ul>

    <h2 id="usage-examples">Usage Examples</h2>

    <h3 id="single-document-processing">Single Document Processing</h3>
    <pre><code>from metaparse.core.pipeline import DocExtractionPipeline

# Process a single document
pipeline = DocExtractionPipeline(
    doc_path="path/to/document.pdf",
    save_path="path/to/output",
    optimization_level="balanced",
    use_cache=True
)
pipeline.run_pipeline()</code></pre>

    <h3 id="batch-processing">Batch Processing</h3>
    <pre><code>from metaparse.cli import batch_process_docs

# Process multiple documents
doc_files = [
    "path/to/doc1.pdf",
    "path/to/doc2.docx",
    "path/to/doc3.png"
]
output_base_folder = "path/to/output"

# Process in parallel
results = batch_process_docs(
    doc_files,
    output_base_folder,
    parallel=True,
    optimization_level="speed",
    max_workers=4
)

# Process sequentially
results = batch_process_docs(
    doc_files,
    output_base_folder,
    parallel=False,
    optimization_level="quality"
)</code></pre>

    <h3 id="asynchronous-processing">Asynchronous Processing</h3>
    <pre><code>import asyncio
from metaparse.cli import batch_process_docs_async

# Process multiple documents asynchronously
async def main():
    doc_files = [
        "path/to/doc1.pdf",
        "path/to/doc2.docx",
        "path/to/doc3.png"
    ]
    output_base_folder = "path/to/output"

    results = await batch_process_docs_async(
        doc_files,
        output_base_folder,
        optimization_level="quality",
        concurrency_limit=3
    )
    return results

results = asyncio.run(main())</code></pre>

    <h2 id="output-structure">Output Structure</h2>
    <p>MetaParse generates various output files for each processed document:</p>

    <h3 id="json-output">JSON Output</h3>
    <ul>
        <li><code>extracted_data.json</code>: Contains the full extraction results, including text, tables, and images</li>
        <li><code>extracted_data_segmented.json</code>: Contains the document content segmented into logical sections</li>
        <li><code>extracted_data_imagesinfo.json</code>: Contains metadata for extracted images</li>
        <li><code>table_data.json</code>: Contains metadata and content for extracted tables</li>
    </ul>

    <h3 id="tables">Tables</h3>
    <p>Tables are saved as Excel files with names like <code>table_1_1.xlsx</code>, where the first number is the page number and the second is the table index on that page.</p>

    <h3 id="images">Images</h3>
    <p>Images are saved as PNG files in an <code>images</code> subfolder with names like <code>1_image1.png</code>, where the first number is the page number and the second is the image index on that page.</p>

    <h3 id="summary-sheet">Summary Sheet</h3>
    <p>MetaParse generates a summary Excel sheet with details about each processed document. The summary sheet includes the following columns:</p>
    <ul>
        <li><code>document_name</code>: Name of the document (with file extension)</li>
        <li><code>number_of_json_data_files</code>: Number of JSON files generated</li>
        <li><code>number_of_tables</code>: Number of tables extracted</li>
        <li><code>number_of_pictures</code>: Number of images extracted</li>
    </ul>
    <p>The summary sheet is named based on the input:</p>
    <ul>
        <li>For single file mode: <code>{document_name}_summary.xlsx</code></li>
        <li>For batch mode: <code>{input_folder_name}_summary.xlsx</code></li>
    </ul>
    <p>The summary sheet is always saved in the output folder.</p>

    <h2 id="advanced-features">Advanced Features</h2>

    <h3 id="docx-to-pdf-conversion">DOCX to PDF Conversion</h3>
    <p>MetaParse can automatically convert DOCX files to PDF format for processing. The conversion is handled by the <code>DocxToPdfConverter</code> class, which supports multiple conversion methods:</p>
    <ul>
        <li>LibreOffice: Uses LibreOffice in headless mode for conversion</li>
        <li>Microsoft Word: Uses the Microsoft Word COM interface for conversion (Windows only)</li>
    </ul>
    <p>The converter will automatically select the best available method based on the system configuration.</p>
    <pre><code>from metaparse.utils.docx_to_pdf import DocxToPdfConverter

# Initialize the converter
converter = DocxToPdfConverter(
    use_temp_dir=True,
    cleanup_temp=True,
    optimize_for_speed=True,
    prefer_method="auto"
)

# Convert DOCX to PDF
pdf_path = converter.convert("path/to/document.docx", "path/to/output")</code></pre>

    <h3 id="optimization-levels">Optimization Levels</h3>
    <p>MetaParse supports three optimization levels:</p>
    <ul>
        <li><code>speed</code>: Optimizes for processing speed at the expense of quality</li>
        <li><code>balanced</code>: Balances processing speed and quality (default)</li>
        <li><code>quality</code>: Optimizes for extraction quality at the expense of speed</li>
    </ul>
    <p>The optimization level affects various aspects of the extraction process, including image resolution, table detection, and text extraction.</p>

    <h3 id="caching">Caching</h3>
    <p>MetaParse supports caching of extraction results to improve performance when processing the same document multiple times. Caching is enabled by default and can be disabled using the <code>use_cache=False</code> parameter or the <code>--no-cache</code> CLI option.</p>

    <h2 id="project-structure">Project Structure</h2>
    <p>The MetaParse project follows a well-organized modular structure that separates concerns and promotes maintainability. Below is the project structure based on the imports used in the codebase:</p>
    <pre><code>metaparse/
│
├── __init__.py                  # Package initialization
│
├── cli.py                       # Command-line interface
│
├── core/                        # Core components
│   ├── __init__.py
│   └── pipeline.py              # Main document extraction pipeline
│
├── extractors/                  # Document extraction components
│   ├── __init__.py
│   └── doc_extractor.py         # Document text extraction
│
├── processors/                  # Content processors
│   ├── __init__.py
│   ├── doc_segmenter.py         # Document segmentation
│   ├── image_processor.py       # Image extraction and processing
│   └── table_processor.py       # Table extraction and processing
│
└── utils/                       # Utility modules
    ├── __init__.py
    ├── file_manager.py          # File operations management
    └── docx_to_pdf.py           # DOCX to PDF conversion</code></pre>

    <h3 id="module-dependencies">Module Dependencies</h3>
    <p>The diagram below shows the dependencies between the main modules in the MetaParse project:</p>
    <pre><code>                                 ┌─────────────────┐
                                 │                 │
                                 │     cli.py      │
                                 │                 │
                                 └────────┬────────┘
                                          │
                                          │ imports
                                          ▼
┌─────────────────┐            ┌─────────────────────┐
│                 │            │                     │
│  pandas, glob   │◄───imports─┤  pipeline.py        │
│                 │            │  (DocExtractionPipe-│
└─────────────────┘            │  line)              │
                               │                     │
                               └─────────┬───────────┘
                                         │
                 ┌─────────────────┬─────┴─────┬─────────────────┐
                 │                 │           │                 │
                 │ imports         │ imports   │ imports         │
                 ▼                 ▼           ▼                 ▼
    ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
    │                 │  │                 │  │                 │  │                 │
    │ doc_extractor.py│  │ file_manager.py │  │ docx_to_pdf.py  │  │ doc_segmenter.py│
    │                 │  │                 │  │                 │  │                 │
    └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘


    ┌─────────────────┐  ┌─────────────────┐
    │                 │  │                 │
    │image_processor.py  │table_processor.py
    │                 │  │                 │
    └─────────────────┘  └─────────────────┘</code></pre>

    <h3 id="key-dependencies">Key Dependencies</h3>
    <ul>
        <li><strong>cli.py</strong>: Depends on pipeline.py for document processing functionality</li>
        <li><strong>pipeline.py</strong>: Central module that orchestrates the entire extraction process
            <ul>
                <li>Imports doc_extractor.py for text extraction</li>
                <li>Imports file_manager.py for file operations</li>
                <li>Imports docx_to_pdf.py for document conversion</li>
                <li>Imports doc_segmenter.py for content segmentation</li>
                <li>Imports image_processor.py for image extraction</li>
                <li>Imports table_processor.py for table extraction</li>
            </ul>
        </li>
    </ul>
    <p>This modular structure allows for easy maintenance and extension of the codebase, as each component has a specific responsibility and can be modified independently of the others.</p>

    <h2>Conclusion</h2>
    <p>MetaParse is a powerful document processing and data extraction tool with a wide range of features and capabilities. It provides a flexible and extensible architecture for extracting structured information from various document formats, with support for batch processing, asynchronous processing, and optimization for different use cases.</p>
    <p>For more information, refer to the API documentation and examples in the codebase.</p>
</body>
</html>
