# Step 1: Create and activate a virtual environment (optional but recommended)
# You can use UV to create the environment
uv venv .venv
# Activate the environment (Windows)
.venv\Scripts\activate
# Activate the environment (Linux/Mac)
# source .venv/bin/activate

# Step 2: Install build tools
pip install build
# or with UV
uv pip install build

# Step 3: Build the package
python -m build

# Step 4: Install your package in development mode
pip install -e .
# or with UV
uv pip install -e .

# Install the package from PyPI (after publishing)
uv pip install metaparse

# Create a lock file for dependencies
uv pip compile pyproject.toml -o uv.lock

# Install from lock file
uv pip install -r uv.lock
