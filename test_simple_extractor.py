#!/usr/bin/env python3
"""
Test the simple PDF extractor.

This script tests the SimplePDFExtractor to verify linebreaks are working.

Author: Anand Jadhav
Date: 2025-01-27
"""

import json
from pathlib import Path
from simple_pdf_extractor import SimplePDFExtractor


def test_extractor():
    """Test the simple PDF extractor."""
    
    print("Testing Simple PDF Extractor")
    print("=" * 30)
    
    # Configuration
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    output_folder = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder"
    
    # Check if PDF exists
    if not Path(pdf_file).exists():
        print(f"Error: PDF file not found")
        return False
    
    try:
        # Extract text
        extractor = SimplePDFExtractor()
        result = extractor.extract_with_linebreaks(pdf_file)
        
        # Save result
        Path(output_folder).mkdir(parents=True, exist_ok=True)
        output_file = Path(output_folder) / "simple_test_output.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        # Analyze results
        print(f"Output saved: {output_file}")
        
        if 'pages' in result and result['pages']:
            first_page = list(result['pages'].values())[0]
            if 'text' in first_page:
                text = first_page['text']
                linebreak_count = text.count('\n')
                
                print(f"Pages extracted: {len(result['pages'])}")
                print(f"First page text length: {len(text)} characters")
                print(f"Linebreaks found: {linebreak_count}")
                
                if linebreak_count > 0:
                    print("SUCCESS: Linebreaks are present!")
                    
                    # Show sample with visible linebreaks
                    sample = text[:200]
                    visible_sample = sample.replace('\n', '\\n\n')
                    print(f"Sample text:\n{visible_sample}...")
                    
                else:
                    print("ISSUE: No linebreaks found")
                    print(f"Sample text: {text[:200]}...")
            else:
                print("No text found in first page")
        else:
            print("No pages found")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False


if __name__ == "__main__":
    success = test_extractor()
    if success:
        print("\nTest completed successfully!")
    else:
        print("\nTest failed!")
