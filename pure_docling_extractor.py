#!/usr/bin/env python3
"""
Pure Docling extractor that preserves exactly what <PERSON><PERSON> provides.

This module extracts PDF content using Docling without any modifications,
preserving the original linebreaks exactly as <PERSON><PERSON> provides them.

Author: Anand Jadhav
Date: 2025-01-27
"""

import json
from pathlib import Path
from docling.document_converter import DocumentConverter


def extract_pdf_pure_docling(pdf_path: str, output_path: str):
    """
    Extract PDF using pure Docling without any text modifications.
    
    Args:
        pdf_path: Path to PDF file
        output_path: Path to save JSON output
    """
    # Convert PDF using Docling
    converter = DocumentConverter()
    result = converter.convert(pdf_path)
    
    # Get the raw dictionary from Docling - no modifications
    data = result.document.export_to_dict()
    
    # Save to JSON exactly as <PERSON><PERSON> provides it
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    return data


def analyze_docling_output(data):
    """Analyze what <PERSON><PERSON> actually provides."""
    
    print("Analyzing Pure Docling Output")
    print("=" * 30)
    
    print(f"Document sections: {list(data.keys())}")
    
    # Check pages
    if 'pages' in data and data['pages']:
        print(f"Pages: {len(data['pages'])}")
        
        first_page = list(data['pages'].values())[0]
        if 'text' in first_page:
            text = first_page['text']
            print(f"\nFirst page text analysis:")
            print(f"  Length: {len(text)} characters")
            print(f"  Linebreaks (\\n): {text.count(chr(10))}")
            print(f"  Carriage returns (\\r): {text.count(chr(13))}")
            print(f"  Tab characters: {text.count(chr(9))}")
            
            # Show raw text with visible special characters
            sample = text[:200]
            visible_sample = (sample.replace('\n', '\\n')
                                   .replace('\r', '\\r')
                                   .replace('\t', '\\t'))
            print(f"\nSample text (first 200 chars):")
            print(f"  Raw: '{sample}...'")
            print(f"  Visible: '{visible_sample}...'")
            
            if text.count('\n') > 0:
                print(f"  ✅ Docling DOES provide linebreaks!")
                
                # Show lines
                lines = text.split('\n')
                print(f"  Lines in text: {len(lines)}")
                print(f"  First 3 lines:")
                for i, line in enumerate(lines[:3], 1):
                    print(f"    Line {i}: '{line.strip()}'")
            else:
                print(f"  ❌ Docling does NOT provide linebreaks in page text")
    
    # Check individual text elements
    if 'texts' in data and data['texts']:
        print(f"\nText elements: {len(data['texts'])}")
        
        # Check first few text elements
        linebreak_elements = 0
        for i, element in enumerate(data['texts'][:10]):
            if 'text' in element:
                element_text = element['text']
                element_linebreaks = element_text.count('\n')
                if element_linebreaks > 0:
                    linebreak_elements += 1
                    print(f"  Element {i+1}: '{element_text}' (has {element_linebreaks} linebreaks)")
        
        if linebreak_elements > 0:
            print(f"  ✅ {linebreak_elements} text elements have linebreaks")
        else:
            print(f"  ❌ No text elements have linebreaks")
    
    # Check tables
    if 'tables' in data and data['tables']:
        print(f"\nTables: {len(data['tables'])}")
        
        # Check first table cells
        first_table = data['tables'][0]
        if 'cells' in first_table and first_table['cells']:
            cell_linebreaks = 0
            for cell in first_table['cells'][:5]:
                if 'text' in cell:
                    cell_text = cell['text']
                    if cell_text.count('\n') > 0:
                        cell_linebreaks += 1
            
            if cell_linebreaks > 0:
                print(f"  ✅ {cell_linebreaks} table cells have linebreaks")
            else:
                print(f"  ❌ No table cells have linebreaks")


def main():
    """Main function."""
    
    # Your file paths
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    output_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder\pure_docling_output.json"
    
    # Check if PDF exists
    if not Path(pdf_file).exists():
        print("PDF file not found!")
        return
    
    # Create output directory
    Path(output_file).parent.mkdir(parents=True, exist_ok=True)
    
    # Extract PDF with pure Docling
    print("Extracting PDF with pure Docling...")
    data = extract_pdf_pure_docling(pdf_file, output_file)
    
    print(f"Extraction completed: {output_file}")
    
    # Analyze what Docling actually provides
    analyze_docling_output(data)


if __name__ == "__main__":
    main()
