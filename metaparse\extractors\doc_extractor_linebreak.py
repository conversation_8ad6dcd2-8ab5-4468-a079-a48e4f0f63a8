"""
Document extraction module for the MetaParse package.

This module provides classes for extracting content from various document formats.
Author : Anand Jadhav
Date : 2025-05-20
"""

import os
import time
import json
import hashlib
import concurrent.futures
from functools import partial
import asyncio
import aiofiles
from typing import Dict, List, Optional, Union, Any
from docling.document_converter import DocumentConverter  # type: ignore
from docling.datamodel.base_models import InputFormat
from docling.document_converter import PdfFormatOption

# Try to import PdfBackend, or define it if not available
try:
    from docling.datamodel.pipeline_options import PdfBackend, PdfPipelineOptions
except ImportError:
    from enum import Enum

    class PdfBackend(str, Enum):
        """Enum of valid PDF backends."""
        DLPARSE_V1 = "dlparse_v1"
        DLPARSE_V2 = "dlparse_v2"
        DLPARSE_V4 = "dlparse_v4"
        PYPDFIUM2 = "pypdfium2"

    # Define a minimal PdfPipelineOptions class if not available
    class PdfPipelineOptions:
        """Minimal PDF pipeline options."""
        def __init__(self):
            self.table_structure_options = type('obj', (object,), {
                'do_cell_matching': True
            })


class DocExtractorConfig:
    """Configuration class for document extraction settings"""

    def __init__(self, optimization_level: str = 'balanced', custom_settings: Optional[Dict] = None):
        """
        Initialize the configuration with default or custom settings.

        Args:
            optimization_level: Level of optimization ('speed', 'balanced', or 'quality')
            custom_settings: Optional custom settings to override defaults
        """
        self.settings = {
            'text_processing': {
                'normalize_text': True,
                'preserve_original_text': True,
                'case_sensitive': True,
                'vertical_text': True
            },
            'extraction': {
                'preserve_formatting': True,
                'exact_text': True,
                'raw_mode': False,
                'preserve_empty_cells': True,
                'table_options': {
                    'detect_headers': True,
                    'preserve_empty_rows': True,
                    'strict_table_boundaries': True,
                    'vertical_text': {
                        'enabled': True,
                        'merge_vertical_cells': True,
                        'vertical_alignment_tolerance': 2.0,
                        'preserve_vertical_spacing': True,
                        'block_detection': {
                            'enabled': True,
                            'min_vertical_overlap': 0.5,
                            'max_vertical_gap': 10,
                            'combine_threshold': 3
                        }
                    }
                }
            },
            'backend_options': {
                'raw_text_extraction': False,
                'exact_mode': True,
                'table_extraction_mode': 'structure_preserving',
                'text_extraction': {
                    'handle_vertical_text': True,
                    'preserve_text_order': True,
                    'vertical_text_detection': True
                }
            },
            'table_extraction': {
                'cell_detection': {
                    'mode': 'precise',
                    'detect_merged_cells': True,
                    'handle_vertical_text': True,
                    'vertical_text': True,
                    'combine_vertical_blocks': True,
                    'text_distance_threshold': 3,
                    'preserve_block_order': True,
                    'vertical_text_processing': {
                        'enabled': True,
                        'merge_threshold': 3,
                        'preserve_order': True
                    }
                },
                'text_extraction': {
                    'preserve_vertical_text': True,
                    'merge_vertical_blocks': True,
                    'block_joining': True,
                    'text_block_processing': {
                        'combine_vertical_blocks': True,
                        'preserve_block_order': True,
                        'vertical_merge_threshold': 5
                    }
                },
                'image_processing': {
                    'deskew': True,
                    'denoise': True,
                    'enhance_contrast': True,
                    'sharpen': True,
                    'merge_fragments': True,  # This is likely the key setting
                    'fragment_distance_threshold': 5  # Adjust based on your needs
                }
            }
        }

        if custom_settings:
            self._update_nested_dict(self.settings, custom_settings)

    def _update_nested_dict(self, d: Dict, u: Dict) -> Dict:
        """
        Update a nested dictionary with values from another dictionary.

        Args:
            d: Dictionary to update
            u: Dictionary with updates

        Returns:
            Updated dictionary
        """
        for k, v in u.items():
            if isinstance(v, dict) and k in d and isinstance(d[k], dict):
                d[k] = self._update_nested_dict(d[k], v)
            else:
                d[k] = v
        return d

    def get_converter_options(self) -> Dict:
        """
        Get consolidated options for DocumentConverter.

        Returns:
            Dictionary of converter options
        """
        return self.settings

    def get_memory_settings(self) -> Dict:
        """
        Get memory-related settings.

        Returns:
            Dictionary of memory settings
        """
        return {
            'batch_size': 5  # Default batch size for streaming
        }


class DocCache:
    """Caching mechanism for document extraction results"""

    def __init__(self, cache_dir: str = '.doc_cache'):
        """
        Initialize the cache with a cache directory.

        Args:
            cache_dir: Directory to store cache files
        """
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)

    def get_file_hash(self, file_path: str) -> str:
        """
        Generate a hash for the file to use as cache key.

        Args:
            file_path: Path to the file

        Returns:
            MD5 hash of the file
        """
        hasher = hashlib.md5()
        with open(file_path, 'rb') as f:
            buf = f.read(65536)
            while len(buf) > 0:
                hasher.update(buf)
                buf = f.read(65536)
        return hasher.hexdigest()

    def get_cache_path(self, doc_path: str) -> str:
        """
        Get the cache file path for a document.

        Args:
            doc_path: Path to the document

        Returns:
            Path to the cache file
        """
        file_hash = self.get_file_hash(doc_path)
        return os.path.join(self.cache_dir, f"{file_hash}.json")

    def has_cached_result(self, doc_path: str) -> bool:
        """
        Check if a cached result exists for the document.

        Args:
            doc_path: Path to the document

        Returns:
            True if cached result exists, False otherwise
        """
        cache_file = self.get_cache_path(doc_path)
        return os.path.exists(cache_file)

    def get_cached_result(self, doc_path: str) -> Optional[Dict]:
        """
        Get cached extraction result if available.

        Args:
            doc_path: Path to the document

        Returns:
            Cached result as dictionary, or None if not available
        """
        cache_file = self.get_cache_path(doc_path)
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ Error reading cache: {e}")
                return None
        return None

    def save_to_cache(self, doc_path: str, data: Dict) -> bool:
        """
        Save extraction result to cache.

        Args:
            doc_path: Path to the document
            data: Data to cache

        Returns:
            True if saved successfully, False otherwise
        """
        cache_file = self.get_cache_path(doc_path)
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"⚠️ Error saving to cache: {e}")
            return False


class DocExtractor:
    """Optimized document text extractor with enhanced table handling"""

    def __init__(self, doc_path: str, optimization_level: str = 'balanced',
                 use_cache: bool = True, custom_settings: Optional[Dict] = None):
        """
        Initialize the document extractor.

        Args:
            doc_path: Path to the document
            optimization_level: Level of optimization ('speed', 'balanced', or 'quality')
            use_cache: Whether to use caching
            custom_settings: Optional custom settings to override defaults
        """
        if not os.path.exists(doc_path):
            raise FileNotFoundError(f"Document file not found: {doc_path}")

        self.doc_path = doc_path
        self.config = DocExtractorConfig(optimization_level, custom_settings)
        self.use_cache = use_cache
        self._converter = None

        if use_cache:
            self.cache = DocCache()

    @property
    def converter(self) -> DocumentConverter:
        """
        Lazy loading and caching of converter instance.

        Returns:
            DocumentConverter instance
        """
        if not self._converter:
            # self._converter = DocumentConverter(
            #         format_options=self.config.get_converter_options()
            #     )

            try:
                self._converter = DocumentConverter(
                    format_options=self.config.get_converter_options()
                )
                # Get converter options
                converter_options = self.config.get_converter_options()

                # Create format options dictionary
                format_options = {}

                # Check if backend_options contains pdf_backend
                if 'backend_options' in converter_options and 'pdf_backend' in converter_options['backend_options']:
                    # Get the specified PDF backend
                    pdf_backend = converter_options['backend_options']['pdf_backend']
                    print(f"Using custom PDF backend: {pdf_backend}")

                    # Create PDF pipeline options with the specified backend
                    pipeline_options = PdfPipelineOptions()

                    # Configure image processing settings if available
                    if 'image_processing' in converter_options:
                        img_settings = converter_options['image_processing']
                        if 'fragment_distance_threshold' in img_settings:
                            # Adjust table structure options for better image handling
                            pipeline_options.table_structure_options.do_cell_matching = True

                    # Set the PDF backend
                    format_options[InputFormat.PDF] = PdfFormatOption(
                        pipeline_options=pipeline_options,
                        pdf_backend=pdf_backend
                    )

                    self._converter = DocumentConverter(format_options=format_options)
                else:
                    # Use default converter options
                    self._converter = DocumentConverter(
                        format_options=converter_options
                    )
            except Exception as e:
                print(f"❌ Error initializing converter: {e}")
                raise
        return self._converter

    def _process_table_cell_content(self, cell_data: Dict) -> Dict:
        """
        Enhanced table cell content processing with better text extraction.

        Args:
            cell_data: Cell data dictionary

        Returns:
            Processed cell data
        """
        if not cell_data or not isinstance(cell_data, dict):
            return cell_data

        text_blocks = []

        # Handle nested blocks with improved text extraction
        if 'blocks' in cell_data:
            blocks = cell_data['blocks']
            # Sort blocks by position (top to bottom, left to right)
            sorted_blocks = sorted(
                blocks,
                key=lambda x: (
                    x.get('bbox', {}).get('t', 0),
                    x.get('bbox', {}).get('l', 0)
                )
            )

            current_line = []
            last_bottom = None
            last_right = None

            for block in sorted_blocks:
                text = block.get('text', '').strip()
                if not text:
                    continue

                bbox = block.get('bbox', {})
                top = bbox.get('t', 0)
                left = bbox.get('l', 0)

                # Check if this block belongs to the current line
                if (last_bottom is not None and
                    abs(top - last_bottom) <= 5 and  # Vertical tolerance
                    (last_right is None or left - last_right <= 20)):  # Horizontal gap tolerance
                    current_line.append(text)
                else:
                    if current_line:
                        text_blocks.append(' '.join(current_line))
                    current_line = [text]

                last_bottom = bbox.get('b', 0)
                last_right = bbox.get('r', 0)

            # Add last line
            if current_line:
                text_blocks.append(' '.join(current_line))

        else:
            # Handle single text block
            text = cell_data.get('text', '').strip()
            if text:
                text_blocks.append(text)

        # Combine all blocks with appropriate spacing
        combined_text = ' '.join(text_blocks)

        # Update cell data while preserving original structure
        return {
            **cell_data,
            'text': combined_text,
            'original_blocks': text_blocks,
            'processed': True
        }

    def extract_text_optimized(self) -> Dict:
        """
        Extract text with enhanced table handling.

        Returns:
            Dictionary containing extracted document data
        """
        try:
            if self.use_cache:
                cached_result = self.cache.get_cached_result(self.doc_path)
                if cached_result:
                    print(f"✅ Using cached result for {os.path.basename(self.doc_path)}")
                    # Update schema_name from DoclingDocument to MetaParseDocument in cached result
                    if 'schema_name' in cached_result and cached_result['schema_name'] == 'DoclingDocument':
                        cached_result['schema_name'] = 'MetaParseDocument'
                    return cached_result

            print(f" Converting document: {os.path.basename(self.doc_path)}...")
            start_time = time.time()

            # Convert document using configured converter
            result = self.converter.convert(self.doc_path)
            result_dict = result.document.export_to_dict()

            # Update schema_name from DoclingDocument to MetaParseDocument
            if 'schema_name' in result_dict and result_dict['schema_name'] == 'DoclingDocument':
                result_dict['schema_name'] = 'MetaParseDocument'

            # Process tables with enhanced cell handling
            if 'tables' in result_dict:
                processed_tables = []
                seen_tables = set()  # Track unique tables

                for table in result_dict['tables']:
                    # Create a signature for the table based on content
                    table_signature = json.dumps(
                        {
                            'page': table.get('prov', [{}])[0].get('page_no'),
                            'cells': [
                                cell.get('text', '')
                                for cell in table.get('cells', [])
                            ]
                        },
                        sort_keys=True
                    )

                    if table_signature in seen_tables:
                        continue

                    seen_tables.add(table_signature)

                    # Process cells with enhanced content extraction
                    if 'cells' in table:
                        processed_cells = []
                        for cell in table['cells']:
                            processed_cell = self._process_table_cell_content(cell)
                            if processed_cell and processed_cell.get('text', '').strip():
                                processed_cells.append(processed_cell)
                        table['cells'] = processed_cells

                    processed_tables.append(table)

                # Replace original tables with processed ones
                result_dict['tables'] = processed_tables

            elapsed = round(time.time() - start_time, 2)
            print(f"✅ Conversion completed in {elapsed} seconds")

            # Cache results if enabled
            if self.use_cache:
                self.cache.save_to_cache(self.doc_path, result_dict)

            return result_dict

        except Exception as e:
            print(f"❌ Error in extract_text_optimized: {e}")
            raise

    def extract_text_streaming(self) -> Dict:
        """
        Extract text from document using streaming for large files.

        Returns:
            Dictionary containing extracted document data
        """
        try:
            print(f" Converting document using streaming: {os.path.basename(self.doc_path)}...")
            start_time = time.time()

            # This is a placeholder for streaming implementation
            # Actual implementation depends on DocumentConverter capabilities
            memory_settings = self.config.get_memory_settings()
            batch_size = memory_settings['batch_size']

            # Simulate streaming with batch processing
            result_dict = {'schema_name': 'MetaParseDocument', 'pages': {}}

            # Process the document in batches
            # Note: This is conceptual and would need to be adapted to the actual API
            for batch_num, batch_pages in enumerate(self._process_in_batches(batch_size)):
                for page_num, page_content in batch_pages.items():
                    result_dict['pages'][page_num] = page_content

                print(f"  Processed batch {batch_num + 1}...")

            elapsed = round(time.time() - start_time, 2)
            print(f"✅ Streaming conversion completed in {elapsed} seconds")

            # Save to cache if enabled
            if self.use_cache:
                self.cache.save_to_cache(self.doc_path, result_dict)

            return result_dict
        except Exception as e:
            print(f"❌ Error in streaming extraction: {e}")
            return {'pages': {}}

    def _process_in_batches(self, batch_size: int) -> List[Dict]:
        """
        Process document in batches (placeholder implementation).

        Args:
            batch_size: Number of pages to process in each batch

        Yields:
            Dictionary of pages for each batch
        """
        # This is a placeholder - actual implementation would depend on DocumentConverter API
        # For now, we'll just use the regular converter and simulate batching
        result = self.converter.convert(self.doc_path)
        result_dict = result.document.export_to_dict()

        # Update schema_name from DoclingDocument to MetaParseDocument
        if 'schema_name' in result_dict and result_dict['schema_name'] == 'DoclingDocument':
            result_dict['schema_name'] = 'MetaParseDocument'

        pages = result_dict.get('pages', {})
        page_items = list(pages.items())

        # # Split pages into batches
        # for i in range(0, len(page_items), batch_size):
        #     batch = dict(page_items[i:i+batch_size])
        #     yield batch
        # Split pages into batches
        batches = []
        for i in range(0, len(page_items), batch_size):
            batch = dict(page_items[i:i+batch_size])
            batches.append(batch)

        return batches


class DocProcessor:
    """Handles batch processing of multiple document files with various optimizations"""

    def __init__(self, optimization_level: str = 'balanced', use_cache: bool = True,
                 output_dir: Optional[str] = None, max_workers: Optional[int] = None):
        """
        Initialize the document processor.

        Args:
            optimization_level: Level of optimization ('speed', 'balanced', or 'quality')
            use_cache: Whether to use caching
            output_dir: Directory to save output
            max_workers: Maximum number of worker processes
        """
        self.optimization_level = optimization_level
        self.use_cache = use_cache
        self.output_dir = output_dir
        self.max_workers = max_workers

    def process_doc(self, doc_path: str) -> Dict[str, Any]:
        """
        Process a single document file.

        Args:
            doc_path: Path to the document

        Returns:
            Dictionary with processing results
        """
        start_time = time.time()
        print(f"\n Processing: {os.path.basename(doc_path)}")

        result = {
            'doc_path': doc_path,
            'success': False,
            'pages': 0,
            'time': 0,
            'output_path': None,
            'error': None
        }

        # Custom table settings for enhanced extraction
        custom_table_settings = {
            'text_processing': {
                'normalize_text': True,
                'preserve_original_text': True,
                'case_sensitive': True
            },
            'extraction': {
                'preserve_formatting': True,
                'exact_text': True,
                'raw_mode': False,
                'preserve_empty_cells': True,
                'table_options': {
                    'detect_headers': True,
                    'preserve_empty_rows': True,
                    'strict_table_boundaries': True,
                    'vertical_text': {
                        'enabled': True,
                        'merge_vertical_cells': True,
                        'vertical_alignment_tolerance': 2.0,
                        'preserve_vertical_spacing': True
                    },
                    'cell_detection': {
                        'mode': 'precise',
                        'detect_merged_cells': True,
                        'handle_vertical_text': True
                    }
                }
            },
            'backend_options': {
                'raw_text_extraction': False,
                'exact_mode': True,
                'table_extraction_mode': 'structure_preserving',
                'text_extraction': {
                    'handle_vertical_text': True,
                    'preserve_text_order': True,
                    'vertical_text_detection': True
                }
            },
            # Add dedicated image processing settings
            'image_processing': {
                'deskew': True,
                'denoise': True,
                'enhance_contrast': True,
                'sharpen': True,
                'merge_fragments': True,
                'fragment_distance_threshold': 5,
                'min_size': 100,  # Minimum size in pixels to consider as an image
                'preserve_vector_graphics': True
            },
            # Add image detection settings
            'image_detection': {
                'merge_overlapping': True,  # Merge overlapping image elements
                'overlap_threshold': 0.2,  # Percentage of overlap to consider merging
                'proximity_merge': True,  # Merge nearby image elements
                'proximity_threshold': 50,  # Distance in pixels to consider nearby
                'min_fragment_size': 100,  # Minimum size for image fragments
                'treat_as_single_image': True,  # Treat adjacent image fragments as a single image
                'detect_compound_images': True  # Detect images composed of multiple elements
            },
            'table_options': {
                'detect_headers': True,
                'preserve_empty_rows': True,
                'strict_table_boundaries': True,
                'vertical_text': {
                    'enabled': True,
                    'merge_vertical_cells': True,
                    'vertical_alignment_tolerance': 2.0,
                    'preserve_vertical_spacing': True,
                    'block_detection': {
                        'enabled': True,
                        'min_vertical_overlap': 0.5,
                        'max_vertical_gap': 20,
                        'combine_threshold': 5
                    }
                },
                'cell_detection': {
                    'mode': 'precise',
                    'detect_merged_cells': True,
                    'handle_vertical_text': True,
                    'vertical_text_processing': {
                        'enabled': True,
                        'merge_threshold': 5,
                        'preserve_order': True
                    }
                },
                'text_block_processing': {
                    'combine_vertical_blocks': True,
                    'preserve_block_order': True,
                    'vertical_merge_threshold': 10
                }
            }
        }

        try:
            # Use enhanced settings for quality extraction
            extractor = DocExtractor(
                doc_path,
                optimization_level='quality',  # Use quality as base
                use_cache=False,  # Disable cache while testing
                custom_settings=custom_table_settings
            )

            extracted_data = extractor.extract_text_optimized()

            if extracted_data and extracted_data.get('pages'):
                # Create output directory
                if self.output_dir:
                    base_output_dir = self.output_dir
                else:
                    base_output_dir = os.path.dirname(doc_path)

                output_folder = os.path.join(
                    base_output_dir,
                    'output',
                    os.path.splitext(os.path.basename(doc_path))[0]
                )
                os.makedirs(output_folder, exist_ok=True)

                # Update schema_name from DoclingDocument to MetaParseDocument
                if 'schema_name' in extracted_data and extracted_data['schema_name'] == 'DoclingDocument':
                    extracted_data['schema_name'] = 'MetaParseDocument'

                # Save extracted data
                json_output_path = os.path.join(output_folder, "extracted_output.json")
                with open(json_output_path, "w", encoding="utf-8") as f:
                    json.dump(extracted_data, f, indent=2, ensure_ascii=False)

                total_pages = len(extracted_data['pages'])
                print(f"✅ Extracted {total_pages} pages")
                print(f"✅ Data saved to {json_output_path}")

                # Print sample from first page
                if extracted_data['pages']:
                    first_page = extracted_data['pages'].get('1', {})  # Get first page
                    print("\nSample text from first page:")
                    text = first_page.get('text', '')
                    print(text[:100] + "..." if len(text) > 100 else text)

                result['success'] = True
                result['pages'] = total_pages
                result['output_path'] = json_output_path
            else:
                print("⚠️ No valid data extracted")
                result['error'] = "No valid data extracted"

        except Exception as e:
            error_msg = f"Error processing {os.path.basename(doc_path)}: {e}"
            print(f"❌ {error_msg}")
            result['error'] = error_msg
        finally:
            elapsed = round(time.time() - start_time, 2)
            print(f" Time: {elapsed} seconds")
            result['time'] = elapsed

        return result

    def process_docs_sequential(self, doc_files: List[str]) -> List[Dict[str, Any]]:
        """
        Process multiple document files sequentially.

        Args:
            doc_files: List of document file paths

        Returns:
            List of dictionaries with processing results
        """
        results = []
        total = len(doc_files)

        for i, doc_file in enumerate(doc_files, 1):
            print(f"\n[{i}/{total}] Processing file {i} of {total}")
            if os.path.exists(doc_file):
                result = self.process_doc(doc_file)
                results.append(result)
            else:
                print(f"⚠️ File not found: {doc_file}")
                results.append({
                    'doc_path': doc_file,
                    'success': False,
                    'error': 'File not found'
                })

            # Show progress
            print(f"Progress: {i}/{total} ({round(i/total*100)}%)")

        return results

    def process_docs_parallel(self, doc_files: List[str]) -> List[Dict[str, Any]]:
        """
        Process multiple document files in parallel.

        Args:
            doc_files: List of document file paths

        Returns:
            List of dictionaries with processing results
        """
        # Filter out non-existent files
        valid_files = [f for f in doc_files if os.path.exists(f)]
        invalid_files = [f for f in doc_files if f not in valid_files]

        results = []

        # Add results for invalid files
        for file in invalid_files:
            results.append({
                'doc_path': file,
                'success': False,
                'error': 'File not found'
            })

        if not valid_files:
            print("No valid document files found.")
            return results

        print(f"Processing {len(valid_files)} document files in parallel...")

        # Use ProcessPoolExecutor for CPU-bound tasks
        with concurrent.futures.ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all processing tasks
            future_to_doc = {
                executor.submit(self.process_doc, doc_file): doc_file
                for doc_file in valid_files
            }

            # Process results as they complete
            for i, future in enumerate(concurrent.futures.as_completed(future_to_doc), 1):
                doc_file = future_to_doc[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    print(f"❌ Error in parallel processing for {os.path.basename(doc_file)}: {e}")
                    results.append({
                        'doc_path': doc_file,
                        'success': False,
                        'error': str(e)
                    })

                # Show progress
                print(f"Progress: {i}/{len(valid_files)} ({round(i/len(valid_files)*100)}%)")

        return results

    async def process_doc_async(self, doc_path: str) -> Dict[str, Any]:
        """
        Process a document file with asynchronous I/O operations.

        Args:
            doc_path: Path to the document file

        Returns:
            Dictionary with processing results
        """
        start_time = time.time()
        print(f"\n Processing async: {os.path.basename(doc_path)}")

        result = {
            'doc_path': doc_path,
            'success': False,
            'pages': 0,
            'time': 0,
            'output_path': None,
            'error': None
        }

        try:
            # Document processing is CPU-bound, so we run it in a thread pool
            loop = asyncio.get_event_loop()
            extractor = DocExtractor(
                doc_path,
                optimization_level=self.optimization_level,
                use_cache=self.use_cache
            )

            # Run CPU-bound extraction in a thread pool
            extracted_data = await loop.run_in_executor(
                None, extractor.extract_text_optimized
            )

            if extracted_data and extracted_data.get('pages'):
                # Create output directory
                if self.output_dir:
                    base_output_dir = self.output_dir
                else:
                    base_output_dir = os.path.dirname(doc_path)

                output_folder = os.path.join(
                    base_output_dir,
                    'output',
                    os.path.splitext(os.path.basename(doc_path))[0]
                )
                os.makedirs(output_folder, exist_ok=True)

                # Update schema_name from DoclingDocument to MetaParseDocument
                if 'schema_name' in extracted_data and extracted_data['schema_name'] == 'DoclingDocument':
                    extracted_data['schema_name'] = 'MetaParseDocument'

                # Save extracted data asynchronously
                json_output_path = os.path.join(output_folder, "extracted_output.json")

                async with aiofiles.open(json_output_path, "w", encoding="utf-8") as f:
                    await f.write(json.dumps(extracted_data, indent=2, ensure_ascii=False))

                total_pages = len(extracted_data['pages'])
                print(f"✅ Extracted {total_pages} pages")
                print(f"✅ Data saved to {json_output_path}")

                result['success'] = True
                result['pages'] = total_pages
                result['output_path'] = json_output_path
            else:
                print("⚠️ No valid data extracted")
                result['error'] = "No valid data extracted"

        except Exception as e:
            error_msg = f"Error processing {os.path.basename(doc_path)}: {e}"
            print(f"❌ {error_msg}")
            result['error'] = error_msg
        finally:
            elapsed = round(time.time() - start_time, 2)
            print(f" Time: {elapsed} seconds")
            result['time'] = elapsed

        return result

    async def process_docs_async(self, doc_files: List[str], concurrency_limit: int = 5) -> List[Dict[str, Any]]:
        """
        Process multiple document files using async/await pattern with concurrency control.

        Args:
            doc_files: List of document file paths
            concurrency_limit: Maximum number of concurrent tasks

        Returns:
            List of dictionaries with processing results
        """
        # Filter out non-existent files
        valid_files = [f for f in doc_files if os.path.exists(f)]
        invalid_files = [f for f in doc_files if f not in valid_files]

        results = []

        # Add results for invalid files
        for file in invalid_files:
            results.append({
                'doc_path': file,
                'success': False,
                'error': 'File not found'
            })

        if not valid_files:
            print("No valid document files found.")
            return results

        print(f"Processing {len(valid_files)} document files asynchronously (limit: {concurrency_limit})...")

        # Process files with concurrency limit
        semaphore = asyncio.Semaphore(concurrency_limit)

        async def process_with_semaphore(doc_file):
            async with semaphore:
                return await self.process_doc_async(doc_file)

        # Create tasks for all files
        tasks = [process_with_semaphore(doc_file) for doc_file in valid_files]

        # Process all tasks and collect results
        for i, task_result in enumerate(asyncio.as_completed(tasks), 1):
            result = await task_result
            results.append(result)
            print(f"Progress: {i}/{len(valid_files)} ({round(i/len(valid_files)*100)}%)")

        return results
