#!/usr/bin/env python3
"""
Example script showing how to use the updated DocExtractor main block
with your specific PDF file and output folder.

This script demonstrates different ways to run the extraction with
linebreak preservation using your specified paths.

Author: Anand Jadhav
Date: 2025-01-27
"""

import os
import subprocess
import sys
from pathlib import Path

def run_basic_extraction():
    """Run basic extraction with default settings."""
    print("🔄 Running Basic Extraction with Default Settings")
    print("-" * 50)
    
    print("📋 This will:")
    print("   - Use your default PDF file: 04760587J.article.002.pdf")
    print("   - Save to your default output folder: extracted_data_folder")
    print("   - Preserve linebreaks as \\n characters")
    print("   - Use quality optimization level")
    
    cmd = [sys.executable, "metaparse/extractors/doc_extractor_linebreak.py"]
    
    print(f"\n🚀 Command: {' '.join(cmd)}")
    print("\n⏳ Processing... (this may take a few moments)")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("\n✅ Extraction completed successfully!")
        
        # Check output
        output_folder = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder"
        output_file = Path(output_folder) / "04760587J.article.002_extracted.json"
        
        if output_file.exists():
            file_size = output_file.stat().st_size
            print(f"📁 Output file: {output_file}")
            print(f"📊 File size: {file_size:,} bytes")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Extraction failed with return code: {e.returncode}")
    except Exception as e:
        print(f"\n❌ Error: {e}")

def run_extraction_with_pipe_separators():
    """Run extraction with pipe separators instead of newlines."""
    print("\n🔄 Running Extraction with Pipe Separators")
    print("-" * 50)
    
    print("📋 This will:")
    print("   - Use pipe characters ' | ' instead of \\n for line breaks")
    print("   - Use double pipes ' || ' for paragraph separation")
    print("   - Useful for certain text processing pipelines")
    
    cmd = [
        sys.executable, 
        "metaparse/extractors/doc_extractor_linebreak.py",
        "--linebreak-char", " | ",
        "--paragraph-separator", " || "
    ]
    
    print(f"\n🚀 Command: {' '.join(cmd)}")
    print("\n⏳ Processing...")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("\n✅ Extraction with pipe separators completed!")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Extraction failed with return code: {e.returncode}")
    except Exception as e:
        print(f"\n❌ Error: {e}")

def run_extraction_without_linebreaks():
    """Run extraction without linebreak preservation (traditional mode)."""
    print("\n🔄 Running Extraction WITHOUT Linebreak Preservation")
    print("-" * 50)
    
    print("📋 This will:")
    print("   - Disable linebreak preservation")
    print("   - Join text with spaces (traditional Docling behavior)")
    print("   - Useful for comparison with linebreak-preserved version")
    
    cmd = [
        sys.executable, 
        "metaparse/extractors/doc_extractor_linebreak.py",
        "--no-linebreaks"
    ]
    
    print(f"\n🚀 Command: {' '.join(cmd)}")
    print("\n⏳ Processing...")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("\n✅ Extraction without linebreaks completed!")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Extraction failed with return code: {e.returncode}")
    except Exception as e:
        print(f"\n❌ Error: {e}")

def show_demo():
    """Show the demo mode."""
    print("\n🎯 Showing Demo Mode")
    print("-" * 30)
    
    cmd = [sys.executable, "metaparse/extractors/doc_extractor_linebreak.py", "--demo"]
    
    try:
        result = subprocess.run(cmd, check=True)
        print("\n✅ Demo completed!")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Demo failed with return code: {e.returncode}")
    except Exception as e:
        print(f"\n❌ Error: {e}")

def check_prerequisites():
    """Check if all prerequisites are met."""
    print("🔍 Checking Prerequisites")
    print("-" * 30)
    
    # Check if we're in the right directory
    if not Path("metaparse/extractors/doc_extractor_linebreak.py").exists():
        print("❌ Error: doc_extractor_linebreak.py not found")
        print("💡 Please run this script from the MetaParse project root directory")
        return False
    
    print("✅ DocExtractor module found")
    
    # Check if PDF file exists
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    if Path(pdf_file).exists():
        print("✅ PDF file found")
        file_size = Path(pdf_file).stat().st_size
        print(f"📊 PDF file size: {file_size:,} bytes")
    else:
        print("❌ PDF file not found")
        print(f"💡 Expected location: {pdf_file}")
        print("💡 Please ensure the PDF file exists at this location")
        return False
    
    # Check output directory
    output_folder = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder"
    try:
        Path(output_folder).mkdir(parents=True, exist_ok=True)
        print("✅ Output folder ready")
    except Exception as e:
        print(f"❌ Cannot create output folder: {e}")
        return False
    
    return True

def main():
    """Main function to run extraction examples."""
    print("🚀 DocExtractor Usage Examples")
    print("=" * 50)
    
    print("\n📋 Your Configuration:")
    print("   PDF file: C:\\Users\\<USER>\\Desktop\\Anand\\MetaParse\\pdf_folder\\04760587J.article.002.pdf")
    print("   Output folder: C:\\Users\\<USER>\\Desktop\\Anand\\MetaParse\\extracted_data_folder")
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please fix the issues above.")
        return
    
    print("\n✅ All prerequisites met!")
    
    # Show available options
    print("\n📚 Available Examples:")
    print("1. Basic extraction with linebreak preservation")
    print("2. Extraction with pipe separators")
    print("3. Extraction without linebreak preservation")
    print("4. Show demo mode")
    print("5. Run all examples")
    
    try:
        choice = input("\n🔢 Enter your choice (1-5): ").strip()
        
        if choice == "1":
            run_basic_extraction()
        elif choice == "2":
            run_extraction_with_pipe_separators()
        elif choice == "3":
            run_extraction_without_linebreaks()
        elif choice == "4":
            show_demo()
        elif choice == "5":
            print("\n🔄 Running all examples...")
            run_basic_extraction()
            run_extraction_with_pipe_separators()
            run_extraction_without_linebreaks()
            show_demo()
        else:
            print("❌ Invalid choice. Please run the script again.")
            return
            
    except KeyboardInterrupt:
        print("\n\n⏹️ Interrupted by user")
        return
    except Exception as e:
        print(f"\n❌ Error: {e}")
        return
    
    print("\n🎯 Example Completed!")
    print("\n📁 Check your output folder for the extracted JSON files:")
    print("   C:\\Users\\<USER>\\Desktop\\Anand\\MetaParse\\extracted_data_folder")
    
    print("\n💡 You can also run the extractor directly:")
    print("   python metaparse/extractors/doc_extractor_linebreak.py")
    print("   python metaparse/extractors/doc_extractor_linebreak.py --help")

if __name__ == "__main__":
    main()
