"""
Ligature processing module for the MetaParse package.

This module provides functionality to replace ligature encodings with their proper
English character equivalents in JSON data.
"""

import re
import json
from typing import Dict, Any, List, Union


class LigatureProcessor:
    """
    Process and replace ligature encodings in document JSON data.
    
    This class handles the replacement of ligature encodings with their proper
    English character equivalents in JSON data extracted from documents.
    """
    
    def __init__(self, json_data: Dict[str, Any]):
        """
        Initialize the ligature processor.
        
        Args:
            json_data: Document data in dictionary format
        """
        self.json_data = json_data
    
    def replace_ligatures(self, text: str) -> str:
        """
        Replace ligature encodings with their proper English character equivalents.
        
        Args:
            text: Text that may contain ligature encodings
            
        Returns:
            Text with ligatures replaced by their proper character equivalents
        """
        if not text:
            return text
        
        # Common ligature mappings
        ligature_map = {
            # Latin ligatures
            " /uniFB00 ": "ff",    # LATIN SMALL LIGATURE FF
            " /uniFB01 ": "fi",    # LATIN SMALL LIGATURE FI
            " /uniFB02 ": "fl",    # LATIN SMALL LIGATURE FL
            " /uniFB03 ": "ffi",   # LATIN SMALL LIGATURE FFI
            " /uniFB04 ": "ffl",   # LATIN SMALL LIGATURE FFL
            " /uniFB05 ": "st",    # LATIN SMALL LIGATURE LONG S T
            " /uniFB06 ": "st",    # LATIN SMALL LIGATURE ST

            "/uniFB00": "ff",    # LATIN SMALL LIGATURE FF
            "/uniFB01": "fi",    # LATIN SMALL LIGATURE FI
            "/uniFB02": "fl",    # LATIN SMALL LIGATURE FL
            "/uniFB03": "ffi",   # LATIN SMALL LIGATURE FFI
            "/uniFB04": "ffl",   # LATIN SMALL LIGATURE FFL
            "/uniFB05": "st",    # LATIN SMALL LIGATURE LONG S T
            "/uniFB06": "st",    # LATIN SMALL LIGATURE ST
            
            # Other common encodings that might appear
            "/uni0027": "'",     # APOSTROPHE
            "/uni2019": "'",     # RIGHT SINGLE QUOTATION MARK
            "/uni2013": "–",     # EN DASH
            "/uni2014": "—",     # EM DASH
            
            # Simplified versions that might appear
            " /FB00 ": "ff",
            " /FB01 ": "fi",
            " /FB02 ": "fl",
            " /FB03 ": "ffi",
            " /FB04 ": "ffl",
            " /FB05 ": "st",
            " /FB06 ": "st",

            "/FB00": "ff",
            "/FB01": "fi",
            "/FB02": "fl",
            "/FB03": "ffi",
            "/FB04": "ffl",
            "/FB05": "st",
            "/FB06": "st",
            
            # Additional patterns sometimes seen
            r" \ufb01 ": "fi",
            r" \ufb02 ": "fl",
            r"\ufb01": "fi",
            r"\ufb02": "fl",
            r"\u2019": "'",
        }
        
        # Step 1: Replace all ligatures with their proper English character equivalents
        for ligature, replacement in ligature_map.items():
            text = text.replace(ligature, replacement)
        
        # # Step 2: Fix spacing issues that might be introduced during replacement
        # # Create a list of all the replacement values (ff, fi, fl, etc.)
        # replacements = set(ligature_map.values())
        
        # # For each replacement value, create patterns to fix spacing issues
        # for repl in replacements:
        #     # Pattern 1: Fix cases where the replacement is surrounded by spaces
        #     # Example: "quanti fi ed" -> "quantified"
        #     pattern_with_spaces = r'(\w) ' + re.escape(repl) + r' (\w)'
        #     text = re.sub(pattern_with_spaces, r'\1' + repl + r'\2', text)
            
        #     # Pattern 2: Fix cases where there's a space before the replacement
        #     # Example: "quanti fi" -> "quantifi"
        #     pattern_space_before = r'(\w) ' + re.escape(repl) + r'(\w)'
        #     text = re.sub(pattern_space_before, r'\1' + repl + r'\2', text)
            
        #     # Pattern 3: Fix cases where there's a space after the replacement
        #     # Example: "fi ed" -> "fied"
        #     pattern_space_after = r'(\w)' + re.escape(repl) + r' (\w)'
        #     text = re.sub(pattern_space_after, r'\1' + repl + r'\2', text)
            
        #     # Pattern 4: Fix cases at word boundaries
        #     # Example: " fi " -> " fi"
        #     pattern_word_boundary = r'(\s)' + re.escape(repl) + r' '
        #     text = re.sub(pattern_word_boundary, r'\1' + repl, text)
            
        #     # Pattern 5: Fix cases at the end of a word
        #     # Example: "modi fi " -> "modifi "
        #     pattern_end_of_word = r'(\w) ' + re.escape(repl) + r'(\s)'
        #     text = re.sub(pattern_end_of_word, r'\1' + repl + r'\2', text)
        
        # # Step 3: Handle special compound ligatures that might have been broken
        # # Example: "f fi" -> "ffi" or "f fl" -> "ffl"
        # text = re.sub(r'f fi', 'ffi', text)
        # text = re.sub(r'f fl', 'ffl', text)
        
        return text
    
    def process_text_elements(self) -> None:
        """
        Process all text elements in the JSON data to replace ligatures.
        """
        # Process texts array
        if "texts" in self.json_data:
            for text_obj in self.json_data["texts"]:
                if "text" in text_obj:
                    text_obj["text"] = self.replace_ligatures(text_obj["text"])
        
        # Process sections array if present
        if "sections" in self.json_data:
            for section in self.json_data["sections"]:
                if "text" in section:
                    section["text"] = self.replace_ligatures(section["text"])
        
        # Process tables array if present
        if "tables" in self.json_data:
            for table in self.json_data["tables"]:
                # Process table caption
                if "caption" in table and isinstance(table["caption"], str):
                    table["caption"] = self.replace_ligatures(table["caption"])
                
                # Process table cells
                if "cells" in table:
                    for cell in table["cells"]:
                        if "text" in cell:
                            cell["text"] = self.replace_ligatures(cell["text"])
        
        # Process pictures array if present
        if "pictures" in self.json_data:
            for picture in self.json_data["pictures"]:
                # Process picture captions
                if "captions" in picture:
                    for i, caption in enumerate(picture["captions"]):
                        if isinstance(caption, str):
                            picture["captions"][i] = self.replace_ligatures(caption)
                        elif isinstance(caption, dict) and "title" in caption:
                            caption["title"] = self.replace_ligatures(caption["title"])
    
    def process_segmented_json(self) -> None:
        """
        Process segmented JSON data to replace ligatures.
        """
        # Process text_data array in segmented JSON
        if "text_data" in self.json_data:
            for section in self.json_data["text_data"]:
                # Process section header
                if "header" in section:
                    section["header"] = self.replace_ligatures(section["header"])
                
                # Process section text ✔
                if "section_text" in section:
                    for text_ref in section["section_text"]:
                        for ref_key, text_obj in text_ref.items():
                            if isinstance(text_obj, dict) and "text" in text_obj:
                                text_obj["text"] = self.replace_ligatures(text_obj["text"])
                            elif isinstance(text_obj, str):
                                text_ref[ref_key] = self.replace_ligatures(text_obj)
    
    def process_json(self) -> Dict[str, Any]:
        """
        Process the JSON data to replace all ligatures.
        
        Returns:
            Processed JSON data with ligatures replaced 
        """
        # Check if this is a segmented JSON
        if "text_data" in self.json_data:
            self.process_segmented_json()
        else:
            self.process_text_elements()
        
        return self.json_data


def process_document_json(json_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process document JSON data to replace ligatures.
    
    Args:
        json_data: Document data in dictionary format
        
    Returns:
        Processed JSON data with ligatures replaced
    """
    processor = LigatureProcessor(json_data)
    return processor.process_json()


if __name__ == "__main__":
    import argparse
    import os
    
    # Create argument parser
    parser = argparse.ArgumentParser(description="Replace ligatures in document JSON data")
    parser.add_argument("--input_json", required=True, help="Path to the input JSON file")
    parser.add_argument("--output_json", required=True, help="Path to the output JSON file")
    
    # Parse arguments
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input_json):
        print(f"Error: Input file {args.input_json} not found")
        exit(1)
    
    # Load input JSON
    with open(args.input_json, "r", encoding="utf-8") as f:
        json_data = json.load(f)
    
    # Process JSON
    processed_json = process_document_json(json_data)
    
    # Save output JSON
    with open(args.output_json, "w", encoding="utf-8") as f:
        json.dump(processed_json, f, indent=2, ensure_ascii=False)
    
    print(f"Ligature replacement complete. Output saved to {args.output_json}")
