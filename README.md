# MetaParse

A comprehensive document processing and data extraction tool designed to extract structured information from various document formats including PDF, DOCX, and image files.

## Features

- Extract text, tables, and images from documents
- Process multiple document formats (PDF, DOCX, images)
- Generate structured output in JSON and Excel formats
- Command-line interface for easy usage
- Batch processing capabilities
- Asynchronous processing support

## Installation

```bash
pip install metaparse
```

Or with UV:

```bash
uv pip install metaparse
```

## Usage

### Command Line

```bash
# Process a single document
metaparse --input "path/to/document.pdf" --output "path/to/output" --optimization quality

# Batch processing
metaparse --input "path/to/documents" --output "path/to/output" --batch --parallel --optimization speed

# Asynchronous processing
metaparse --input "path/to/documents" --output "path/to/output" --batch --async --workers 4
```

### Python API

```python
from metaparse.core.pipeline import DocExtractionPipeline

# Process a single document
pipeline = DocExtractionPipeline(
    doc_path="path/to/document.pdf",
    save_path="path/to/output",
    optimization_level="balanced",
    use_cache=True
)
pipeline.run_pipeline()
```

## License

MIT
