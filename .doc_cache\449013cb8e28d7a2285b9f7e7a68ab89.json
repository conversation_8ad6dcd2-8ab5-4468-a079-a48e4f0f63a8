{"schema_name": "MetaParseDocument", "version": "1.3.0", "name": "04760587J.article.002", "origin": {"mimetype": "application/pdf", "binary_hash": 6636608289976669575, "filename": "04760587J.article.002.pdf"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/texts/0"}, {"$ref": "#/texts/1"}, {"$ref": "#/pictures/0"}, {"$ref": "#/pictures/1"}, {"$ref": "#/texts/2"}, {"$ref": "#/texts/3"}, {"$ref": "#/texts/4"}, {"$ref": "#/texts/5"}, {"$ref": "#/groups/0"}, {"$ref": "#/texts/10"}, {"$ref": "#/texts/11"}, {"$ref": "#/texts/12"}, {"$ref": "#/pictures/2"}, {"$ref": "#/texts/13"}, {"$ref": "#/pictures/3"}, {"$ref": "#/texts/14"}, {"$ref": "#/texts/15"}, {"$ref": "#/texts/16"}, {"$ref": "#/texts/17"}, {"$ref": "#/texts/18"}, {"$ref": "#/texts/19"}, {"$ref": "#/texts/20"}, {"$ref": "#/texts/21"}, {"$ref": "#/texts/22"}, {"$ref": "#/texts/23"}, {"$ref": "#/texts/24"}, {"$ref": "#/texts/25"}, {"$ref": "#/texts/26"}, {"$ref": "#/pictures/4"}, {"$ref": "#/texts/27"}, {"$ref": "#/tables/0"}, {"$ref": "#/texts/28"}, {"$ref": "#/texts/29"}, {"$ref": "#/texts/30"}, {"$ref": "#/texts/31"}, {"$ref": "#/texts/32"}, {"$ref": "#/texts/33"}, {"$ref": "#/texts/34"}, {"$ref": "#/groups/1"}, {"$ref": "#/texts/36"}, {"$ref": "#/texts/37"}, {"$ref": "#/texts/38"}, {"$ref": "#/texts/39"}, {"$ref": "#/texts/40"}, {"$ref": "#/texts/41"}, {"$ref": "#/texts/42"}, {"$ref": "#/texts/43"}, {"$ref": "#/texts/44"}, {"$ref": "#/texts/45"}, {"$ref": "#/texts/46"}, {"$ref": "#/pictures/5"}, {"$ref": "#/texts/47"}, {"$ref": "#/texts/48"}, {"$ref": "#/texts/49"}, {"$ref": "#/texts/50"}, {"$ref": "#/texts/51"}, {"$ref": "#/texts/52"}, {"$ref": "#/texts/53"}, {"$ref": "#/pictures/6"}, {"$ref": "#/groups/2"}, {"$ref": "#/texts/56"}, {"$ref": "#/texts/57"}, {"$ref": "#/texts/58"}, {"$ref": "#/texts/59"}, {"$ref": "#/texts/60"}, {"$ref": "#/texts/61"}, {"$ref": "#/pictures/7"}, {"$ref": "#/texts/63"}, {"$ref": "#/texts/64"}, {"$ref": "#/pictures/8"}, {"$ref": "#/texts/65"}, {"$ref": "#/tables/1"}, {"$ref": "#/texts/66"}, {"$ref": "#/pictures/9"}, {"$ref": "#/texts/100"}, {"$ref": "#/texts/101"}, {"$ref": "#/texts/102"}, {"$ref": "#/texts/103"}, {"$ref": "#/texts/104"}, {"$ref": "#/pictures/10"}, {"$ref": "#/pictures/11"}, {"$ref": "#/texts/119"}, {"$ref": "#/texts/120"}, {"$ref": "#/texts/121"}, {"$ref": "#/texts/122"}, {"$ref": "#/texts/123"}, {"$ref": "#/pictures/12"}, {"$ref": "#/texts/124"}, {"$ref": "#/texts/125"}, {"$ref": "#/texts/126"}, {"$ref": "#/texts/127"}, {"$ref": "#/pictures/13"}, {"$ref": "#/texts/129"}, {"$ref": "#/texts/130"}, {"$ref": "#/texts/131"}, {"$ref": "#/texts/132"}, {"$ref": "#/texts/133"}, {"$ref": "#/texts/134"}, {"$ref": "#/texts/135"}, {"$ref": "#/texts/136"}, {"$ref": "#/texts/137"}, {"$ref": "#/texts/138"}, {"$ref": "#/groups/3"}, {"$ref": "#/texts/141"}, {"$ref": "#/texts/142"}, {"$ref": "#/pictures/14"}, {"$ref": "#/groups/4"}, {"$ref": "#/texts/157"}, {"$ref": "#/texts/158"}, {"$ref": "#/groups/5"}, {"$ref": "#/texts/174"}, {"$ref": "#/texts/175"}, {"$ref": "#/pictures/15"}, {"$ref": "#/groups/6"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/6"}, {"$ref": "#/texts/7"}, {"$ref": "#/texts/8"}, {"$ref": "#/texts/9"}], "content_layer": "body", "name": "group", "label": "key_value_area"}, {"self_ref": "#/groups/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/35"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/2", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/55"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/3", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/139"}, {"$ref": "#/texts/140"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/4", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/143"}, {"$ref": "#/texts/144"}, {"$ref": "#/texts/145"}, {"$ref": "#/texts/146"}, {"$ref": "#/texts/147"}, {"$ref": "#/texts/148"}, {"$ref": "#/texts/149"}, {"$ref": "#/texts/150"}, {"$ref": "#/texts/151"}, {"$ref": "#/texts/152"}, {"$ref": "#/texts/153"}, {"$ref": "#/texts/154"}, {"$ref": "#/texts/155"}, {"$ref": "#/texts/156"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/5", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/159"}, {"$ref": "#/texts/160"}, {"$ref": "#/texts/161"}, {"$ref": "#/texts/162"}, {"$ref": "#/texts/163"}, {"$ref": "#/texts/164"}, {"$ref": "#/texts/165"}, {"$ref": "#/texts/166"}, {"$ref": "#/texts/167"}, {"$ref": "#/texts/168"}, {"$ref": "#/texts/169"}, {"$ref": "#/texts/170"}, {"$ref": "#/texts/171"}, {"$ref": "#/texts/172"}, {"$ref": "#/texts/173"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/6", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/176"}, {"$ref": "#/texts/177"}, {"$ref": "#/texts/178"}], "content_layer": "body", "name": "list", "label": "list"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 1, "bbox": {"l": 72.075, "t": 665.615, "r": 216.657, "b": 659.225, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 44]}], "orig": "Nucleosides,  Nucleotides  &  Nucleic  Acids", "text": "Nucleosides,  Nucleotides  &  Nucleic  Acids"}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 1, "bbox": {"l": 72.005, "t": 656.655, "r": 212.443, "b": 650.266, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 45]}], "orig": "https://doi.org/10.1080/15257770.2023.2298408", "text": "https://doi.org/10.1080/15257770.2023.2298408"}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 72.0, "t": 615.934, "r": 395.408, "b": 572.071, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 138]}], "orig": "Prognostic  value  of  lncRNA  LINC01018  in  prostate cancer  by  regulating  miR-182-5p  (The  role  of LINC01018  in  prostate  cancer)", "text": "Prognostic  value  of  lncRNA  LINC01018  in  prostate cancer  by  regulating  miR-182-5p  (The  role  of LINC01018  in  prostate  cancer)", "level": 1}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 72.0, "t": 555.575, "r": 384.298, "b": 546.639, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 72]}, {"page_no": 1, "bbox": {"l": 72.0, "t": 555.575, "r": 384.298, "b": 546.639, "coord_origin": "BOTTOMLEFT"}, "charspan": [73, 206]}], "orig": "<PERSON><PERSON><PERSON>*, <PERSON><PERSON><PERSON>*,  <PERSON><PERSON>,  <PERSON><PERSON><PERSON>  and  Min  Cao department  of  urology  Andrology,  sinopharm  dongfeng  General  Hospital,  Hubei  university  of Medicine,  shiyan,  Hubei,  china", "text": "<PERSON><PERSON><PERSON>*, <PERSON><PERSON><PERSON>*,  <PERSON><PERSON>,  <PERSON><PERSON><PERSON>  and  Min  Cao department  of  urology  Andrology,  sinopharm  dongfeng  General  Hospital,  Hubei  university  of Medicine,  shiyan,  Hubei,  china"}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 84.075, "t": 494.553, "r": 121.275, "b": 488.235, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "ABSTRACT", "text": "ABSTRACT", "level": 1}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 84.0, "t": 485.564, "r": 325.938, "b": 367.905, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 742]}], "orig": "LncRNAs  are  abnormally  expressed  in  a  variety  of  cancers  and play  unique  roles  in  therapy.  Based  on  this,  the  prognostic value of lncRNA LINC01018 in prostate cancer was discussed in this  study.  LINC01018  was  underexpressed  in  prostate  cancer tissues  and  cells,  while  miR-182-5p  was  elevated  (*** p <  0.001). Overexpression  of  LINC01018  may  inhibit  the  progression  of prostate  cancer  by  targeting  miR-182-5p.  This  study  revealed that upregulated LINC01018 may prolong the overall survival of patients with prostate cancer (log-rank p =  0.042), and LINC01018 may become a prognostic biomarker for patients with prostate cancer, which  brings  a  new  direction  for  the  treatment  of patients.", "text": "LncRNAs  are  abnormally  expressed  in  a  variety  of  cancers  and play  unique  roles  in  therapy.  Based  on  this,  the  prognostic value of lncRNA LINC01018 in prostate cancer was discussed in this  study.  LINC01018  was  underexpressed  in  prostate  cancer tissues  and  cells,  while  miR-182-5p  was  elevated  (*** p <  0.001). Overexpression  of  LINC01018  may  inhibit  the  progression  of prostate  cancer  by  targeting  miR-182-5p.  This  study  revealed that upregulated LINC01018 may prolong the overall survival of patients with prostate cancer (log-rank p =  0.042), and LINC01018 may become a prognostic biomarker for patients with prostate cancer, which  brings  a  new  direction  for  the  treatment  of patients."}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 342.575, "t": 495.428, "r": 406.526, "b": 489.11, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 15]}], "orig": "ARTICLE HISTORY", "text": "ARTICLE HISTORY", "level": 1}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 342.5, "t": 486.765, "r": 424.824, "b": 452.957, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "Received  9  November 2022 Accepted  18  December 2023", "text": "Received  9  November 2022 Accepted  18  December 2023"}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 342.575, "t": 444.253, "r": 383.772, "b": 437.935, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "KEYWORDS", "text": "KEYWORDS", "level": 1}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 342.5, "t": 435.59, "r": 425.755, "b": 401.782, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 77]}], "orig": "Prostate  cancer;  lncRNA LINC01018; overexpression;  miR- 182-5p;  prognosis", "text": "Prostate  cancer;  lncRNA LINC01018; overexpression;  miR- 182-5p;  prognosis"}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 72.0, "t": 332.912, "r": 146.971, "b": 323.624, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "1.  Introduction", "text": "1.  Introduction", "level": 1}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 72.0, "t": 311.984, "r": 434.682, "b": 175.77300000000002, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 817]}], "orig": "Prostate  cancer  is  a  male  tumor  that  often  occurs  in  the  urinary  system, with  high  morbidity  and  mortality. [1-2] The  pathogenic  factors  of  prostate cancer  have  not  been  fully  elucidated,  there  are  studies  showing  that  the incidence  of  prostate  cancer  is  related  to  genetics,  environment,  sexual activity,  and  eating  habits. [3-4] Prostate  cancer  patients  can  be  effectively treated  with  excision  surgery,  radiation  therapy,  and  hormone  therapy  at this  stage.  However,  it  has  been  reported  that  60%  of  prostate  cancer patients  will  experience  biochemical  recurrence  after  radical  prostatectomy. [5] It  is  meaningful  to  monitor  the  prognostic  effect  of  patients  by molecular  means  to  study  more  reliable  prognostic  biomarkers.", "text": "Prostate  cancer  is  a  male  tumor  that  often  occurs  in  the  urinary  system, with  high  morbidity  and  mortality. [1-2] The  pathogenic  factors  of  prostate cancer  have  not  been  fully  elucidated,  there  are  studies  showing  that  the incidence  of  prostate  cancer  is  related  to  genetics,  environment,  sexual activity,  and  eating  habits. [3-4] Prostate  cancer  patients  can  be  effectively treated  with  excision  surgery,  radiation  therapy,  and  hormone  therapy  at this  stage.  However,  it  has  been  reported  that  60%  of  prostate  cancer patients  will  experience  biochemical  recurrence  after  radical  prostatectomy. [5] It  is  meaningful  to  monitor  the  prognostic  effect  of  patients  by molecular  means  to  study  more  reliable  prognostic  biomarkers."}, {"self_ref": "#/texts/12", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 72.0, "t": 171.98400000000004, "r": 434.657, "b": 105.77300000000002, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 392]}], "orig": "The application of molecular biology in diseases has found that lncRNAs are  involved  in  a  variety  of  cellular  metabolism,  cycle  regulation,  transcription  and  translation,  in  which  abnormal  expression  has  been  confirmed to be related to human tumors. [6-7] Moreover, lncRNAs are identified as  potential  marker  that  affects  the  diagnosis,  treatment  and  prognosis  of", "text": "The application of molecular biology in diseases has found that lncRNAs are  involved  in  a  variety  of  cellular  metabolism,  cycle  regulation,  transcription  and  translation,  in  which  abnormal  expression  has  been  confirmed to be related to human tumors. [6-7] Moreover, lncRNAs are identified as  potential  marker  that  affects  the  diagnosis,  treatment  and  prognosis  of"}, {"self_ref": "#/texts/13", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 72.0, "t": 90.14200000000005, "r": 433.573, "b": 65.327, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 225]}], "orig": "CONTACT Min  cao <EMAIL> department  of  urology  Andrology,  sinopharm  dongfeng General  Hospital,  Hubei  university  of  Medicine,  No.  16,  daling  Road,  Zhangwan  district,  shiyan,  Hubei,  442008, china.", "text": "CONTACT Min  cao <EMAIL> department  of  urology  Andrology,  sinopharm  dongfeng General  Hospital,  Hubei  university  of  Medicine,  No.  16,  daling  Road,  Zhangwan  district,  shiyan,  Hubei,  442008, china."}, {"self_ref": "#/texts/14", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 72.0, "t": 63.14200000000005, "r": 238.192, "b": 56.327, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 59]}], "orig": "*these  two  authors  contributed  equally  to  this  text.", "text": "*these  two  authors  contributed  equally  to  this  text."}, {"self_ref": "#/texts/15", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 1, "bbox": {"l": 72.0, "t": 52.61800000000005, "r": 170.693, "b": 46.653999999999996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 39]}], "orig": "©  2023 taylor  &  Francis  Group,  llc", "text": "©  2023 taylor  &  Francis  Group,  llc"}, {"self_ref": "#/texts/16", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 2, "bbox": {"l": 72.0, "t": 692.075, "r": 75.848, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "2", "text": "2"}, {"self_ref": "#/texts/17", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 2, "bbox": {"l": 98.477, "t": 692.075, "r": 141.242, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 13]}], "orig": "W. LUO ET AL.", "text": "W. LUO ET AL."}, {"self_ref": "#/texts/18", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 72.0, "t": 666.781, "r": 434.686, "b": 516.57, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 822]}], "orig": "the  disease.  For  example,  lncRNA  MEG3  as  down-regulated  RNA,  that affected  the  proliferation  level  and  apoptosis  of  prostate  cancer  cells. [8] LncRNA OIP5-AS1 promoted the development of prostate cancer through regulating  the  miR-128-3p/SLC7A11  axis. [9]   There  were  evidences  that lncRNA H19, as a predictive marker for neuroendocrine prostate cancer, [10] also  contributed  to  the  treatment  of  lung  cancer, [11] and  that  H19  competitively  bound  miR-140  to  promote  ovarian  cancer  cells  proliferation. [12] Likewise, LINC01018 has also been demonstrated as biomarker for different diseases, such as liver cancer, leukemia, and colorectal cancer. [13-15] However, LINC01018  has  not  been  reported  in  prostate  cancer  research,  which sparked  interest  in  further  research.", "text": "the  disease.  For  example,  lncRNA  MEG3  as  down-regulated  RNA,  that affected  the  proliferation  level  and  apoptosis  of  prostate  cancer  cells. [8] LncRNA OIP5-AS1 promoted the development of prostate cancer through regulating  the  miR-128-3p/SLC7A11  axis. [9]   There  were  evidences  that lncRNA H19, as a predictive marker for neuroendocrine prostate cancer, [10] also  contributed  to  the  treatment  of  lung  cancer, [11] and  that  H19  competitively  bound  miR-140  to  promote  ovarian  cancer  cells  proliferation. [12] Likewise, LINC01018 has also been demonstrated as biomarker for different diseases, such as liver cancer, leukemia, and colorectal cancer. [13-15] However, LINC01018  has  not  been  reported  in  prostate  cancer  research,  which sparked  interest  in  further  research."}, {"self_ref": "#/texts/19", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 72.0, "t": 512.781, "r": 434.643, "b": 390.569, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 624]}], "orig": "Evidence  has  shown  that  lncRNAs  act  as  endogenous  sponges  to  regulate  various  biological  processes  in  tumors  by  binding  to  miRNAs. [16] According  to  the  close  correlation  between  lncRNAs  and  miRNAs,  as well  as  the  association  between  lncRNAs  and  tumors,  it  is  speculated that  abnormal  expression  of  lncRNAs  will  interfere  with  the  normal expression and efficacy of miRNAs, and then have a positive or negative impact  on  tumor  progression. [17] Therefore,  the  role  and  mechanism  of LINC01018  regulating  miRNA  in  prostate  cancer  is  a  topic  worthy  of discussion.", "text": "Evidence  has  shown  that  lncRNAs  act  as  endogenous  sponges  to  regulate  various  biological  processes  in  tumors  by  binding  to  miRNAs. [16] According  to  the  close  correlation  between  lncRNAs  and  miRNAs,  as well  as  the  association  between  lncRNAs  and  tumors,  it  is  speculated that  abnormal  expression  of  lncRNAs  will  interfere  with  the  normal expression and efficacy of miRNAs, and then have a positive or negative impact  on  tumor  progression. [17] Therefore,  the  role  and  mechanism  of LINC01018  regulating  miRNA  in  prostate  cancer  is  a  topic  worthy  of discussion."}, {"self_ref": "#/texts/20", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 72.0, "t": 386.781, "r": 434.65, "b": 334.569, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 317]}], "orig": "The current study explored the expression of LINC01018 and its targeted factor  miR-182-5p  in  prostate  cancer  to  understand  its  mechanism  of action  and  biological  effect,  providing  a  theoretical  basis  for  the  clinical application  of  LINC01018  as  a  new  prognostic  factor  in  prostate  cancer.", "text": "The current study explored the expression of LINC01018 and its targeted factor  miR-182-5p  in  prostate  cancer  to  understand  its  mechanism  of action  and  biological  effect,  providing  a  theoretical  basis  for  the  clinical application  of  LINC01018  as  a  new  prognostic  factor  in  prostate  cancer."}, {"self_ref": "#/texts/21", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 72.0, "t": 302.709, "r": 200.888, "b": 293.42, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 27]}], "orig": "2.  Materials  and  methods", "text": "2.  Materials  and  methods", "level": 1}, {"self_ref": "#/texts/22", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 72.0, "t": 281.372, "r": 210.612, "b": 272.502, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 35]}], "orig": "2.1.  Patients  and  clinical  data", "text": "2.1.  Patients  and  clinical  data", "level": 1}, {"self_ref": "#/texts/23", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 72.0, "t": 260.781, "r": 434.636, "b": 138.56999999999994, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 705]}], "orig": "There  were  110  cases  of  prostate  cancer  patients  were  selected  as  subjects in  this  experiment,  all  of  them  were  from  Sinopharm  Dongfeng  General Hospital,  Hubei  University  of  Medicine.  All  patients  met  the  following criteria:  (1)  prostate  cancer  confirmed  by  three  experts;  (2)  no  other treatment  before  participating  in  the  study;  (3)  no  hypertension,  diabetes, cardiovascular  disease,  or  family  history.  The  required  cancer  and  normal tissues  were  collected  as  experimental  materials.  The  obtained  samples were sequentially washed with PBS, then rapidly treated with liquid nitrogen  and  finally  stored  in  a  refrigerator  at  -80 °C.", "text": "There  were  110  cases  of  prostate  cancer  patients  were  selected  as  subjects in  this  experiment,  all  of  them  were  from  Sinopharm  Dongfeng  General Hospital,  Hubei  University  of  Medicine.  All  patients  met  the  following criteria:  (1)  prostate  cancer  confirmed  by  three  experts;  (2)  no  other treatment  before  participating  in  the  study;  (3)  no  hypertension,  diabetes, cardiovascular  disease,  or  family  history.  The  required  cancer  and  normal tissues  were  collected  as  experimental  materials.  The  obtained  samples were sequentially washed with PBS, then rapidly treated with liquid nitrogen  and  finally  stored  in  a  refrigerator  at  -80 °C."}, {"self_ref": "#/texts/24", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 72.0, "t": 134.78099999999995, "r": 434.618, "b": 68.56999999999994, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 405]}], "orig": "The  included  subjects  were  observed  for  60 months  after  surgery  to obtain  real  and  effective  prognostic  information,  and  the  relevant  clinical characteristic  data  were  recorded  in  Table  1.  All  subjects  have  the  right to  know  and  participate  in  the  experiment  voluntarily.  In  addition,  the experiment  was  conducted  under  the  admission  of  the  Ethics  Committee", "text": "The  included  subjects  were  observed  for  60 months  after  surgery  to obtain  real  and  effective  prognostic  information,  and  the  relevant  clinical characteristic  data  were  recorded  in  Table  1.  All  subjects  have  the  right to  know  and  participate  in  the  experiment  voluntarily.  In  addition,  the experiment  was  conducted  under  the  admission  of  the  Ethics  Committee"}, {"self_ref": "#/texts/25", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 3, "bbox": {"l": 252.303, "t": 692.075, "r": 405.183, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "NUCLEOSIDES, NUCLEOTIDES & NUCLEIC ACIDS", "text": "NUCLEOSIDES, NUCLEOTIDES & NUCLEIC ACIDS"}, {"self_ref": "#/texts/26", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 3, "bbox": {"l": 428.153, "t": 692.075, "r": 432.0, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "3", "text": "3"}, {"self_ref": "#/texts/27", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 3, "bbox": {"l": 72.0, "t": 666.933, "r": 433.59, "b": 647.84, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 110]}], "orig": "Table 1. clinical characteristics of prostate cancer patients and their association with liNc01018 expression.", "text": "Table 1. clinical characteristics of prostate cancer patients and their association with liNc01018 expression."}, {"self_ref": "#/texts/28", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 72.0, "t": 462.312, "r": 434.525, "b": 438.101, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 123]}], "orig": "of  Sinopharm  Dongfeng  General  Hospital,  Hubei  University  of  Medicine and  followed  the  Declaration  of  Helsinki.", "text": "of  Sinopharm  Dongfeng  General  Hospital,  Hubei  University  of  Medicine and  followed  the  Declaration  of  Helsinki."}, {"self_ref": "#/texts/29", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 3, "bbox": {"l": 72.0, "t": 411.52, "r": 224.46, "b": 402.651, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 38]}], "orig": "2.2.  Cell  culture  and  transfection", "text": "2.2.  Cell  culture  and  transfection", "level": 1}, {"self_ref": "#/texts/30", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 72.0, "t": 390.93, "r": 434.697, "b": 322.797, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 374]}], "orig": "Prostate  cancer  cell  lines  (DU145,  PC3,  LNCaP,  and  22RV1)  and  control prostate  epithelial  cell  line  RWPE-1  were  purchased  from  American  Type Culture  Collection  (ATCC;  Manassas,  USA)  and  supplemented  with  10% fetal  bovine  serum  (FBS;  Gibco,  USA)  in  Dulbecco's  Modified  Eagle's medium  (DMEM;  Invitrogen,  USA)  at  37 °C  with  5%  CO . 2", "text": "Prostate  cancer  cell  lines  (DU145,  PC3,  LNCaP,  and  22RV1)  and  control prostate  epithelial  cell  line  RWPE-1  were  purchased  from  American  Type Culture  Collection  (ATCC;  Manassas,  USA)  and  supplemented  with  10% fetal  bovine  serum  (FBS;  Gibco,  USA)  in  Dulbecco's  Modified  Eagle's medium  (DMEM;  Invitrogen,  USA)  at  37 °C  with  5%  CO . 2"}, {"self_ref": "#/texts/31", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 3, "bbox": {"l": 72.0, "t": 295.372, "r": 167.938, "b": 286.502, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 24]}], "orig": "2.3.  Cell  transfection", "text": "2.3.  Cell  transfection", "level": 1}, {"self_ref": "#/texts/32", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 72.0, "t": 274.781, "r": 434.557, "b": 236.56899999999996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 226]}], "orig": "The  plasmid  pcDNA3.1  was  used  as  a  vector  to  construct  overexpressing LINC01018  (pcDNA3.1-LINC01018),  and  the  recombinant  plasmid  was transfected into PC3 or LNCaP cells by Lipofectamine 2000 (Invitrogen, USA).", "text": "The  plasmid  pcDNA3.1  was  used  as  a  vector  to  construct  overexpressing LINC01018  (pcDNA3.1-LINC01018),  and  the  recombinant  plasmid  was transfected into PC3 or LNCaP cells by Lipofectamine 2000 (Invitrogen, USA)."}, {"self_ref": "#/texts/33", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 3, "bbox": {"l": 72.0, "t": 211.372, "r": 370.662, "b": 202.50199999999995, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 69]}], "orig": "2.4.  Real-time  quantitative  polymerase  chain  reaction  (RT-qPCR)", "text": "2.4.  Real-time  quantitative  polymerase  chain  reaction  (RT-qPCR)", "level": 1}, {"self_ref": "#/texts/34", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 72.0, "t": 190.78099999999995, "r": 434.656, "b": 68.56999999999994, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 682]}], "orig": "Total  RNA  was  isolated  with  TRIZOL  reagent  (Beyotime),  and  then  RNA  was reverse  transcribed  according  to  M-MLV  Reverse  Transcriptase  kit  (Takara, Japan).  The  RT-qPCR reaction system was configured by SYBR Green Mix kit (Takara, Japan), and amplification experiments were performed on an ABI 7500 system  (Applied  Biosystems).  GAPDH  was  used  as  a  normalized  control  for LINC01018,  and  U6  for  miR-182-5p.  The  data  obtained  from  the  experiment were  calculated  by  2 -ΔΔCt method.  The  primer  sequences  are  as  follows: LINC01018 F-5 -GTAGAAGACGGTGAATGAGC-3 , ′ ′ R-5 -AGTTTG ′ GAAGGAAGGAGGTG-3; miR-182-5p F-5-GTAATCGTTCTATGGTTTGA-3, ′ ′ ′", "text": "Total  RNA  was  isolated  with  TRIZOL  reagent  (Beyotime),  and  then  RNA  was reverse  transcribed  according  to  M-MLV  Reverse  Transcriptase  kit  (Takara, Japan).  The  RT-qPCR reaction system was configured by SYBR Green Mix kit (Takara, Japan), and amplification experiments were performed on an ABI 7500 system  (Applied  Biosystems).  GAPDH  was  used  as  a  normalized  control  for LINC01018,  and  U6  for  miR-182-5p.  The  data  obtained  from  the  experiment were  calculated  by  2 -ΔΔCt method.  The  primer  sequences  are  as  follows: LINC01018 F-5 -GTAGAAGACGGTGAATGAGC-3 , ′ ′ R-5 -AGTTTG ′ GAAGGAAGGAGGTG-3; miR-182-5p F-5-GTAATCGTTCTATGGTTTGA-3, ′ ′ ′"}, {"self_ref": "#/texts/35", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 4, "bbox": {"l": 72.0, "t": 692.075, "r": 141.242, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 15]}], "orig": "4 W. LUO ET AL.", "text": "4 W. LUO ET AL.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/36", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 72.0, "t": 666.908, "r": 434.611, "b": 628.57, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 170]}], "orig": "R-5 -CCATAGAACGATTACAGT-3 ; ′ ′ GAPDH F-5 -GAAGGTGAAG ′ GTCGGAGTC-3 , ′ R-5 -GAAGATGGTGATGGGATTC-3 ; ′ ′ U6 F-5 -′ GCTTGCTTCGGCAGCACA-3, R-5-CATGTCATCCTTGCTCAGGG-3. ′ ′ ′", "text": "R-5 -CCATAGAACGATTACAGT-3 ; ′ ′ GAPDH F-5 -GAAGGTGAAG ′ GTCGGAGTC-3 , ′ R-5 -GAAGATGGTGATGGGATTC-3 ; ′ ′ U6 F-5 -′ GCTTGCTTCGGCAGCACA-3, R-5-CATGTCATCCTTGCTCAGGG-3. ′ ′ ′"}, {"self_ref": "#/texts/37", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 4, "bbox": {"l": 72.0, "t": 607.372, "r": 234.867, "b": 598.502, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 41]}], "orig": "2.5.  Cell  counting  kit  (CCK)-8  assay", "text": "2.5.  Cell  counting  kit  (CCK)-8  assay", "level": 1}, {"self_ref": "#/texts/38", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 72.0, "t": 586.781, "r": 434.525, "b": 506.569, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 431]}], "orig": "CCK-8  method  (Sigma-Aldrich,  USA)  was  used  to  evaluate  the  proliferation of prostate cancer cells LNCaP and PC3. The transfected cells (5 × 10 3 cells/well)  were  cultured  in  96-well  plates,  and  CCK-8  solution  was  added to  the  plates  at  0,  24,  48,  and  72 h,  and  then  incubated  for  2 h,  the  absorbance  was  measured  using  fishbrand™  accukan™  GO  UV/Visible  plate spectrophotometer  at  450 nm.", "text": "CCK-8  method  (Sigma-Aldrich,  USA)  was  used  to  evaluate  the  proliferation of prostate cancer cells LNCaP and PC3. The transfected cells (5 × 10 3 cells/well)  were  cultured  in  96-well  plates,  and  CCK-8  solution  was  added to  the  plates  at  0,  24,  48,  and  72 h,  and  then  incubated  for  2 h,  the  absorbance  was  measured  using  fishbrand™  accukan™  GO  UV/Visible  plate spectrophotometer  at  450 nm."}, {"self_ref": "#/texts/39", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 4, "bbox": {"l": 72.0, "t": 485.372, "r": 164.695, "b": 476.502, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 22]}], "orig": "2.6.  Transwell  assay", "text": "2.6.  Transwell  assay", "level": 1}, {"self_ref": "#/texts/40", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 72.0, "t": 464.781, "r": 434.61, "b": 328.569, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 804]}], "orig": "The  migration  levels  and  invasion  levels  of  LNCaP  and  PC3  cells  were estimated by Transwell method (Corning, USA). In the migration assay, the cell  suspension  (5 × 10   cells/well)  was  loaded  into  the  upper  chambers  of 4 24-well Transwell and the lower chambers were filled with DMEM medium containing  10%  FBS  as  attractants.  Similarly,  in  the  invasion  assay,  except that  <PERSON><PERSON><PERSON>  (Becton  Dickinson,  USA)  was  applied  to  the  surface  of  the upper  chambers  in  advance,  the  rest  of  the  operation  steps  were  the  same as  the  above.  After  48 h  of  incubation  at  37 °C  and  removed  noninvasive cells  with cotton, all cells were stained with crystal violet and counted under a microscope. To improve accuracy, the experiment was repeated three times.", "text": "The  migration  levels  and  invasion  levels  of  LNCaP  and  PC3  cells  were estimated by Transwell method (Corning, USA). In the migration assay, the cell  suspension  (5 × 10   cells/well)  was  loaded  into  the  upper  chambers  of 4 24-well Transwell and the lower chambers were filled with DMEM medium containing  10%  FBS  as  attractants.  Similarly,  in  the  invasion  assay,  except that  <PERSON><PERSON><PERSON>  (Becton  Dickinson,  USA)  was  applied  to  the  surface  of  the upper  chambers  in  advance,  the  rest  of  the  operation  steps  were  the  same as  the  above.  After  48 h  of  incubation  at  37 °C  and  removed  noninvasive cells  with cotton, all cells were stained with crystal violet and counted under a microscope. To improve accuracy, the experiment was repeated three times."}, {"self_ref": "#/texts/41", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 4, "bbox": {"l": 72.0, "t": 307.372, "r": 208.962, "b": 298.502, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 33]}], "orig": "2.7.  Luciferase  reporter  assay", "text": "2.7.  Luciferase  reporter  assay", "level": 1}, {"self_ref": "#/texts/42", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 72.0, "t": 286.781, "r": 434.736, "b": 164.56899999999996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 650]}], "orig": "The binding sites  between  LINC01018 and miR-182-5p were predicted by an online database, and WT-LINC01018 (wild type) and MUT-LINC01018 (mutant type) were constructed using a luciferase reporter vector (Promega, China).  Then  WT-LINC01018  or  MUT-LINC01018  were  co-transfected into  PC3  cells  with  miR-182-5p  mimic,  miR-182-5p  inhibitor,  mimic  negative  control  (NC)  or  inhibitor  NC  with  the  assistance  of  Lipofectamine 2000.  After  transfection  and  incubation  for  48 h,  the  luciferase  activity  in PC3  cells  was  detected  and  recorded  using  the  dual-luciferase  reporter gene  detection  kit  (Promega,  China).", "text": "The binding sites  between  LINC01018 and miR-182-5p were predicted by an online database, and WT-LINC01018 (wild type) and MUT-LINC01018 (mutant type) were constructed using a luciferase reporter vector (Promega, China).  Then  WT-LINC01018  or  MUT-LINC01018  were  co-transfected into  PC3  cells  with  miR-182-5p  mimic,  miR-182-5p  inhibitor,  mimic  negative  control  (NC)  or  inhibitor  NC  with  the  assistance  of  Lipofectamine 2000.  After  transfection  and  incubation  for  48 h,  the  luciferase  activity  in PC3  cells  was  detected  and  recorded  using  the  dual-luciferase  reporter gene  detection  kit  (Promega,  China)."}, {"self_ref": "#/texts/43", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 4, "bbox": {"l": 72.0, "t": 143.37200000000007, "r": 265.872, "b": 134.50199999999995, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 44]}], "orig": "2.8.  RNA  immunoprecipitation  (RIP)  assay", "text": "2.8.  RNA  immunoprecipitation  (RIP)  assay", "level": 1}, {"self_ref": "#/texts/44", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 72.0, "t": 122.78099999999995, "r": 434.526, "b": 70.56999999999994, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 246]}], "orig": "Prostate  PC3  cells  were  treated  with  magnetic  beads  of  human  immunoglobulin G (IgG) antibody and Argonaute 2 (Ago2) antibody, and RIP Kit of  Sigma-Aldrich  company  was  selected  to  enrich  Ago2  binding  RNA  for RIP  determination.", "text": "Prostate  PC3  cells  were  treated  with  magnetic  beads  of  human  immunoglobulin G (IgG) antibody and Argonaute 2 (Ago2) antibody, and RIP Kit of  Sigma-Aldrich  company  was  selected  to  enrich  Ago2  binding  RNA  for RIP  determination."}, {"self_ref": "#/texts/45", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 5, "bbox": {"l": 252.303, "t": 692.075, "r": 405.183, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "NUCLEOSIDES, NUCLEOTIDES & NUCLEIC ACIDS", "text": "NUCLEOSIDES, NUCLEOTIDES & NUCLEIC ACIDS"}, {"self_ref": "#/texts/46", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 5, "bbox": {"l": 428.153, "t": 692.075, "r": 432.0, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "5", "text": "5"}, {"self_ref": "#/texts/47", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 5, "bbox": {"l": 72.0, "t": 666.857, "r": 180.487, "b": 657.987, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 27]}], "orig": "2.9.  Statistical  analyses", "text": "2.9.  Statistical  analyses", "level": 1}, {"self_ref": "#/texts/48", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 72.0, "t": 646.267, "r": 434.689, "b": 538.055, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 612]}], "orig": "Experimental  data  were  calculated  as  mean ± standard  deviation  by  SPSS software  (version  20.0;  IBM,  USA)  and  GraphPad  software  (version  7.0; La  Jolla,  USA).  Student's  t-test  and  one-way  ANOVA  was  used  to  determine  the  differences  between  two  or  three  groups.  <PERSON><PERSON><PERSON><PERSON>  curve analysis  was  conducted  to  evaluate  the  prognostic  value  of  abnormal LINC01018  expression  in  prostate  cancer.  Pearson  analysis  was  used  to evaluate the correlation of LINC01018 with clinical parameters in prostate cancer  patients. p <  0.05  indicated  statistical  significance.", "text": "Experimental  data  were  calculated  as  mean ± standard  deviation  by  SPSS software  (version  20.0;  IBM,  USA)  and  GraphPad  software  (version  7.0; La  Jolla,  USA).  Student's  t-test  and  one-way  ANOVA  was  used  to  determine  the  differences  between  two  or  three  groups.  <PERSON><PERSON><PERSON><PERSON>  curve analysis  was  conducted  to  evaluate  the  prognostic  value  of  abnormal LINC01018  expression  in  prostate  cancer.  Pearson  analysis  was  used  to evaluate the correlation of LINC01018 with clinical parameters in prostate cancer  patients. p <  0.05  indicated  statistical  significance."}, {"self_ref": "#/texts/49", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 5, "bbox": {"l": 72.0, "t": 514.194, "r": 120.319, "b": 504.906, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "3.  Results", "text": "3.  Results", "level": 1}, {"self_ref": "#/texts/50", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 5, "bbox": {"l": 72.0, "t": 492.857, "r": 276.364, "b": 483.987, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 49]}], "orig": "3.1.  Relative  expression  levels  of  LINC01018", "text": "3.1.  Relative  expression  levels  of  LINC01018", "level": 1}, {"self_ref": "#/texts/51", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 72.0, "t": 472.266, "r": 434.643, "b": 406.055, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 348]}], "orig": "The  LINC01018  expression  in  prostate  cancer  tissues  and  cell  samples  in this  experiment was determined by RT-qPCR, and normal tissues and cells were  used  as  the  control  group.  As  a  result,  LINC01018  was  decreased  in prostate  cancer  tissues  (Figure  1A)  and  similarly  decreased  in  prostate cancer  cells  (Figure  1B).", "text": "The  LINC01018  expression  in  prostate  cancer  tissues  and  cell  samples  in this  experiment was determined by RT-qPCR, and normal tissues and cells were  used  as  the  control  group.  As  a  result,  LINC01018  was  decreased  in prostate  cancer  tissues  (Figure  1A)  and  similarly  decreased  in  prostate cancer  cells  (Figure  1B)."}, {"self_ref": "#/texts/52", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 5, "bbox": {"l": 72.0, "t": 380.857, "r": 432.55, "b": 357.987, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 94]}], "orig": "3.2.  Correlation  between  LINC01018  expression  and  clinical  characteristics  of patients", "text": "3.2.  Correlation  between  LINC01018  expression  and  clinical  characteristics  of patients", "level": 1}, {"self_ref": "#/texts/53", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 72.0, "t": 346.266, "r": 434.7, "b": 280.055, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 388]}], "orig": "All  prostate  cancer  patients  participating  in  the  experiment  were  divided into  low  expression  of  LINC01018  and  high  expression  of  LINC01018, which  were  grouped  according  to  the  average  expression  of  LINC01018 in  prostate  cancer  tissue.  Table  1  presented  the  relationship  between LINC01018 expression and clinical parameters of prostate cancer patients.", "text": "All  prostate  cancer  patients  participating  in  the  experiment  were  divided into  low  expression  of  LINC01018  and  high  expression  of  LINC01018, which  were  grouped  according  to  the  average  expression  of  LINC01018 in  prostate  cancer  tissue.  Table  1  presented  the  relationship  between LINC01018 expression and clinical parameters of prostate cancer patients."}, {"self_ref": "#/texts/54", "parent": {"$ref": "#/pictures/6"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 5, "bbox": {"l": 72.0, "t": 110.75300000000004, "r": 433.899, "b": 69.65999999999997, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 287]}], "orig": "Figure  1. Rt-qPcR  method  was  chosen  to  detect  the  expression  level  of  liNc01018.  (A) liNc01018 expression was decreased in prostate cancer tissues compared to normal tissues. (B) downregulation of liNc01018 in prostate cancer cells du145, Pc3, lNcaP and 22RV1. *** p < 0.001.", "text": "Figure  1. Rt-qPcR  method  was  chosen  to  detect  the  expression  level  of  liNc01018.  (A) liNc01018 expression was decreased in prostate cancer tissues compared to normal tissues. (B) downregulation of liNc01018 in prostate cancer cells du145, Pc3, lNcaP and 22RV1. *** p < 0.001."}, {"self_ref": "#/texts/55", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 72.0, "t": 692.075, "r": 141.242, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 15]}], "orig": "6 W. LUO ET AL.", "text": "6 W. LUO ET AL.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/56", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 6, "bbox": {"l": 72.0, "t": 666.781, "r": 434.677, "b": 614.57, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 259]}], "orig": "Among  many  characteristic  parameters,  LINC01018  expression  was  significantly correlated with lymph node metastasis and TNM stage ( p =  0.007, p =  0.002),  while  there  was  no  correlation  with  patients'  age,  gleason  score, or  differentiation.", "text": "Among  many  characteristic  parameters,  LINC01018  expression  was  significantly correlated with lymph node metastasis and TNM stage ( p =  0.007, p =  0.002),  while  there  was  no  correlation  with  patients'  age,  gleason  score, or  differentiation."}, {"self_ref": "#/texts/57", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 6, "bbox": {"l": 72.0, "t": 589.372, "r": 374.694, "b": 580.502, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 72]}], "orig": "3.3.  Prognostic  value  of  LINC01018  expression  in  prostate  cancer", "text": "3.3.  Prognostic  value  of  LINC01018  expression  in  prostate  cancer", "level": 1}, {"self_ref": "#/texts/58", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 6, "bbox": {"l": 72.0, "t": 568.781, "r": 434.521, "b": 502.569, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 396]}], "orig": "<PERSON><PERSON><PERSON><PERSON> survival curves and multivariate Cox regression linear analysis  were  used  to  interpret  the  prognostic  value  of  LINC01018  expression in  prostate  cancer.  In  Figure  2,  patients  with  high-expressing  LINC01018 prostate  cancer  were  found  to  have  higher  overall  survival  than  low-expressing  LINC01018  at  60 months  of  follow-up  (log-rank  test p =  0.042).", "text": "<PERSON><PERSON><PERSON><PERSON> survival curves and multivariate Cox regression linear analysis  were  used  to  interpret  the  prognostic  value  of  LINC01018  expression in  prostate  cancer.  In  Figure  2,  patients  with  high-expressing  LINC01018 prostate  cancer  were  found  to  have  higher  overall  survival  than  low-expressing  LINC01018  at  60 months  of  follow-up  (log-rank  test p =  0.042)."}, {"self_ref": "#/texts/59", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 6, "bbox": {"l": 72.0, "t": 498.781, "r": 434.546, "b": 446.569, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 283]}], "orig": "Furthermore,  LINC01018  expression  (HR  =  3.467,  95%CI  =  1.09810.944, p =  0.034)  and  Lymph  node  metastasis  (HR  =  2.881,  95%CI  = 1.067-7.776, p =  0.037)  were  significantly  correlated  with  overall  survival in  prostate  cancer  patients  as  shown  in  Table  2.", "text": "Furthermore,  LINC01018  expression  (HR  =  3.467,  95%CI  =  1.09810.944, p =  0.034)  and  Lymph  node  metastasis  (HR  =  2.881,  95%CI  = 1.067-7.776, p =  0.037)  were  significantly  correlated  with  overall  survival in  prostate  cancer  patients  as  shown  in  Table  2."}, {"self_ref": "#/texts/60", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 6, "bbox": {"l": 72.0, "t": 419.39, "r": 387.021, "b": 410.52, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 77]}], "orig": "3.4.  Effects  of  overexpression  of  LINC01018  on  prostate  cancer  cells", "text": "3.4.  Effects  of  overexpression  of  LINC01018  on  prostate  cancer  cells", "level": 1}, {"self_ref": "#/texts/61", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 6, "bbox": {"l": 72.0, "t": 398.799, "r": 434.583, "b": 360.588, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 229]}], "orig": "The  recombinant  plasmid  was  constructed  with  pcDNA3.1  as  the  vector and  transfected  into  prostate  cancer  cells.  Transfection  results  of  overexpressed  LINC01018 in LNCaP and PC3 cells are shown in  Figure 3A and", "text": "The  recombinant  plasmid  was  constructed  with  pcDNA3.1  as  the  vector and  transfected  into  prostate  cancer  cells.  Transfection  results  of  overexpressed  LINC01018 in LNCaP and PC3 cells are shown in  Figure 3A and"}, {"self_ref": "#/texts/62", "parent": {"$ref": "#/pictures/7"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 6, "bbox": {"l": 72.0, "t": 99.75300000000004, "r": 433.873, "b": 69.65999999999997, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 256]}], "orig": "Figure  2. the  prognostic  value  of  liNc01018  expression  in  prostate  cancer  was  analyzed  by <PERSON><PERSON>  method.  Prostate  cancer  patients  with  high  expression  of  liNc01018  were  more favorable  for  survival  (log-rank  test p = 0.042).", "text": "Figure  2. the  prognostic  value  of  liNc01018  expression  in  prostate  cancer  was  analyzed  by <PERSON><PERSON>  method.  Prostate  cancer  patients  with  high  expression  of  liNc01018  were  more favorable  for  survival  (log-rank  test p = 0.042)."}, {"self_ref": "#/texts/63", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 7, "bbox": {"l": 252.303, "t": 692.075, "r": 405.183, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "NUCLEOSIDES, NUCLEOTIDES & NUCLEIC ACIDS", "text": "NUCLEOSIDES, NUCLEOTIDES & NUCLEIC ACIDS"}, {"self_ref": "#/texts/64", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 7, "bbox": {"l": 428.153, "t": 692.075, "r": 432.0, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "7", "text": "7"}, {"self_ref": "#/texts/65", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 7, "bbox": {"l": 72.0, "t": 666.933, "r": 386.218, "b": 658.84, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 93]}], "orig": "Table  2. Multivariate  cox  analyses  of  overall  survival  of  prostate  cancer  patients.", "text": "Table  2. Multivariate  cox  analyses  of  overall  survival  of  prostate  cancer  patients."}, {"self_ref": "#/texts/66", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 72.0, "t": 571.922, "r": 290.11, "b": 565.107, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 75]}], "orig": "Note:  data  are  analyzed  using  multivariate  cox  regression  analysis.", "text": "Note:  data  are  analyzed  using  multivariate  cox  regression  analysis."}, {"self_ref": "#/texts/67", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 7, "bbox": {"l": 72.0, "t": 283.003, "r": 433.884, "b": 241.90999999999997, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 420]}], "orig": "Figure 3. effects of overexpression of liNc01018 on prostate cancer cells. (A and B) transfection results  of  overexpressed  liNc01018  in  lNcaP  and  Pc3  cells.  (c  and  d)  the  proliferative  ability of  the  cells  was  verified  by  the  ccK-8  method. (e and F) the migratory ability and invasion level of  lNcaP  and  Pc3  cells  were  examined via transwell  assay.  *** p < 0.001,  ** p < 0.01,  * p < 0.05.", "text": "Figure 3. effects of overexpression of liNc01018 on prostate cancer cells. (A and B) transfection results  of  overexpressed  liNc01018  in  lNcaP  and  Pc3  cells.  (c  and  d)  the  proliferative  ability of  the  cells  was  verified  by  the  ccK-8  method. (e and F) the migratory ability and invasion level of  lNcaP  and  Pc3  cells  were  examined via transwell  assay.  *** p < 0.001,  ** p < 0.01,  * p < 0.05."}, {"self_ref": "#/texts/68", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 86.0, "t": 486.3333333333333, "r": 93.33333333333333, "b": 481.6666666666667, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "1.0", "text": "1.0"}, {"self_ref": "#/texts/69", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 207.33333333333334, "t": 487.0, "r": 214.66666666666666, "b": 481.6666666666667, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "1.0", "text": "1.0"}, {"self_ref": "#/texts/70", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 79.0, "t": 475.3333333333333, "r": 93.66666666666667, "b": 466.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "5 0.5", "text": "5 0.5"}, {"self_ref": "#/texts/71", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 207.33333333333334, "t": 471.0, "r": 214.66666666666666, "b": 466.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "0.5", "text": "0.5"}, {"self_ref": "#/texts/72", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 73.33333333333333, "t": 475.0, "r": 78.66666666666667, "b": 454.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/73", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 194.66666666666666, "t": 475.0, "r": 200.0, "b": 454.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/74", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 86.0, "t": 455.6666666666667, "r": 93.33333333333333, "b": 451.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "0.0", "text": "0.0"}, {"self_ref": "#/texts/75", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 207.33333333333334, "t": 455.6666666666667, "r": 214.66666666666666, "b": 451.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "0.0", "text": "0.0"}, {"self_ref": "#/texts/76", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 372.3333333333333, "t": 444.0, "r": 394.3333333333333, "b": 436.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "Time (h)", "text": "Time (h)"}, {"self_ref": "#/texts/77", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 76.66666666666667, "t": 408.33333333333337, "r": 83.33333333333333, "b": 401.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "D", "text": "D"}, {"self_ref": "#/texts/78", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 130.66666666666669, "t": 394.33333333333337, "r": 144.0, "b": 387.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "PC3", "text": "PC3"}, {"self_ref": "#/texts/79", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 206.0, "t": 384.33333333333337, "r": 214.66666666666666, "b": 379.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "600", "text": "600"}, {"self_ref": "#/texts/80", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 326.6666666666667, "t": 383.66666666666663, "r": 335.3333333333333, "b": 379.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "400", "text": "400"}, {"self_ref": "#/texts/81", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 110.0, "t": 379.66666666666663, "r": 127.33333333333333, "b": 375.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "control", "text": "control"}, {"self_ref": "#/texts/82", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 109.66666666666666, "t": 373.66666666666663, "r": 132.0, "b": 367.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "PcDNA3.1", "text": "PcDNA3.1"}, {"self_ref": "#/texts/83", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 326.6666666666667, "t": 368.33333333333337, "r": 335.3333333333333, "b": 363.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "300", "text": "300"}, {"self_ref": "#/texts/84", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 86.0, "t": 363.66666666666663, "r": 93.33333333333333, "b": 359.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "1.0", "text": "1.0"}, {"self_ref": "#/texts/85", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 109.66666666666666, "t": 366.33333333333337, "r": 157.33333333333331, "b": 359.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 18]}], "orig": "PcDNA3.1-LINCO1018", "text": "PcDNA3.1-LINCO1018"}, {"self_ref": "#/texts/86", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 206.0, "t": 363.66666666666663, "r": 214.66666666666666, "b": 359.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "400", "text": "400"}, {"self_ref": "#/texts/87", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 320.0, "t": 367.66666666666663, "r": 325.3333333333333, "b": 347.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/88", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 326.6666666666667, "t": 353.0, "r": 335.3333333333333, "b": 347.66666666666663, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "200", "text": "200"}, {"self_ref": "#/texts/89", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 79.33333333333333, "t": 335.66666666666663, "r": 84.66666666666667, "b": 327.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "8", "text": "8"}, {"self_ref": "#/texts/90", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 231.0, "t": 301.0, "r": 278.33333333333337, "b": 294.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 18]}], "orig": "PcDNA3.1-LINCO1018", "text": "PcDNA3.1-LINCO1018"}, {"self_ref": "#/texts/91", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 351.6666666666667, "t": 301.0, "r": 399.3333333333333, "b": 294.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 18]}], "orig": "PcDNA3.1-LINCO1018", "text": "PcDNA3.1-LINCO1018"}, {"self_ref": "#/texts/92", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 128.12431009848405, "t": 453.5558024770547, "r": 138.87568990151595, "b": 429.11086418961196, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/93", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 145.47425220594567, "t": 446.68535291698953, "r": 172.8590811273877, "b": 431.64798041634384, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "1-LINCO1018", "text": "1-LINCO1018"}, {"self_ref": "#/texts/94", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 249.64201203668824, "t": 452.9242325654699, "r": 260.6913212966451, "b": 429.40910076786344, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/95", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 267.5383903595193, "t": 447.01245750021525, "r": 294.1282763071473, "b": 431.3208758331181, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "1-LINCO1018", "text": "1-LINCO1018"}, {"self_ref": "#/texts/96", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 99.7684570961176, "t": 446.0415008261842, "r": 115.89820957054906, "b": 438.29183250714914, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "control", "text": "control"}, {"self_ref": "#/texts/97", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 220.10179042945094, "t": 446.37483415951755, "r": 238.2315429038824, "b": 438.62516584048245, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "control", "text": "control"}, {"self_ref": "#/texts/98", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 137.61209573701876, "t": 433.6055872907649, "r": 146.72123759631455, "b": 411.3944127092351, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/99", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 253.3766592713838, "t": 426.7096422622332, "r": 274.95667406194957, "b": 417.6236910711001, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "pcDNA3.1", "text": "pcDNA3.1"}, {"self_ref": "#/texts/100", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 72.0, "t": 222.745, "r": 434.669, "b": 156.53300000000002, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 322]}], "orig": "3B. Upregulation of LINC01018 markedly suppressed the growth of LNCaP and  PC3  cells  by  CCK-8  assay  (Figure  3C  and  3D .  Likewise,  Transwell ) assay  confirmed  that  the  migration  and  invasion  levels  of  prostate  cancer cells  transfected  with  pcDNA3.1-LINC01018  were  decreased  (Figure  3E and  3F . )", "text": "3B. Upregulation of LINC01018 markedly suppressed the growth of LNCaP and  PC3  cells  by  CCK-8  assay  (Figure  3C  and  3D .  Likewise,  Transwell ) assay  confirmed  that  the  migration  and  invasion  levels  of  prostate  cancer cells  transfected  with  pcDNA3.1-LINC01018  were  decreased  (Figure  3E and  3F . )"}, {"self_ref": "#/texts/101", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 7, "bbox": {"l": 72.0, "t": 131.33500000000004, "r": 279.871, "b": 122.46500000000003, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 50]}], "orig": "3.5.  Relative  expression  levels  of  miR-182-5p", "text": "3.5.  Relative  expression  levels  of  miR-182-5p", "level": 1}, {"self_ref": "#/texts/102", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 72.0, "t": 110.745, "r": 434.696, "b": 72.53300000000002, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 241]}], "orig": "The  possible  target  genes  of  LINC01018  were  predicted  by  the  database to  understand  its  mechanism  of  action,  and  it  was  found  that  LINC01018 may  play  a  role  in  targeting  miR-182-5p.  Therefore,  the  present  study", "text": "The  possible  target  genes  of  LINC01018  were  predicted  by  the  database to  understand  its  mechanism  of  action,  and  it  was  found  that  LINC01018 may  play  a  role  in  targeting  miR-182-5p.  Therefore,  the  present  study"}, {"self_ref": "#/texts/103", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 72.0, "t": 692.075, "r": 75.848, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "8", "text": "8"}, {"self_ref": "#/texts/104", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 98.477, "t": 692.075, "r": 141.242, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 13]}], "orig": "W. LUO ET AL.", "text": "W. LUO ET AL."}, {"self_ref": "#/texts/105", "parent": {"$ref": "#/pictures/10"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 8, "bbox": {"l": 72.0, "t": 515.058, "r": 433.864, "b": 484.965, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 280]}], "orig": "Figure  4. expression  of  miR-182-5p  in  prostate  cancer  tissues  and  cells.  (A)  miR-182-5p  level was  increased  in  prostate  cancer  tissues  compared  with  normal  tissues.  (B)  miR-182-5p  was significantly  upregulated  in  prostate  cancer  cells.  *** p < 0.001.", "text": "Figure  4. expression  of  miR-182-5p  in  prostate  cancer  tissues  and  cells.  (A)  miR-182-5p  level was  increased  in  prostate  cancer  tissues  compared  with  normal  tissues.  (B)  miR-182-5p  was significantly  upregulated  in  prostate  cancer  cells.  *** p < 0.001."}, {"self_ref": "#/texts/106", "parent": {"$ref": "#/pictures/10"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 84.66666666666667, "t": 667.0, "r": 91.33333333333333, "b": 659.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "A", "text": "A"}, {"self_ref": "#/texts/107", "parent": {"$ref": "#/pictures/10"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 268.0, "t": 667.0, "r": 274.66666666666663, "b": 659.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "B", "text": "B"}, {"self_ref": "#/texts/108", "parent": {"$ref": "#/pictures/10"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 342.9939938726704, "t": 545.5933932599373, "r": 362.6726727939963, "b": 543.4066067400627, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "PC3", "text": "PC3"}, {"self_ref": "#/texts/109", "parent": {"$ref": "#/pictures/11"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 8, "bbox": {"l": 72.0, "t": 342.173, "r": 433.884, "b": 301.08, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 362]}], "orig": "Figure  5. miR-182-5p  acted  as  the  target  of  liNc01018.  (A)  liNc01018  and  miR-182-5p  level were  negatively  correlated  (r = -0.5482, p < 0.0001).  (B)  miR-182-5p  inhibitor  markedly  increased the  luciferase  activity  of  Wt-liNc01018.  (c)  the  expression  of  liNc01018  and  miR-182-5p  were Ago2  enrichment  in  Pc3  cells.  *** p < 0.001.", "text": "Figure  5. miR-182-5p  acted  as  the  target  of  liNc01018.  (A)  liNc01018  and  miR-182-5p  level were  negatively  correlated  (r = -0.5482, p < 0.0001).  (B)  miR-182-5p  inhibitor  markedly  increased the  luciferase  activity  of  Wt-liNc01018.  (c)  the  expression  of  liNc01018  and  miR-182-5p  were Ago2  enrichment  in  Pc3  cells.  *** p < 0.001."}, {"self_ref": "#/texts/110", "parent": {"$ref": "#/pictures/11"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 73.33333333333333, "t": 459.6666666666667, "r": 81.33333333333333, "b": 450.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "A", "text": "A"}, {"self_ref": "#/texts/111", "parent": {"$ref": "#/pictures/11"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 201.0, "t": 458.6666666666667, "r": 207.66666666666666, "b": 450.6666666666667, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "B", "text": "B"}, {"self_ref": "#/texts/112", "parent": {"$ref": "#/pictures/11"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 323.6666666666667, "t": 402.0, "r": 329.0, "b": 382.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/113", "parent": {"$ref": "#/pictures/11"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 161.66666666666669, "t": 373.3333333333333, "r": 169.0, "b": 368.6666666666667, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "0.6", "text": "0.6"}, {"self_ref": "#/texts/114", "parent": {"$ref": "#/pictures/11"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 284.33333333333337, "t": 367.3333333333333, "r": 311.0, "b": 361.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 12]}], "orig": "inhibltor NC", "text": "inhibltor NC"}, {"self_ref": "#/texts/115", "parent": {"$ref": "#/pictures/11"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 357.0, "t": 364.6666666666667, "r": 365.6666666666667, "b": 359.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "IgG", "text": "IgG"}, {"self_ref": "#/texts/116", "parent": {"$ref": "#/pictures/11"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 402.3333333333333, "t": 365.3333333333333, "r": 414.3333333333333, "b": 358.6666666666667, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "Ago2", "text": "Ago2"}, {"self_ref": "#/texts/117", "parent": {"$ref": "#/pictures/11"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 222.0, "t": 360.0, "r": 260.33333333333337, "b": 353.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "miR-182-5p mimic", "text": "miR-182-5p mimic"}, {"self_ref": "#/texts/118", "parent": {"$ref": "#/pictures/11"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 275.66666666666663, "t": 358.6666666666667, "r": 319.66666666666663, "b": 353.3333333333333, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 20]}], "orig": "miR-182-5p inhibitor", "text": "miR-182-5p inhibitor"}, {"self_ref": "#/texts/119", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 72.0, "t": 275.896, "r": 434.71, "b": 223.68399999999997, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 290]}], "orig": "measured  the  expression  of  miR-182-5p  in  prostate  cancer  tissues  and cells.  The  expression  of  miR-182-5p  was  increased  in  prostate  cancer  tissues (Figure 4A). In prostate cancer cells, miR-182-5p was also significantly higher  than  that  in  RAWPE1  cells  (Figure  4B).", "text": "measured  the  expression  of  miR-182-5p  in  prostate  cancer  tissues  and cells.  The  expression  of  miR-182-5p  was  increased  in  prostate  cancer  tissues (Figure 4A). In prostate cancer cells, miR-182-5p was also significantly higher  than  that  in  RAWPE1  cells  (Figure  4B)."}, {"self_ref": "#/texts/120", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 8, "bbox": {"l": 72.0, "t": 198.48699999999997, "r": 241.02, "b": 189.61699999999996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 37]}], "orig": "3.6.  LINC01018  targeted  miR-182-5p", "text": "3.6.  LINC01018  targeted  miR-182-5p", "level": 1}, {"self_ref": "#/texts/121", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 72.0, "t": 177.89599999999996, "r": 434.715, "b": 69.68499999999995, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 553]}], "orig": "The targeting effect of LINC01018 on miR-182-5p was further investigated. Figure 5A showed that the expression of LINC01018 and miR-182-5p was negatively  correlated  (r = -0.5482, p <  0.0001).  The  luciferase  activity  assay suggested that abnormal expression of miR-182-5p has no significant effect on  MUT-LINC01018,  while  miR-182-5p  inhibitor  markedly  increased  the luciferase  activity  of  WT-LINC01018  (Figure  5B).  Moreover,  LINC01018 and  miR-182-5p  expression  in  RIP  assays  were  Ago2  enrichment  in  PC3 cells  (Figure  5C).", "text": "The targeting effect of LINC01018 on miR-182-5p was further investigated. Figure 5A showed that the expression of LINC01018 and miR-182-5p was negatively  correlated  (r = -0.5482, p <  0.0001).  The  luciferase  activity  assay suggested that abnormal expression of miR-182-5p has no significant effect on  MUT-LINC01018,  while  miR-182-5p  inhibitor  markedly  increased  the luciferase  activity  of  WT-LINC01018  (Figure  5B).  Moreover,  LINC01018 and  miR-182-5p  expression  in  RIP  assays  were  Ago2  enrichment  in  PC3 cells  (Figure  5C)."}, {"self_ref": "#/texts/122", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 9, "bbox": {"l": 252.303, "t": 692.075, "r": 405.183, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "NUCLEOSIDES, NUCLEOTIDES & NUCLEIC ACIDS", "text": "NUCLEOSIDES, NUCLEOTIDES & NUCLEIC ACIDS"}, {"self_ref": "#/texts/123", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 9, "bbox": {"l": 428.153, "t": 692.075, "r": 432.0, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "9", "text": "9"}, {"self_ref": "#/texts/124", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 9, "bbox": {"l": 72.0, "t": 666.819, "r": 137.182, "b": 657.531, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 14]}], "orig": "4.  Disc<PERSON><PERSON>", "text": "4.  Disc<PERSON><PERSON>", "level": 1}, {"self_ref": "#/texts/125", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 9, "bbox": {"l": 72.0, "t": 645.718, "r": 434.611, "b": 309.518, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1861]}], "orig": "The  global  cancer  statistics  report  shows  that  the  incidence  of  prostate cancer ranks second among male cancers. [18]  However, conventional clinical  treatment  methods  will  produce  certain  side  effects  and  sequelae, affecting  the  normal  life  of  prostate  cancer  patients.  Therefore,  there  is an  urgent  need  to  explore  new  therapeutic  and  prognostic  approaches. In  existing  prostate  cancer-related  studies,  the  expression  levels  of  many lncRNAs  are  associated  with  patient  prognosis.  <PERSON>  et  al.  revealed  a novel  molecular  mechanism  mediated  by  lncRNA  PCAT6,  a  prognostic factor  for  the  treatment  of  bone-metastatic  prostate  cancer. [19] <PERSON><PERSON>  and several  others  noticed  that  PCA3,  PCAT18,  HOTAIR,  and  CCAT2  were up-regulated in prostate cancer, which was expected to be specific markers  for  application. [20] Additionally,  <PERSON><PERSON>  and  colleagues  also  proposed that  RNA methyltransferase METTL1 mediated methylation to influence prostate  cancer  treatment  outcomes. [21] LINC01018  is  a  novel  lncRNA located  on  human  chromosome  5  (5p15.31). [22]   According  to  existing evidence,  LINC01018  can  not  only  affect  the  deterioration  of  glioma  by mediating  miR-942-5p/KNG1  axis, [23]   but  also  regulate  PDCD4 via targeting miR-499a-5p to slow the progression of acute myeloid leukemia. [14] In  addition,  the  LINC01018/miR-197-3p/GNA14  network  proposed  by <PERSON><PERSON>  et  al.  explained  the  prognostic  mechanism  in  hepatocellular  carcinoma. [24] This  study  found  that  LINC01018  expression  was  decreased  in prostate  tissues  and  cells,  and  low  expression  of  LINC01018  was  proven to  be  detrimental  to  patient  survival.  Therefore,  LINC01018  is  likely  to be an independent prognostic and curative biomarker for prostate cancer.", "text": "The  global  cancer  statistics  report  shows  that  the  incidence  of  prostate cancer ranks second among male cancers. [18]  However, conventional clinical  treatment  methods  will  produce  certain  side  effects  and  sequelae, affecting  the  normal  life  of  prostate  cancer  patients.  Therefore,  there  is an  urgent  need  to  explore  new  therapeutic  and  prognostic  approaches. In  existing  prostate  cancer-related  studies,  the  expression  levels  of  many lncRNAs  are  associated  with  patient  prognosis.  <PERSON>  et  al.  revealed  a novel  molecular  mechanism  mediated  by  lncRNA  PCAT6,  a  prognostic factor  for  the  treatment  of  bone-metastatic  prostate  cancer. [19] <PERSON><PERSON>  and several  others  noticed  that  PCA3,  PCAT18,  HOTAIR,  and  CCAT2  were up-regulated in prostate cancer, which was expected to be specific markers  for  application. [20] Additionally,  <PERSON><PERSON>  and  colleagues  also  proposed that  RNA methyltransferase METTL1 mediated methylation to influence prostate  cancer  treatment  outcomes. [21] LINC01018  is  a  novel  lncRNA located  on  human  chromosome  5  (5p15.31). [22]   According  to  existing evidence,  LINC01018  can  not  only  affect  the  deterioration  of  glioma  by mediating  miR-942-5p/KNG1  axis, [23]   but  also  regulate  PDCD4 via targeting miR-499a-5p to slow the progression of acute myeloid leukemia. [14] In  addition,  the  LINC01018/miR-197-3p/GNA14  network  proposed  by <PERSON><PERSON>  et  al.  explained  the  prognostic  mechanism  in  hepatocellular  carcinoma. [24] This  study  found  that  LINC01018  expression  was  decreased  in prostate  tissues  and  cells,  and  low  expression  of  LINC01018  was  proven to  be  detrimental  to  patient  survival.  Therefore,  LINC01018  is  likely  to be an independent prognostic and curative biomarker for prostate cancer."}, {"self_ref": "#/texts/126", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 9, "bbox": {"l": 72.0, "t": 305.556, "r": 434.735, "b": 68.56999999999994, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1319]}], "orig": "To further mechanistically understand the ability of LINC01018 to regulate prostate  cancer,  we  transfected  pcDNA3.1-LINC01018  in  selected  prostate cancer  cells  and  demonstrated  that  miR-182-5p  was  a  downstream  target miRNA  of  LINC01018  based  on  online  database,  luciferase  activity  assay and  RIP  assay,  and  the  highly  enriched  LINC01018  suppressed  cell  reproduction  and  mobile  level.  Non-coding  RNAs  (ncRNAs),  including  lncRNAs and circRNAs, are known to target and regulate miRNAs expression, thereby mediating tumor progression. [25] For  example, regulatory mechanisms including the circROBO1/miR-556-5p/PGK1 network were demonstrated to accelerate  prostate  cancer  progression  and  enzalutamide  resistance. [26-27] Highly expressed in tumors, miR-182-5p belongs to the polycistronic miR-183 family and is located on chromosome 7q32.2. [28]  The increased expression of miR182-5p in prostate cancer tissues and cells was quantified in this experiment, which  was  negatively  correlated  with  LINC01018.  miR-182-5p  was  up-regulated  in  triple-negative  breast  cancer  tissues  and  MDA-MB-231  cells,  and was dominated by the regulation of lncRNA, which was similar to the results of  this  experiment. [29] In  addition,  miR-182-5p  is  also  involved  in  the", "text": "To further mechanistically understand the ability of LINC01018 to regulate prostate  cancer,  we  transfected  pcDNA3.1-LINC01018  in  selected  prostate cancer  cells  and  demonstrated  that  miR-182-5p  was  a  downstream  target miRNA  of  LINC01018  based  on  online  database,  luciferase  activity  assay and  RIP  assay,  and  the  highly  enriched  LINC01018  suppressed  cell  reproduction  and  mobile  level.  Non-coding  RNAs  (ncRNAs),  including  lncRNAs and circRNAs, are known to target and regulate miRNAs expression, thereby mediating tumor progression. [25] For  example, regulatory mechanisms including the circROBO1/miR-556-5p/PGK1 network were demonstrated to accelerate  prostate  cancer  progression  and  enzalutamide  resistance. [26-27] Highly expressed in tumors, miR-182-5p belongs to the polycistronic miR-183 family and is located on chromosome 7q32.2. [28]  The increased expression of miR182-5p in prostate cancer tissues and cells was quantified in this experiment, which  was  negatively  correlated  with  LINC01018.  miR-182-5p  was  up-regulated  in  triple-negative  breast  cancer  tissues  and  MDA-MB-231  cells,  and was dominated by the regulation of lncRNA, which was similar to the results of  this  experiment. [29] In  addition,  miR-182-5p  is  also  involved  in  the"}, {"self_ref": "#/texts/127", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 10, "bbox": {"l": 102.325, "t": 692.075, "r": 145.09, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 13]}], "orig": "W. LUO ET AL.", "text": "W. LUO ET AL."}, {"self_ref": "#/texts/128", "parent": {"$ref": "#/pictures/13"}, "children": [], "content_layer": "body", "label": "page_header", "prov": [{"page_no": 10, "bbox": {"l": 72.0, "t": 692.075, "r": 79.695, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "10", "text": "10"}, {"self_ref": "#/texts/129", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 72.0, "t": 667.956, "r": 434.662, "b": 460.569, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1176]}], "orig": "metastasis  and  regulation  of  lung  adenocarcinoma, [30] gastric  cancer, [31] and prostate  cancer. [32] More  critically,  <PERSON>  et  al.  noted  that  the  LINC01018/miR182-5p  axis  has  positive  implications  for  glioma  cell  bioactivity  and  patient therapy. [33] In  the  HCC  discussion,  <PERSON>  et  al.  also  proposed  that  the  elevated  expression  of  LINC01018  as  a  tumor  suppressor  affected  cell  survival by  sponging  miR-182-5p  and  activating  the  downstream  fox01  factor. [34] Combined with the above,  elevated  LINC01018  was  found  to  have  a  better prognostic  value  in  prostate  cancer,  and  LINC01018  sponge  miR-182-5p mediated tumor progression. The results provide a new theoretical reference for the prognosis and clinical treatment of prostate cancer patients. However, there  are  still  some  shortcomings  in  this  study:  the  sample  size  is  limited, and  more  samples  are  needed  to  reveal  the  potential  association  between LINC01018 expression and clinical characteristics in prostate cancer patients. In  vivo assays  are  lacking,  and  animal  models  need  to  be  designed  for further  investigation.", "text": "metastasis  and  regulation  of  lung  adenocarcinoma, [30] gastric  cancer, [31] and prostate  cancer. [32] More  critically,  <PERSON>  et  al.  noted  that  the  LINC01018/miR182-5p  axis  has  positive  implications  for  glioma  cell  bioactivity  and  patient therapy. [33] In  the  HCC  discussion,  <PERSON>  et  al.  also  proposed  that  the  elevated  expression  of  LINC01018  as  a  tumor  suppressor  affected  cell  survival by  sponging  miR-182-5p  and  activating  the  downstream  fox01  factor. [34] Combined with the above,  elevated  LINC01018  was  found  to  have  a  better prognostic  value  in  prostate  cancer,  and  LINC01018  sponge  miR-182-5p mediated tumor progression. The results provide a new theoretical reference for the prognosis and clinical treatment of prostate cancer patients. However, there  are  still  some  shortcomings  in  this  study:  the  sample  size  is  limited, and  more  samples  are  needed  to  reveal  the  potential  association  between LINC01018 expression and clinical characteristics in prostate cancer patients. In  vivo assays  are  lacking,  and  animal  models  need  to  be  designed  for further  investigation."}, {"self_ref": "#/texts/130", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 10, "bbox": {"l": 72.0, "t": 435.709, "r": 139.074, "b": 426.42, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 14]}], "orig": "5.  Conclusion", "text": "5.  Conclusion", "level": 1}, {"self_ref": "#/texts/131", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 72.0, "t": 414.781, "r": 434.58, "b": 348.569, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 351]}], "orig": "In summary, LINC01018 was downregulated in prostate cancer tissues and cells,  which  indicated  a  poor  prognosis.  Overexpression  of  LINC01018 inhibited  the  progression  of  prostate  cancer  by  regulating  miR-182-5p. More  importantly,  this  suggests  that  LINC01018  may  become  a  potential prognostic  biomarker  for  prostate  cancer.", "text": "In summary, LINC01018 was downregulated in prostate cancer tissues and cells,  which  indicated  a  poor  prognosis.  Overexpression  of  LINC01018 inhibited  the  progression  of  prostate  cancer  by  regulating  miR-182-5p. More  importantly,  this  suggests  that  LINC01018  may  become  a  potential prognostic  biomarker  for  prostate  cancer."}, {"self_ref": "#/texts/132", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 10, "bbox": {"l": 72.0, "t": 324.709, "r": 176.917, "b": 315.42, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "Disclosure  statement", "text": "Disclosure  statement", "level": 1}, {"self_ref": "#/texts/133", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 72.0, "t": 304.479, "r": 322.133, "b": 296.043, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 73]}], "orig": "No  potential  conflict  of  interest  was  reported  by  the  author(s).", "text": "No  potential  conflict  of  interest  was  reported  by  the  author(s)."}, {"self_ref": "#/texts/134", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 10, "bbox": {"l": 72.0, "t": 271.709, "r": 112.799, "b": 262.42, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "Funding", "text": "Funding", "level": 1}, {"self_ref": "#/texts/135", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 72.0, "t": 251.47900000000004, "r": 432.038, "b": 243.043, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 93]}], "orig": "The author(s) reported there is no funding associated with the work featured in this article.", "text": "The author(s) reported there is no funding associated with the work featured in this article."}, {"self_ref": "#/texts/136", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 10, "bbox": {"l": 72.0, "t": 221.709, "r": 207.794, "b": 212.41999999999996, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 29]}], "orig": "Data  availability  statement", "text": "Data  availability  statement", "level": 1}, {"self_ref": "#/texts/137", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 72.0, "t": 201.47900000000004, "r": 294.17, "b": 193.043, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 59]}], "orig": "Corresponding  authors  may  provide  data  and  materials.", "text": "Corresponding  authors  may  provide  data  and  materials."}, {"self_ref": "#/texts/138", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 10, "bbox": {"l": 72.0, "t": 170.59899999999993, "r": 126.541, "b": 161.31100000000004, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 10]}], "orig": "References", "text": "References", "level": 1}, {"self_ref": "#/texts/139", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 10, "bbox": {"l": 76.551, "t": 150.36900000000003, "r": 434.185, "b": 117.93299999999999, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 257]}], "orig": "[1]  <PERSON>, <PERSON><PERSON>;  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON><PERSON>  <PERSON><PERSON>  <PERSON>-Target  Virtual  Screening  and  Network Pharmacology for Prediction of Molecular Mechanism of <PERSON><PERSON><PERSON><PERSON> for  Prostate  Cancer. Sci.  Rep. 2021 , 11 ,  6656.  DOI:  10.1038/s41598-021-86141-1.", "text": "[1]  <PERSON>, <PERSON><PERSON>;  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON><PERSON>  <PERSON><PERSON>  <PERSON>-Target  Virtual  Screening  and  Network Pharmacology for Prediction of Molecular Mechanism of <PERSON><PERSON><PERSON><PERSON> for  Prostate  Cancer. Sci.  Rep. 2021 , 11 ,  6656.  DOI:  10.1038/s41598-021-86141-1.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/140", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 10, "bbox": {"l": 72.0, "t": 114.36900000000003, "r": 434.23, "b": 69.93299999999999, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 297]}], "orig": "[2] <PERSON>, <PERSON><PERSON>;  <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>. <PERSON> of Potential Key Genes for  Pathogenesis  and  Prognosis  in  Prostate  Cancer  by  Integrated  Analysis  of  Gene Expression Profiles and the Cancer Genome Atlas. Front. Oncol. 2020 , 10 ,  809.  DOI: 10.3389/fonc.2020.00809.", "text": "[2] <PERSON>, <PERSON><PERSON>;  <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>. <PERSON> of Potential Key Genes for  Pathogenesis  and  Prognosis  in  Prostate  Cancer  by  Integrated  Analysis  of  Gene Expression Profiles and the Cancer Genome Atlas. Front. Oncol. 2020 , 10 ,  809.  DOI: 10.3389/fonc.2020.00809.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/141", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 11, "bbox": {"l": 248.456, "t": 692.075, "r": 401.336, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "NUCLEOSIDES, NUCLEOTIDES & NUCLEIC ACIDS", "text": "NUCLEOSIDES, NUCLEOTIDES & NUCLEIC ACIDS"}, {"self_ref": "#/texts/142", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 11, "bbox": {"l": 424.305, "t": 692.075, "r": 432.0, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "11", "text": "11"}, {"self_ref": "#/texts/143", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 72.0, "t": 666.933, "r": 434.172, "b": 634.498, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 254]}], "orig": "[3] <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>.  Intensity  Modulated  Radiotherapy  in  Combination  with Endocrinotherapy  in  the  Treatment  of  Middle  and  Advanced  Prostatic  Cancer. Pak. J.  Med.  Sci. 2019 , 35 ,  1264-1269.  DOI:  10.12669/pjms.35.5.591.", "text": "[3] <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>.  Intensity  Modulated  Radiotherapy  in  Combination  with Endocrinotherapy  in  the  Treatment  of  Middle  and  Advanced  Prostatic  Cancer. Pak. J.  Med.  Sci. 2019 , 35 ,  1264-1269.  DOI:  10.12669/pjms.35.5.591.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/144", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 72.0, "t": 630.933, "r": 434.157, "b": 586.498, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 328]}], "orig": "[4] <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>; <PERSON>,  <PERSON>.  The  Effects  of  Matrine  in  Combination  with  Docetaxel  on  CastrationResistant  (Androgen-Independent)  Prostate  Cancer. Cancer  Manag.  Res. 2019 , 11 , 10125-10133.  DOI:  10.2147/CMAR.S213419.", "text": "[4] <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>; <PERSON>,  <PERSON>.  The  Effects  of  Matrine  in  Combination  with  Docetaxel  on  CastrationResistant  (Androgen-Independent)  Prostate  Cancer. Cancer  Manag.  Res. 2019 , 11 , 10125-10133.  DOI:  10.2147/CMAR.S213419.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/145", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 72.0, "t": 582.933, "r": 434.051, "b": 538.498, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 302]}], "orig": "[5] <PERSON><PERSON><PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  C.;  <PERSON>,  S.;  <PERSON>, <PERSON>;  <PERSON>, M<PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON><PERSON>, <PERSON><PERSON> <PERSON>. <PERSON> Salvage Radiotherapy following  Radical  Prostatectomy. Eur.  Urol. 2014 , 65 , 1034-1043.  DOI:  10.1016/j. eururo.2013.08.013.", "text": "[5] <PERSON><PERSON><PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  C.;  <PERSON>,  S.;  <PERSON>, <PERSON>;  <PERSON>, M<PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON><PERSON>, <PERSON><PERSON> <PERSON>. <PERSON> Salvage Radiotherapy following  Radical  Prostatectomy. Eur.  Urol. 2014 , 65 , 1034-1043.  DOI:  10.1016/j. eururo.2013.08.013.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/146", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 76.551, "t": 534.933, "r": 434.155, "b": 502.497, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 253]}], "orig": "[6]  <PERSON><PERSON>,  <PERSON> <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON><PERSON>  Metabolism-Related  4-lncRNA Prognostic Signature and Corresponding Mechanisms in Intrahepatic Cholangiocarcinoma. BMC Cancer. 2021 , 21 ,  608.  DOI:  10.1186/s12885-021-08322-5.", "text": "[6]  <PERSON><PERSON>,  <PERSON> <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON><PERSON>  Metabolism-Related  4-lncRNA Prognostic Signature and Corresponding Mechanisms in Intrahepatic Cholangiocarcinoma. BMC Cancer. 2021 , 21 ,  608.  DOI:  10.1186/s12885-021-08322-5.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/147", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 72.0, "t": 498.933, "r": 434.077, "b": 454.497, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 271]}], "orig": "[7] <PERSON><PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON><PERSON>; <PERSON>, X. Regulation of Long Non-Coding RNADreh  Involved  in  Proliferation  and  Migration  of  Hepatic  Progenitor  Cells  during Liver  Regeneration  in  Rats. Int. J. Mol.  Sci. 2019 , 20 , 2549.  DOI:  10.3390/ ijms20102549.", "text": "[7] <PERSON><PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON><PERSON>; <PERSON>, X. Regulation of Long Non-Coding RNADreh  Involved  in  Proliferation  and  Migration  of  Hepatic  Progenitor  Cells  during Liver  Regeneration  in  Rats. Int. J. Mol.  Sci. 2019 , 20 , 2549.  DOI:  10.3390/ ijms20102549.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/148", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 72.0, "t": 450.933, "r": 434.175, "b": 418.497, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 250]}], "orig": "[8] <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  Xi,  X.  LncRNA  MEG3 Inhibits  the  Progression  of  Prostate  Cancer  by  Modulating  miR-9-5p/QKI-5  Axis. J. Cell.  Mol.  Med. 2019 , 23 ,  29-38.  DOI:  10.1111/jcmm.13658.", "text": "[8] <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  Xi,  X.  LncRNA  MEG3 Inhibits  the  Progression  of  Prostate  Cancer  by  Modulating  miR-9-5p/QKI-5  Axis. J. Cell.  Mol.  Med. 2019 , 23 ,  29-38.  DOI:  10.1111/jcmm.13658.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/149", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 72.0, "t": 414.933, "r": 434.221, "b": 370.497, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 331]}], "orig": "[9] <PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>;  <PERSON>,  X.  LncRNA  OIP5-AS1  Inhibits  Ferroptosis  in  Prostate  Cancer  with  LongTerm  Cadmium  Exposure  through  miR-128-3p/SLC7A11  Signaling. Ecotoxicol. Environ.  Saf. 2021 , 220 ,  112376.  DOI:  10.1016/j.ecoenv.2021.112376.", "text": "[9] <PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>;  <PERSON>,  X.  LncRNA  OIP5-AS1  Inhibits  Ferroptosis  in  Prostate  Cancer  with  LongTerm  Cadmium  Exposure  through  miR-128-3p/SLC7A11  Signaling. Ecotoxicol. Environ.  Saf. 2021 , 220 ,  112376.  DOI:  10.1016/j.ecoenv.2021.112376.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/150", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 72.0, "t": 366.933, "r": 434.157, "b": 322.497, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 330]}], "orig": "10]  <PERSON>,  <PERSON><PERSON>;  <PERSON><PERSON><PERSON>, <PERSON> <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  S.  <PERSON>.  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>, [ V<PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  K<PERSON>;  <PERSON>,  <PERSON>;  et  al.  The  Long  Noncoding  RNA  H19 Regulates Tumor Plasticity in Neuroendocrine Prostate Cancer. Nat. Commun. 2021 , 12 ,  7349.  DOI:  10.1038/s41467-021-26901-9.", "text": "10]  <PERSON>,  <PERSON><PERSON>;  <PERSON><PERSON><PERSON>, <PERSON> <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  S.  <PERSON>.  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>, [ V<PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  K<PERSON>;  <PERSON>,  <PERSON>;  et  al.  The  Long  Noncoding  RNA  H19 Regulates Tumor Plasticity in Neuroendocrine Prostate Cancer. Nat. Commun. 2021 , 12 ,  7349.  DOI:  10.1038/s41467-021-26901-9.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/151", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 72.0, "t": 318.933, "r": 434.232, "b": 274.497, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 307]}], "orig": "11]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>, [ <PERSON><PERSON>; <PERSON>,  <PERSON>;  et  al.  Curcumenol  Triggered  Ferroptosis  in  Lung  Cancer  Cells  via lncRNA H19/miR-19b-3p/FTH1 Axis. Bioact. Mater. 2022 , 13 ,  23-36.  DOI: 10.1016/j. bioactmat.2021.11.013.", "text": "11]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>, [ <PERSON><PERSON>; <PERSON>,  <PERSON>;  et  al.  Curcumenol  Triggered  Ferroptosis  in  Lung  Cancer  Cells  via lncRNA H19/miR-19b-3p/FTH1 Axis. Bioact. Mater. 2022 , 13 ,  23-36.  DOI: 10.1016/j. bioactmat.2021.11.013.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/152", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 72.0, "t": 270.933, "r": 434.181, "b": 238.497, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 232]}], "orig": "12]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON> <PERSON>  <PERSON><PERSON>  Non-Coding  RNA-H19  Promotes  Ovarian  Cancer  Cell [ Proliferation  and  Migration  via  the  microRNA-140/Wnt1  Axis. Kaohsiung  J.  Med. Sci. 2021 , 37 ,  768-775.  DOI:  10.1002/kjm2.12393.", "text": "12]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON> <PERSON>  <PERSON><PERSON>  Non-Coding  RNA-H19  Promotes  Ovarian  Cancer  Cell [ Proliferation  and  Migration  via  the  microRNA-140/Wnt1  Axis. Kaohsiung  J.  Med. Sci. 2021 , 37 ,  768-775.  DOI:  10.1002/kjm2.12393.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/153", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 72.0, "t": 234.933, "r": 434.165, "b": 190.49700000000007, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 345]}], "orig": "13]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>, [ <PERSON><PERSON>; <PERSON>,  <PERSON>.;  et  al.  Potential  lncRNA  Biomarkers  for  HBV-Related  Hepatocellular Carcinoma  Diagnosis  Revealed  by  Analysis  on  Coexpression  Network. Biomed  Res. Int. 2021 , 2021 ,  9972011-9972015.  DOI:  10.1155/2021/9972011.", "text": "13]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>, [ <PERSON><PERSON>; <PERSON>,  <PERSON>.;  et  al.  Potential  lncRNA  Biomarkers  for  HBV-Related  Hepatocellular Carcinoma  Diagnosis  Revealed  by  Analysis  on  Coexpression  Network. Biomed  Res. Int. 2021 , 2021 ,  9972011-9972015.  DOI:  10.1155/2021/9972011.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/154", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 72.0, "t": 186.933, "r": 434.145, "b": 154.49700000000007, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 252]}], "orig": "14]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON><PERSON>  Non-Coding  RNA  LINC01018  Inhibits  the [ Progression  of  Acute  Myeloid  Leukemia  by  Targeting  miR-499a-5p  to  Regulate PDCD4. Oncol.  Lett. 2021 , 22 ,  541.  DOI:  10.3892/ol.2021.12802.", "text": "14]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON><PERSON>  Non-Coding  RNA  LINC01018  Inhibits  the [ Progression  of  Acute  Myeloid  Leukemia  by  Targeting  miR-499a-5p  to  Regulate PDCD4. Oncol.  Lett. 2021 , 22 ,  541.  DOI:  10.3892/ol.2021.12802.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/155", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 72.0, "t": 150.933, "r": 434.168, "b": 118.49800000000005, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 248]}], "orig": "15]  <PERSON><PERSON><PERSON>,  S.;  <PERSON>,  S.;  <PERSON>,  S.;  <PERSON>,  G.;  <PERSON>,  S.  Weighted [ Correlation Network Analysis Revealed Novel Long Non-Coding RNAs for Colorectal Cancer. Sci.  Rep. 2022 , 12 ,  2990.  DOI:  10.1038/s41598-022-06934-w.", "text": "15]  <PERSON><PERSON><PERSON>,  S.;  <PERSON>,  S.;  <PERSON>,  S.;  <PERSON>,  G.;  <PERSON>,  S.  Weighted [ Correlation Network Analysis Revealed Novel Long Non-Coding RNAs for Colorectal Cancer. Sci.  Rep. 2022 , 12 ,  2990.  DOI:  10.1038/s41598-022-06934-w.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/156", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 72.0, "t": 114.93299999999999, "r": 434.16, "b": 70.49800000000005, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 285]}], "orig": "16]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>, [ F. LncRNA CRNDE Promotes the Progression of B-Cell Precursor Acute Lymphoblastic Leukemia  by  Targeting  the  miR-345-5p/CREB  Axis. Molecules  Cells. 2020 , 43 ,  718727.", "text": "16]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>, [ F. LncRNA CRNDE Promotes the Progression of B-Cell Precursor Acute Lymphoblastic Leukemia  by  Targeting  the  miR-345-5p/CREB  Axis. Molecules  Cells. 2020 , 43 ,  718727.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/157", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 12, "bbox": {"l": 72.0, "t": 692.075, "r": 79.695, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "12", "text": "12"}, {"self_ref": "#/texts/158", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 12, "bbox": {"l": 102.325, "t": 692.075, "r": 145.09, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 13]}], "orig": "W. LUO ET AL.", "text": "W. LUO ET AL."}, {"self_ref": "#/texts/159", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 72.0, "t": 666.933, "r": 434.083, "b": 622.498, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 326]}], "orig": "17]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON><PERSON> [ Noncoding  RNA  (lncRNA)  FOXD2-AS1  Promotes  Cell  Proliferation  and  Metastasis in  Hepatocellular  Carcinoma  by  Regulating  MiR-185/AKT  Axis. Med.  Sci.  Monit. 2019 , 25 ,  9618-9629.  DOI:  10.12659/MSM.918230.", "text": "17]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON><PERSON> [ Noncoding  RNA  (lncRNA)  FOXD2-AS1  Promotes  Cell  Proliferation  and  Metastasis in  Hepatocellular  Carcinoma  by  Regulating  MiR-185/AKT  Axis. Med.  Sci.  Monit. 2019 , 25 ,  9618-9629.  DOI:  10.12659/MSM.918230.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/160", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 72.0, "t": 618.933, "r": 434.143, "b": 586.498, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 227]}], "orig": "18]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON> <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON> <PERSON>  of  Prostate  Cancer [ Treatment:  Armed  CAR-T  or  Combination  Therapy. Cancers  (Basel). 2022 , 14, 967. DOI:  10.3390/cancers14040967.", "text": "18]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON> <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON> <PERSON>  of  Prostate  Cancer [ Treatment:  Armed  CAR-T  or  Combination  Therapy. Cancers  (Basel). 2022 , 14, 967. DOI:  10.3390/cancers14040967.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/161", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 72.0, "t": 582.933, "r": 434.175, "b": 538.498, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 327]}], "orig": "19]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>, [ X.  m(6)  a  Modification  of  lncRNA  PCAT6  Promotes  Bone  Metastasis  in  Prostate Cancer through IGF2BP2-Mediated IGF1R mRNA Stabilization. Clinical Translational Med. 2021 , 11 ,  e426.  DOI:  10.1002/ctm2.426.", "text": "19]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>, [ X.  m(6)  a  Modification  of  lncRNA  PCAT6  Promotes  Bone  Metastasis  in  Prostate Cancer through IGF2BP2-Mediated IGF1R mRNA Stabilization. Clinical Translational Med. 2021 , 11 ,  e426.  DOI:  10.1002/ctm2.426.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/162", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 72.0, "t": 534.933, "r": 434.104, "b": 502.497, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 231]}], "orig": "20]  <PERSON><PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  L.  Emerging  Roles  of  Long [ Non-Coding RNAs as Novel Biomarkers in the Diagnosis and Prognosis of  Prostate Cancer. Discov.  Med. 2021 , 32 ,  29-37.", "text": "20]  <PERSON><PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  L.  Emerging  Roles  of  Long [ Non-Coding RNAs as Novel Biomarkers in the Diagnosis and Prognosis of  Prostate Cancer. Discov.  Med. 2021 , 32 ,  29-37.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/163", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 72.0, "t": 498.933, "r": 434.25, "b": 454.497, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 355]}], "orig": "21]  <PERSON>, <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON><PERSON><PERSON>, [ V<PERSON>;  <PERSON>,  S.;  <PERSON>,  P .;  Z<PERSON>,  K.;  <PERSON>,  I.;  <PERSON>,  S.; et  al.  METTL1 Promotes Tumorigenesis through tRNA-Derived Fragment Biogenesis in  Prostate  Cancer. Mol.  Cancer. 2023 , 22 ,  119.  DOI:  10.1186/s12943-023-01809-8.", "text": "21]  <PERSON>, <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON><PERSON><PERSON>, [ V<PERSON>;  <PERSON>,  S.;  <PERSON>,  P .;  Z<PERSON>,  K.;  <PERSON>,  I.;  <PERSON>,  S.; et  al.  METTL1 Promotes Tumorigenesis through tRNA-Derived Fragment Biogenesis in  Prostate  Cancer. Mol.  Cancer. 2023 , 22 ,  119.  DOI:  10.1186/s12943-023-01809-8.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/164", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 72.0, "t": 450.933, "r": 434.157, "b": 406.497, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 329]}], "orig": "22]  <PERSON><PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  M<PERSON>;  <PERSON><PERSON>,  <PERSON>;  <PERSON><PERSON><PERSON>,  <PERSON>;  <PERSON><PERSON><PERSON>, [ Y.;  Yo<PERSON>,  N.;  <PERSON><PERSON><PERSON>,  M.;  et  al.  In  Vivo  Functional  Analysis  of  Non-Conserved Human  lncRNAs  Associated  with  Cardiometabolic  Traits. Nat.  Commun. 2020 , 11 , 45.  DOI:  10.1038/s41467-019-13688-z.", "text": "22]  <PERSON><PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  M<PERSON>;  <PERSON><PERSON>,  <PERSON>;  <PERSON><PERSON><PERSON>,  <PERSON>;  <PERSON><PERSON><PERSON>, [ Y.;  Yo<PERSON>,  N.;  <PERSON><PERSON><PERSON>,  M.;  et  al.  In  Vivo  Functional  Analysis  of  Non-Conserved Human  lncRNAs  Associated  with  Cardiometabolic  Traits. Nat.  Commun. 2020 , 11 , 45.  DOI:  10.1038/s41467-019-13688-z.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/165", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 72.0, "t": 402.933, "r": 434.167, "b": 370.497, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 262]}], "orig": "23]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  J.  LncRNA  LINC01018/miR-942-[ 5p/KNG1  Axis  Regulates  the  Malignant  Development  of  Glioma  in  Vitro  and  in Vivo. CNS  Neurosci.  Ther. 2023 , 29 ,  691-711.  DOI:  10.1111/cns.14053.", "text": "23]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  J.  LncRNA  LINC01018/miR-942-[ 5p/KNG1  Axis  Regulates  the  Malignant  Development  of  Glioma  in  Vitro  and  in Vivo. CNS  Neurosci.  Ther. 2023 , 29 ,  691-711.  DOI:  10.1111/cns.14053.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/166", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 72.0, "t": 366.933, "r": 434.213, "b": 334.497, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 276]}], "orig": "24]  <PERSON><PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON> <PERSON>;  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>.  Comprehensive  Analysis [ of  ESR1-Related  ceRNA  Axis  as  a  Novel  Prognostic  Biomarker  in  Hepatocellular Carcinoma. Epigenomics. 2022 , 14 ,  1393-1409.  DOI:  10.2217/epi-2022-0291.", "text": "24]  <PERSON><PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON> <PERSON>;  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>.  Comprehensive  Analysis [ of  ESR1-Related  ceRNA  Axis  as  a  Novel  Prognostic  Biomarker  in  Hepatocellular Carcinoma. Epigenomics. 2022 , 14 ,  1393-1409.  DOI:  10.2217/epi-2022-0291.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/167", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 72.0, "t": 330.933, "r": 434.161, "b": 286.497, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 296]}], "orig": "25]  <PERSON><PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  T.  LncRNA  NCK1-AS1  Promotes [ Cancer  Cell  Proliferation  and  Increase  Cell  Stemness  in  Urinary  Bladder  Cancer Patients by Downregulating miR-143. Cancer Manag. Res. 2020 , 12 ,  1661-1668. DOI: 10.2147/CMAR.S223172.", "text": "25]  <PERSON><PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  T.  LncRNA  NCK1-AS1  Promotes [ Cancer  Cell  Proliferation  and  Increase  Cell  Stemness  in  Urinary  Bladder  Cancer Patients by Downregulating miR-143. Cancer Manag. Res. 2020 , 12 ,  1661-1668. DOI: 10.2147/CMAR.S223172.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/168", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 72.0, "t": 282.933, "r": 434.157, "b": 250.497, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 244]}], "orig": "26]  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>.  Non-Coding  RNAs  in [ Enzalutamide  Resistance  of  Castration-Resistant  Prostate  Cancer. Cancer  Lett. 2023 , 566 ,  216247.  DOI:  10.1016/j.canlet.2023.216247.", "text": "26]  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>.  Non-Coding  RNAs  in [ Enzalutamide  Resistance  of  Castration-Resistant  Prostate  Cancer. Cancer  Lett. 2023 , 566 ,  216247.  DOI:  10.1016/j.canlet.2023.216247.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/169", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 72.0, "t": 246.933, "r": 434.069, "b": 214.497, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 282]}], "orig": "27]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>; [ <PERSON>, <PERSON><PERSON>  circRO<PERSON>1 Promotes Prostate Cancer Growth and Enzalutamide Resistance via  Accelerating  Glycolysis. J.  Cancer. 2023 , 14 ,  2574-2584.  DOI:  10.7150/jca.86940.", "text": "27]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>; [ <PERSON>, <PERSON><PERSON>  circRO<PERSON>1 Promotes Prostate Cancer Growth and Enzalutamide Resistance via  Accelerating  Glycolysis. J.  Cancer. 2023 , 14 ,  2574-2584.  DOI:  10.7150/jca.86940.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/170", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 72.0, "t": 210.933, "r": 434.221, "b": 178.49700000000007, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 251]}], "orig": "28]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON> <PERSON>;  <PERSON><PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON> <PERSON>;  <PERSON>,  <PERSON><PERSON> [ Significance of miR-183-3p and miR-182-5p in NSCLC and Their Correlation. Cancer Manag.  Res. 2021 , 13 ,  3539-3550.  DOI:  10.2147/CMAR.S305179.", "text": "28]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON> <PERSON>;  <PERSON><PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON> <PERSON>;  <PERSON>,  <PERSON><PERSON> [ Significance of miR-183-3p and miR-182-5p in NSCLC and Their Correlation. Cancer Manag.  Res. 2021 , 13 ,  3539-3550.  DOI:  10.2147/CMAR.S305179.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/171", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 72.0, "t": 174.933, "r": 434.175, "b": 142.49800000000005, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 228]}], "orig": "29]  <PERSON><PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON><PERSON> <PERSON><PERSON> Non-Coding RNAs XIST and MALAT1 [ Hijack  the  PD-L1  Regulatory  Signaling  Pathway  in  Breast  Cancer  Subtypes. Oncol. Lett. 2021 , 22 ,  593.  DOI:  10.3892/ol.2021.12854.", "text": "29]  <PERSON><PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON><PERSON> <PERSON><PERSON> Non-Coding RNAs XIST and MALAT1 [ Hijack  the  PD-L1  Regulatory  Signaling  Pathway  in  Breast  Cancer  Subtypes. Oncol. Lett. 2021 , 22 ,  593.  DOI:  10.3892/ol.2021.12854.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/172", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 72.0, "t": 138.933, "r": 434.077, "b": 106.49800000000005, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 241]}], "orig": "30]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  J.  miR-182-5p  <PERSON>ves  as  an  Oncogene  in [ Lung Adenocarcinoma through Binding to STARD13. Comput.  Math.  Methods  Med. 2021 , 2021 ,  7074343-7074311.  DOI:  10.1155/2021/7074343.", "text": "30]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  J.  miR-182-5p  <PERSON>ves  as  an  Oncogene  in [ Lung Adenocarcinoma through Binding to STARD13. Comput.  Math.  Methods  Med. 2021 , 2021 ,  7074343-7074311.  DOI:  10.1155/2021/7074343.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/173", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 72.0, "t": 102.93299999999999, "r": 433.995, "b": 70.49800000000005, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 275]}], "orig": "31]  <PERSON><PERSON>,  S.  E.;  <PERSON>,  N.  S.;  <PERSON>,  S.  M.;  <PERSON>,  M.  F.  Down  Regulation  of  miR-[ 30a-5p  and  miR-182-5p  in  Gastric  Cancer:  Clinical  Impact  and  Survival  Analysis. Biochem.  Biophys.  Rep. 2021 , 27 ,  101079.  DOI:  10.1016/j.bbrep.2021.101079.", "text": "31]  <PERSON><PERSON>,  S.  E.;  <PERSON>,  N.  S.;  <PERSON>,  S.  M.;  <PERSON>,  M.  F.  Down  Regulation  of  miR-[ 30a-5p  and  miR-182-5p  in  Gastric  Cancer:  Clinical  Impact  and  Survival  Analysis. Biochem.  Biophys.  Rep. 2021 , 27 ,  101079.  DOI:  10.1016/j.bbrep.2021.101079.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/174", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 13, "bbox": {"l": 248.456, "t": 692.075, "r": 401.336, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "NUCLEOSIDES, NUCLEOTIDES & NUCLEIC ACIDS", "text": "NUCLEOSIDES, NUCLEOTIDES & NUCLEIC ACIDS"}, {"self_ref": "#/texts/175", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 13, "bbox": {"l": 424.305, "t": 692.075, "r": 432.0, "b": 685.693, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "13", "text": "13"}, {"self_ref": "#/texts/176", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 72.0, "t": 666.933, "r": 434.23, "b": 634.498, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 272]}], "orig": "32]  <PERSON><PERSON>,  <PERSON><PERSON>;  <PERSON><PERSON><PERSON>,  I.  M.  <PERSON>.;  F<PERSON><PERSON>,  A.  S.;  <PERSON><PERSON><PERSON>,  V .  C.;  <PERSON>,  <PERSON>;  <PERSON>,  L. [ R.  MiR-182-5p Modulates Prostate Cancer Aggressive Phenotypes by Targeting EMT Associated  Pathways. Biomolecules. 2022 , 12 ,  187.  DOI:  10.3390/biom12020187.", "text": "32]  <PERSON><PERSON>,  <PERSON><PERSON>;  <PERSON><PERSON><PERSON>,  I.  M.  <PERSON>.;  F<PERSON><PERSON>,  A.  S.;  <PERSON><PERSON><PERSON>,  V .  C.;  <PERSON>,  <PERSON>;  <PERSON>,  L. [ R.  MiR-182-5p Modulates Prostate Cancer Aggressive Phenotypes by Targeting EMT Associated  Pathways. Biomolecules. 2022 , 12 ,  187.  DOI:  10.3390/biom12020187.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/177", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 72.0, "t": 630.933, "r": 434.158, "b": 586.498, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 310]}], "orig": "33]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON><PERSON><PERSON>, [ <PERSON><PERSON>  Non-Coding  RNA  LINC01018  Inhibits  Human  Glioma  Cell  Proliferation and Metastasis by Directly Targeting miRNA-182-5p. J.  Neurooncol. 2022 , 160 ,  67-78. DOI:  10.1007/s11060-022-04113-5.", "text": "33]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON>;  <PERSON><PERSON><PERSON>, [ <PERSON><PERSON>  Non-Coding  RNA  LINC01018  Inhibits  Human  Glioma  Cell  Proliferation and Metastasis by Directly Targeting miRNA-182-5p. J.  Neurooncol. 2022 , 160 ,  67-78. DOI:  10.1007/s11060-022-04113-5.", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/178", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 72.0, "t": 582.933, "r": 434.118, "b": 538.498, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 301]}], "orig": "34]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  H.  LINC01018  Confers  a  Novel [ Tumor  Suppressor  Role  in  Hepatocellular  Carcinoma  through  Sponging  microRNA-182-5p. Am.  J.  Physiol.  Gastrointest.  Liver  Physiol. 2019 , 317 ,  G116-g126.  DOI: 10.1152/ajpgi.00005.2019.", "text": "34]  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON><PERSON>;  <PERSON>,  <PERSON>;  <PERSON>,  H.  LINC01018  Confers  a  Novel [ Tumor  Suppressor  Role  in  Hepatocellular  Carcinoma  through  Sponging  microRNA-182-5p. Am.  J.  Physiol.  Gastrointest.  Liver  Physiol. 2019 , 317 ,  G116-g126.  DOI: 10.1152/ajpgi.00005.2019.", "enumerated": false, "marker": "-"}], "pictures": [{"self_ref": "#/pictures/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 329.4596252441406, "t": 676.6111717224121, "r": 432.67498779296875, "b": 650.5905914306641, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 368.1827087402344, "t": 642.9289627075195, "r": 432.51971435546875, "b": 629.6413269042969, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 140.6577606201172, "t": 92.98736572265625, "r": 152.362060546875, "b": 81.7301025390625, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 231.94642639160156, "t": 93.29437255859375, "r": 243.53460693359375, "b": 81.51690673828125, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 3, "bbox": {"l": 410.7538757324219, "t": 696.3133659362793, "r": 422.5131530761719, "b": 683.4210662841797, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/5", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 5, "bbox": {"l": 410.9393005371094, "t": 696.3250827789307, "r": 422.3319091796875, "b": 683.3840789794922, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/6", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/54"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 5, "bbox": {"l": 77.7623519897461, "t": 264.3246154785156, "r": 425.7815246582031, "b": 125.18646240234375, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/54"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/7", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/62"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 6, "bbox": {"l": 138.09725952148438, "t": 338.8822021484375, "r": 365.40509033203125, "b": 112.12841796875, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/62"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/8", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 7, "bbox": {"l": 410.83148193359375, "t": 696.4194717407227, "r": 422.50286865234375, "b": 683.4291648864746, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/9", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/67"}, {"$ref": "#/texts/68"}, {"$ref": "#/texts/69"}, {"$ref": "#/texts/70"}, {"$ref": "#/texts/71"}, {"$ref": "#/texts/72"}, {"$ref": "#/texts/73"}, {"$ref": "#/texts/74"}, {"$ref": "#/texts/75"}, {"$ref": "#/texts/76"}, {"$ref": "#/texts/77"}, {"$ref": "#/texts/78"}, {"$ref": "#/texts/79"}, {"$ref": "#/texts/80"}, {"$ref": "#/texts/81"}, {"$ref": "#/texts/82"}, {"$ref": "#/texts/83"}, {"$ref": "#/texts/84"}, {"$ref": "#/texts/85"}, {"$ref": "#/texts/86"}, {"$ref": "#/texts/87"}, {"$ref": "#/texts/88"}, {"$ref": "#/texts/89"}, {"$ref": "#/texts/90"}, {"$ref": "#/texts/91"}, {"$ref": "#/texts/92"}, {"$ref": "#/texts/93"}, {"$ref": "#/texts/94"}, {"$ref": "#/texts/95"}, {"$ref": "#/texts/96"}, {"$ref": "#/texts/97"}, {"$ref": "#/texts/98"}, {"$ref": "#/texts/99"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 7, "bbox": {"l": 73.19480895996094, "t": 544.3490600585938, "r": 431.3471374511719, "b": 294.537353515625, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/67"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/10", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/105"}, {"$ref": "#/texts/106"}, {"$ref": "#/texts/107"}, {"$ref": "#/texts/108"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 8, "bbox": {"l": 77.39935302734375, "t": 669.7360000610352, "r": 425.5398864746094, "b": 526.8738708496094, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/105"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/11", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/109"}, {"$ref": "#/texts/110"}, {"$ref": "#/texts/111"}, {"$ref": "#/texts/112"}, {"$ref": "#/texts/113"}, {"$ref": "#/texts/114"}, {"$ref": "#/texts/115"}, {"$ref": "#/texts/116"}, {"$ref": "#/texts/117"}, {"$ref": "#/texts/118"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 8, "bbox": {"l": 73.33745574951172, "t": 461.6916198730469, "r": 432.1496276855469, "b": 353.5608215332031, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/109"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/12", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 9, "bbox": {"l": 410.939453125, "t": 696.4161758422852, "r": 422.329833984375, "b": 683.3661994934082, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/13", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/128"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 10, "bbox": {"l": 71.6839370727539, "t": 696.5526847839355, "r": 97.38744354248047, "b": 683.6630783081055, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/14", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 11, "bbox": {"l": 406.69305419921875, "t": 696.7627029418945, "r": 419.66204833984375, "b": 683.2370910644531, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/15", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 13, "bbox": {"l": 406.6636657714844, "t": 696.8226222991943, "r": 419.5718078613281, "b": 683.2645072937012, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}], "tables": [{"self_ref": "#/tables/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 3, "bbox": {"l": 71.19676971435547, "t": 647.2644424438477, "r": 432.22625732421875, "b": 483.1105651855469, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 72.0, "t": 90.07799999999997, "r": 117.755, "b": 96.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "characteristics", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 187.663, "t": 81.07799999999997, "r": 205.143, "b": 87.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "cases", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 183.219, "t": 90.07799999999997, "r": 209.595, "b": 96.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "n = 110", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 262.597, "t": 77.57799999999997, "r": 333.033, "b": 84.39300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 2, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 4, "text": "liNc01018 expression", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 387.253, "t": 90.07799999999997, "r": 414.156, "b": 96.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "P values", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 241.028, "t": 90.07799999999997, "r": 283.994, "b": 96.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "low (n = 63)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 309.1, "t": 90.07799999999997, "r": 354.123, "b": 96.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "High (n = 47)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 72.0, "t": 102.07799999999997, "r": 84.31, "b": 108.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Age", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 394.322, "t": 102.07799999999997, "r": 411.466, "b": 108.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.721", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 80.08, "t": 111.07799999999997, "r": 92.608, "b": 117.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "≤65", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 111.07799999999997, "r": 200.163, "b": 117.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "54", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 111.07799999999997, "r": 266.263, "b": 117.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "30", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 111.07799999999997, "r": 335.363, "b": 117.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "24", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 80.08, "t": 120.07799999999997, "r": 92.608, "b": 126.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": ">65", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 120.07799999999997, "r": 200.163, "b": 126.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "56", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 120.07799999999997, "r": 266.263, "b": 126.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "33", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 120.07799999999997, "r": 335.363, "b": 126.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "23", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 72.0, "t": 129.07799999999997, "r": 116.565, "b": 135.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON> score", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 394.322, "t": 129.07799999999997, "r": 411.466, "b": 135.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.086", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 80.08, "t": 138.07799999999997, "r": 88.728, "b": 144.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "≤7", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 138.07799999999997, "r": 200.163, "b": 144.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "72", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 138.07799999999997, "r": 266.263, "b": 144.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "37", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 138.07799999999997, "r": 335.363, "b": 144.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "35", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 80.08, "t": 147.07799999999997, "r": 88.728, "b": 153.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": ">7", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 147.07799999999997, "r": 200.163, "b": 153.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "38", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 147.07799999999997, "r": 266.263, "b": 153.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "26", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 147.07799999999997, "r": 335.363, "b": 153.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "12", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 72.0, "t": 156.07799999999997, "r": 148.64, "b": 162.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "lymph node metastasis", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 80.08, "t": 165.07799999999997, "r": 108.069, "b": 171.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Negative", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 165.07799999999997, "r": 200.163, "b": 171.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "71", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 165.07799999999997, "r": 266.263, "b": 171.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "33", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 165.07799999999997, "r": 335.363, "b": 171.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "38", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.322, "t": 165.07799999999997, "r": 411.466, "b": 171.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.002", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 80.08, "t": 174.07799999999997, "r": 104.365, "b": 180.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Positive", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 174.07799999999997, "r": 200.163, "b": 180.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "39", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 174.07799999999997, "r": 266.263, "b": 180.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "30", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 331.561, "t": 174.07799999999997, "r": 335.361, "b": 180.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 72.0, "t": 183.07799999999997, "r": 117.642, "b": 189.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "differentiation", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 79.792, "t": 192.07799999999997, "r": 93.294, "b": 198.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Well", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 192.07799999999997, "r": 200.163, "b": 198.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "46", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 192.07799999999997, "r": 266.263, "b": 198.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "26", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 192.07799999999997, "r": 335.363, "b": 198.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "20", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.322, "t": 192.07799999999997, "r": 411.466, "b": 198.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.893", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 80.08, "t": 201.07799999999997, "r": 94.462, "b": 207.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Poor", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 201.07799999999997, "r": 200.163, "b": 207.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "64", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 201.07799999999997, "r": 266.263, "b": 207.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "37", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 201.07799999999997, "r": 335.363, "b": 207.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "27", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 72.0, "t": 210.07799999999997, "r": 106.552, "b": 216.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "tNM stage", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 394.322, "t": 210.07799999999997, "r": 411.466, "b": 216.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.007", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 80.08, "t": 219.07799999999997, "r": 88.152, "b": 225.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "i-ii", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 219.07799999999997, "r": 200.163, "b": 225.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "61", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 219.07799999999997, "r": 266.263, "b": 225.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "28", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 219.07799999999997, "r": 335.363, "b": 225.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "33", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 80.08, "t": 228.07799999999997, "r": 94.264, "b": 234.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "iii-iV", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 228.07799999999997, "r": 200.163, "b": 234.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "49", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 228.07799999999997, "r": 266.263, "b": 234.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "35", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 228.07799999999997, "r": 335.363, "b": 234.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "14", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 17, "num_cols": 5, "grid": [[{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 187.663, "t": 81.07799999999997, "r": 205.143, "b": 87.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "cases", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 262.597, "t": 77.57799999999997, "r": 333.033, "b": 84.39300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 2, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 4, "text": "liNc01018 expression", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 262.597, "t": 77.57799999999997, "r": 333.033, "b": 84.39300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 2, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 4, "text": "liNc01018 expression", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 72.0, "t": 90.07799999999997, "r": 117.755, "b": 96.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "characteristics", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 183.219, "t": 90.07799999999997, "r": 209.595, "b": 96.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "n = 110", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 241.028, "t": 90.07799999999997, "r": 283.994, "b": 96.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "low (n = 63)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 309.1, "t": 90.07799999999997, "r": 354.123, "b": 96.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "High (n = 47)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 387.253, "t": 90.07799999999997, "r": 414.156, "b": 96.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "P values", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 72.0, "t": 102.07799999999997, "r": 84.31, "b": 108.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Age", "column_header": false, "row_header": true, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.322, "t": 102.07799999999997, "r": 411.466, "b": 108.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.721", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 80.08, "t": 111.07799999999997, "r": 92.608, "b": 117.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "≤65", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 111.07799999999997, "r": 200.163, "b": 117.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "54", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 111.07799999999997, "r": 266.263, "b": 117.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "30", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 111.07799999999997, "r": 335.363, "b": 117.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "24", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 80.08, "t": 120.07799999999997, "r": 92.608, "b": 126.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": ">65", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 120.07799999999997, "r": 200.163, "b": 126.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "56", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 120.07799999999997, "r": 266.263, "b": 126.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "33", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 120.07799999999997, "r": 335.363, "b": 126.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "23", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 72.0, "t": 129.07799999999997, "r": 116.565, "b": 135.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON> score", "column_header": false, "row_header": true, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.322, "t": 129.07799999999997, "r": 411.466, "b": 135.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.086", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 80.08, "t": 138.07799999999997, "r": 88.728, "b": 144.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "≤7", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 138.07799999999997, "r": 200.163, "b": 144.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "72", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 138.07799999999997, "r": 266.263, "b": 144.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "37", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 138.07799999999997, "r": 335.363, "b": 144.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "35", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 80.08, "t": 147.07799999999997, "r": 88.728, "b": 153.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": ">7", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 147.07799999999997, "r": 200.163, "b": 153.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "38", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 147.07799999999997, "r": 266.263, "b": 153.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "26", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 147.07799999999997, "r": 335.363, "b": 153.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "12", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 72.0, "t": 156.07799999999997, "r": 148.64, "b": 162.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "lymph node metastasis", "column_header": false, "row_header": true, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 80.08, "t": 165.07799999999997, "r": 108.069, "b": 171.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Negative", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 165.07799999999997, "r": 200.163, "b": 171.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "71", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 165.07799999999997, "r": 266.263, "b": 171.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "33", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 165.07799999999997, "r": 335.363, "b": 171.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "38", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.322, "t": 165.07799999999997, "r": 411.466, "b": 171.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.002", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 80.08, "t": 174.07799999999997, "r": 104.365, "b": 180.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Positive", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 174.07799999999997, "r": 200.163, "b": 180.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "39", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 174.07799999999997, "r": 266.263, "b": 180.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "30", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 331.561, "t": 174.07799999999997, "r": 335.361, "b": 180.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "9", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 72.0, "t": 183.07799999999997, "r": 117.642, "b": 189.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "differentiation", "column_header": false, "row_header": true, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 79.792, "t": 192.07799999999997, "r": 93.294, "b": 198.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Well", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 192.07799999999997, "r": 200.163, "b": 198.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "46", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 192.07799999999997, "r": 266.263, "b": 198.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "26", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 192.07799999999997, "r": 335.363, "b": 198.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "20", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.322, "t": 192.07799999999997, "r": 411.466, "b": 198.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.893", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 80.08, "t": 201.07799999999997, "r": 94.462, "b": 207.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Poor", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 201.07799999999997, "r": 200.163, "b": 207.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "64", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 201.07799999999997, "r": 266.263, "b": 207.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "37", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 201.07799999999997, "r": 335.363, "b": 207.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "27", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 72.0, "t": 210.07799999999997, "r": 106.552, "b": 216.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "tNM stage", "column_header": false, "row_header": true, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.322, "t": 210.07799999999997, "r": 411.466, "b": 216.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.007", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 80.08, "t": 219.07799999999997, "r": 88.152, "b": 225.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "i-ii", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 219.07799999999997, "r": 200.163, "b": 225.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "61", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 219.07799999999997, "r": 266.263, "b": 225.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "28", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 219.07799999999997, "r": 335.363, "b": 225.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "33", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 80.08, "t": 228.07799999999997, "r": 94.264, "b": 234.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "iii-iV", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 192.483, "t": 228.07799999999997, "r": 200.163, "b": 234.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "49", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 258.582, "t": 228.07799999999997, "r": 266.263, "b": 234.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "35", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 327.683, "t": 228.07799999999997, "r": 335.363, "b": 234.89299999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "14", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}]]}}, {"self_ref": "#/tables/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 7, "bbox": {"l": 71.14335632324219, "t": 656.6398429870605, "r": 432.2460021972656, "b": 574.2369842529297, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 72.0, "t": 79.07799999999997, "r": 117.755, "b": 85.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "characteristics", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 267.149, "t": 66.57799999999997, "r": 346.866, "b": 73.39300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 4, "text": "Multivariate cox analysis", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 215.077, "t": 79.07799999999997, "r": 223.925, "b": 85.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "HR", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 297.071, "t": 79.07799999999997, "r": 316.935, "b": 85.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "95%ci", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 382.549, "t": 79.07799999999997, "r": 406.46, "b": 85.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "P value", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 72.0, "t": 91.07799999999997, "r": 105.872, "b": 97.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "liNc01018", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 213.122, "t": 91.07799999999997, "r": 230.266, "b": 97.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "3.467", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 285.845, "t": 91.07799999999997, "r": 328.173, "b": 97.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1.098-10.944", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 388.122, "t": 91.07799999999997, "r": 405.266, "b": 97.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.034", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 72.0, "t": 100.07799999999997, "r": 84.31, "b": 106.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Age", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 213.122, "t": 100.07799999999997, "r": 230.266, "b": 106.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.529", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 287.784, "t": 100.07799999999997, "r": 326.232, "b": 106.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "0.630-3.709", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 388.122, "t": 100.07799999999997, "r": 405.266, "b": 106.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.347", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 72.0, "t": 109.07799999999997, "r": 116.565, "b": 115.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON> score", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 213.122, "t": 109.07799999999997, "r": 230.266, "b": 115.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.417", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 287.784, "t": 109.07799999999997, "r": 326.232, "b": 115.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "0.996-5.867", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 388.122, "t": 109.07799999999997, "r": 405.266, "b": 115.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.051", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 72.0, "t": 118.07799999999997, "r": 148.64, "b": 124.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "lymph node metastasis", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 213.122, "t": 118.07799999999997, "r": 230.266, "b": 124.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.217", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 287.784, "t": 118.07799999999997, "r": 326.232, "b": 124.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "0.903-5.443", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 388.122, "t": 118.07799999999997, "r": 405.266, "b": 124.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.082", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 72.0, "t": 127.07799999999997, "r": 117.642, "b": 133.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "differentiation", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 213.122, "t": 127.07799999999997, "r": 230.266, "b": 133.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.511", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 287.784, "t": 127.07799999999997, "r": 326.232, "b": 133.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "0.622-3.667", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 388.122, "t": 127.07799999999997, "r": 405.266, "b": 133.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.362", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 72.0, "t": 136.07799999999997, "r": 106.552, "b": 142.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "tNM stage", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 213.122, "t": 136.07799999999997, "r": 230.266, "b": 142.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.881", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 287.784, "t": 136.07799999999997, "r": 326.232, "b": 142.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1.067-7.776", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 388.122, "t": 136.07799999999997, "r": 405.266, "b": 142.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.037", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 8, "num_cols": 4, "grid": [[{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 267.149, "t": 66.57799999999997, "r": 346.866, "b": 73.39300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 4, "text": "Multivariate cox analysis", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 267.149, "t": 66.57799999999997, "r": 346.866, "b": 73.39300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 4, "text": "Multivariate cox analysis", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 267.149, "t": 66.57799999999997, "r": 346.866, "b": 73.39300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 4, "text": "Multivariate cox analysis", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 72.0, "t": 79.07799999999997, "r": 117.755, "b": 85.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "characteristics", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 215.077, "t": 79.07799999999997, "r": 223.925, "b": 85.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "HR", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 297.071, "t": 79.07799999999997, "r": 316.935, "b": 85.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "95%ci", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 382.549, "t": 79.07799999999997, "r": 406.46, "b": 85.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "P value", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 72.0, "t": 91.07799999999997, "r": 105.872, "b": 97.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "liNc01018", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 213.122, "t": 91.07799999999997, "r": 230.266, "b": 97.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "3.467", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 285.845, "t": 91.07799999999997, "r": 328.173, "b": 97.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1.098-10.944", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 388.122, "t": 91.07799999999997, "r": 405.266, "b": 97.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.034", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 72.0, "t": 100.07799999999997, "r": 84.31, "b": 106.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Age", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 213.122, "t": 100.07799999999997, "r": 230.266, "b": 106.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.529", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 287.784, "t": 100.07799999999997, "r": 326.232, "b": 106.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "0.630-3.709", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 388.122, "t": 100.07799999999997, "r": 405.266, "b": 106.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.347", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 72.0, "t": 109.07799999999997, "r": 116.565, "b": 115.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "<PERSON><PERSON><PERSON> score", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 213.122, "t": 109.07799999999997, "r": 230.266, "b": 115.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.417", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 287.784, "t": 109.07799999999997, "r": 326.232, "b": 115.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "0.996-5.867", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 388.122, "t": 109.07799999999997, "r": 405.266, "b": 115.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.051", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 72.0, "t": 118.07799999999997, "r": 148.64, "b": 124.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "lymph node metastasis", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 213.122, "t": 118.07799999999997, "r": 230.266, "b": 124.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.217", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 287.784, "t": 118.07799999999997, "r": 326.232, "b": 124.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "0.903-5.443", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 388.122, "t": 118.07799999999997, "r": 405.266, "b": 124.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.082", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 72.0, "t": 127.07799999999997, "r": 117.642, "b": 133.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "differentiation", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 213.122, "t": 127.07799999999997, "r": 230.266, "b": 133.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "1.511", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 287.784, "t": 127.07799999999997, "r": 326.232, "b": 133.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "0.622-3.667", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 388.122, "t": 127.07799999999997, "r": 405.266, "b": 133.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.362", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 72.0, "t": 136.07799999999997, "r": 106.552, "b": 142.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "tNM stage", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 213.122, "t": 136.07799999999997, "r": 230.266, "b": 142.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2.881", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 287.784, "t": 136.07799999999997, "r": 326.232, "b": 142.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "1.067-7.776", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 388.122, "t": 136.07799999999997, "r": 405.266, "b": 142.89300000000003, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.037", "column_header": false, "row_header": false, "row_section": false}]]}}], "key_value_items": [], "form_items": [], "pages": {"1": {"size": {"width": 504.0, "height": 720.0}, "page_no": 1}, "2": {"size": {"width": 504.0, "height": 720.0}, "page_no": 2}, "3": {"size": {"width": 504.0, "height": 720.0}, "page_no": 3}, "4": {"size": {"width": 504.0, "height": 720.0}, "page_no": 4}, "5": {"size": {"width": 504.0, "height": 720.0}, "page_no": 5}, "6": {"size": {"width": 504.0, "height": 720.0}, "page_no": 6}, "7": {"size": {"width": 504.0, "height": 720.0}, "page_no": 7}, "8": {"size": {"width": 504.0, "height": 720.0}, "page_no": 8}, "9": {"size": {"width": 504.0, "height": 720.0}, "page_no": 9}, "10": {"size": {"width": 504.0, "height": 720.0}, "page_no": 10}, "11": {"size": {"width": 504.0, "height": 720.0}, "page_no": 11}, "12": {"size": {"width": 504.0, "height": 720.0}, "page_no": 12}, "13": {"size": {"width": 504.0, "height": 720.0}, "page_no": 13}}}