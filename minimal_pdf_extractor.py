#!/usr/bin/env python3
"""
Minimal PDF extractor with linebreaks.

The simplest possible implementation to extract PDF text with linebreaks.

Author: <PERSON> Jadhav
Date: 2025-01-27
"""

import json
import re
from pathlib import Path
from docling.document_converter import DocumentConverter


def extract_pdf_with_linebreaks(pdf_path: str, output_path: str):
    """
    Extract PDF text with linebreaks and save to JSON.
    
    Args:
        pdf_path: Path to PDF file
        output_path: Path to save JSON output
    """
    # Step 1: Convert PDF using Docling
    converter = DocumentConverter()
    result = converter.convert(pdf_path)
    data = result.document.export_to_dict()
    
    # Step 2: Add linebreaks to all text content
    add_linebreaks_to_data(data)
    
    # Step 3: Save to JSON
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)


def add_linebreaks_to_data(data):
    """Add linebreaks to all text in the data."""
    
    # Process pages
    if 'pages' in data:
        for page in data['pages'].values():
            if 'text' in page:
                page['text'] = add_linebreaks_to_text(page['text'])
    
    # Process text items
    if 'texts' in data:
        for item in data['texts']:
            if 'text' in item:
                item['text'] = add_linebreaks_to_text(item['text'])
    
    # Process table cells
    if 'tables' in data:
        for table in data['tables']:
            if 'cells' in table:
                for cell in table['cells']:
                    if 'text' in cell:
                        cell['text'] = add_linebreaks_to_text(cell['text'])


def add_linebreaks_to_text(text):
    """Add linebreaks to text string."""
    
    if not text or not isinstance(text, str):
        return text
    
    text = text.strip()
    
    # Split by sentence endings (period, exclamation, question mark)
    sentences = re.split(r'(?<=[.!?])\s+(?=[A-Z])', text)
    
    if len(sentences) > 1:
        return '\n'.join(sentences)
    
    # If no sentences found, try splitting by periods
    if '. ' in text:
        parts = text.split('. ')
        if len(parts) > 1:
            return '.\n'.join(parts[:-1]) + '.' + parts[-1]
    
    # Return original text if no splits possible
    return text


def main():
    """Main function."""
    
    # Your file paths
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    output_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder\minimal_output.json"
    
    # Check if PDF exists
    if not Path(pdf_file).exists():
        print("PDF file not found!")
        return
    
    # Create output directory
    Path(output_file).parent.mkdir(parents=True, exist_ok=True)
    
    # Extract PDF with linebreaks
    extract_pdf_with_linebreaks(pdf_file, output_file)
    
    # Show results
    print(f"Extraction completed: {output_file}")
    
    # Quick check
    with open(output_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    if 'pages' in data and data['pages']:
        first_page = list(data['pages'].values())[0]
        if 'text' in first_page:
            text = first_page['text']
            linebreaks = text.count('\n')
            print(f"Linebreaks in first page: {linebreaks}")
            if linebreaks > 0:
                print("SUCCESS: Linebreaks found!")
            else:
                print("No linebreaks found")


if __name__ == "__main__":
    main()
