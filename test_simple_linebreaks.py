#!/usr/bin/env python3
"""
Test the simple linebreak approach to verify it works.

This script tests the simplified linebreak insertion method.

Author: <PERSON> J<PERSON>hav
Date: 2025-01-27
"""

import os
import json
import sys
from pathlib import Path

def test_simple_linebreaks():
    """Test the simple linebreak approach."""
    
    print("🧪 Testing Simple Linebreak Approach")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not Path("metaparse/extractors/doc_extractor_linebreak.py").exists():
        print("❌ Error: Please run this script from the MetaParse project root directory")
        return False
    
    try:
        # Import the extractor
        sys.path.insert(0, '.')
        from metaparse.extractors.doc_extractor_linebreak import DocExtractor
        
        # Your PDF file
        pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
        output_folder = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder"
        
        if not Path(pdf_file).exists():
            print(f"❌ PDF file not found: {pdf_file}")
            return False
        
        print(f"📄 Testing with: {Path(pdf_file).name}")
        
        # Create extractor
        print(f"\n🔄 Creating DocExtractor...")
        extractor = DocExtractor(
            pdf_file,
            optimization_level='quality',
            use_cache=False
        )
        
        # Extract text
        print(f"🔄 Extracting text with simple linebreak approach...")
        result = extractor.extract_text_optimized()
        
        # Save result
        Path(output_folder).mkdir(parents=True, exist_ok=True)
        output_file = Path(output_folder) / f"{Path(pdf_file).stem}_simple_linebreaks.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Output saved to: {output_file}")
        
        # Analyze the results
        analyze_simple_results(result, output_file)
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_simple_results(result: dict, output_file: Path):
    """Analyze the simple linebreak results."""
    
    print(f"\n📊 Simple Linebreak Analysis:")
    print("-" * 35)
    
    # Basic stats
    file_size = output_file.stat().st_size
    print(f"📁 Output file size: {file_size:,} bytes")
    
    if 'pages' in result and result['pages']:
        total_pages = len(result['pages'])
        print(f"📄 Total pages: {total_pages}")
        
        # Analyze first page
        first_page_key = list(result['pages'].keys())[0]
        first_page = result['pages'][first_page_key]
        
        if 'text' in first_page:
            text = first_page['text']
            
            # Count various elements
            total_chars = len(text)
            linebreak_count = text.count('\n')
            word_count = len(text.split())
            
            print(f"\n📝 First Page Analysis:")
            print(f"   Total characters: {total_chars:,}")
            print(f"   Word count: {word_count:,}")
            print(f"   Linebreaks (\\n): {linebreak_count}")
            
            if linebreak_count > 0:
                print(f"   ✅ SUCCESS: Simple linebreaks are working!")
                
                # Calculate linebreak density
                linebreak_density = (linebreak_count / total_chars) * 100 if total_chars > 0 else 0
                print(f"   📊 Linebreak density: {linebreak_density:.2f}%")
                
            else:
                print(f"   ❌ ISSUE: Simple linebreak approach didn't work")
            
            # Check for preservation flags
            if first_page.get('linebreaks_preserved'):
                print(f"   ✅ Linebreak preservation flag: True")
                
                if 'sentence_count' in first_page:
                    print(f"   📝 Sentences detected: {first_page['sentence_count']}")
                
                if 'line_count' in first_page:
                    print(f"   📄 Lines created: {first_page['line_count']}")
                    
            else:
                print(f"   ⚠️ Linebreak preservation flag: Missing or False")
            
            # Show sample text with visible linebreaks
            print(f"\n📖 Sample Text (first 300 characters):")
            sample_text = text[:300]
            
            # Show with visible linebreaks
            display_text = sample_text.replace('\n', '\\n\n   ')
            print(f"   '{display_text}...'")
            
            # Count linebreaks in sample
            sample_linebreaks = sample_text.count('\n')
            print(f"\n🔍 Linebreaks in sample: {sample_linebreaks}")
            
            if sample_linebreaks > 0:
                print(f"   ✅ Linebreaks are visible in the sample text!")
                
                # Show the actual lines
                lines = sample_text.split('\n')
                print(f"\n📋 Sample lines:")
                for i, line in enumerate(lines[:3], 1):
                    print(f"   Line {i}: '{line.strip()}'")
                    
            else:
                print(f"   ❌ No linebreaks in the sample text")
            
        else:
            print("⚠️ No text found in first page")
    
    else:
        print("❌ No pages found in extracted data")
    
    # Check other sections
    sections = ['texts', 'tables', 'pictures']
    for section in sections:
        if section in result and result[section]:
            count = len(result[section])
            print(f"📋 {section.capitalize()}: {count} items")

def main():
    """Main function to run the simple linebreak test."""
    
    print("🚀 Simple Linebreak Test")
    print("=" * 30)
    
    print("\n💡 This test verifies that the simple linebreak approach")
    print("   (sentence splitting + word wrapping) is working correctly.")
    
    success = test_simple_linebreaks()
    
    if success:
        print(f"\n🎉 Simple linebreak test completed!")
        
        print(f"\n📚 What to check:")
        print("✅ Look for \\n characters in the sample text above")
        print("✅ Check that 'linebreaks_preserved': true")
        print("✅ Verify 'sentence_count' or 'line_count' fields")
        print("✅ Open the JSON file to see the full extracted text")
        
        print(f"\n🔧 If this works, run the main extractor:")
        print("   python metaparse/extractors/doc_extractor_linebreak.py")
        
        print(f"\n🔍 If this doesn't work, we need to debug further:")
        print("   python debug_linebreak_issue.py")
        print("   python test_raw_docling.py")
        
    else:
        print(f"\n❌ Simple linebreak test failed.")
        print("   This indicates a fundamental issue with the approach.")
    
    print(f"\n✨ Test completed!")

if __name__ == "__main__":
    main()
