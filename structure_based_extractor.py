#!/usr/bin/env python3
"""
Structure-based PDF extractor that preserves original linebreaks.

This module uses <PERSON><PERSON>'s document structure to preserve the original
linebreaks as they appear in the PDF layout.

Author: Anand Jadhav
Date: 2025-01-27
"""

import json
from pathlib import Path
from typing import Dict, Any, List
from docling.document_converter import DocumentConverter


def extract_pdf_with_structure_linebreaks(pdf_path: str, output_path: str):
    """
    Extract PDF text preserving original structure-based linebreaks.
    
    Args:
        pdf_path: Path to PDF file
        output_path: Path to save JSON output
    """
    # Convert PDF using Docling
    converter = DocumentConverter()
    result = converter.convert(pdf_path)
    data = result.document.export_to_dict()
    
    # Reconstruct text with original linebreaks using document structure
    reconstruct_text_with_original_linebreaks(data)
    
    # Save to JSON
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)


def reconstruct_text_with_original_linebreaks(data: Dict[str, Any]):
    """
    Reconstruct text using document structure to preserve original linebreaks.
    
    Args:
        data: Document data dictionary to modify
    """
    # Process each page
    if 'pages' in data and 'texts' in data:
        for page_num, page_data in data['pages'].items():
            if 'text' in page_data:
                # Reconstruct page text from individual text elements
                reconstructed_text = reconstruct_page_text_from_elements(
                    data['texts'], page_num
                )
                if reconstructed_text:
                    page_data['text'] = reconstructed_text


def reconstruct_page_text_from_elements(text_elements: List[Dict], page_num: str) -> str:
    """
    Reconstruct page text from individual text elements with proper linebreaks.
    
    Args:
        text_elements: List of text elements from the document
        page_num: Page number to process
        
    Returns:
        Reconstructed text with proper linebreaks
    """
    # Get text elements for this page
    page_elements = []
    
    for element in text_elements:
        if (element.get('prov') and 
            len(element['prov']) > 0 and
            str(element['prov'][0].get('page_no', '')) == str(page_num)):
            
            text_content = element.get('text', '').strip()
            if text_content:
                bbox = element['prov'][0].get('bbox', {})
                page_elements.append({
                    'text': text_content,
                    'top': bbox.get('t', 0),
                    'bottom': bbox.get('b', 0),
                    'left': bbox.get('l', 0),
                    'right': bbox.get('r', 0)
                })
    
    if not page_elements:
        return ""
    
    # Sort elements by position (top to bottom, then left to right)
    page_elements.sort(key=lambda x: (x['top'], x['left']))
    
    # Reconstruct text with linebreaks based on position
    text_parts = []
    last_bottom = None
    last_right = None
    
    for element in page_elements:
        text = element['text']
        current_top = element['top']
        current_left = element['left']
        
        if last_bottom is not None:
            # Calculate vertical gap
            vertical_gap = current_top - last_bottom
            
            # If there's a significant vertical gap, add linebreak
            if vertical_gap > 5:  # Threshold for line break
                # Check if it's a paragraph break (larger gap)
                if vertical_gap > 15:
                    text_parts.append('\n\n')
                else:
                    text_parts.append('\n')
            
            # If elements are on the same line but separated, add space
            elif vertical_gap <= 5 and last_right is not None:
                horizontal_gap = current_left - last_right
                if horizontal_gap > 10:  # Threshold for word separation
                    text_parts.append(' ')
        
        text_parts.append(text)
        last_bottom = element['bottom']
        last_right = element['right']
    
    return ''.join(text_parts)


def main():
    """Main function."""
    
    # Your file paths
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    output_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder\structure_based_output.json"
    
    # Check if PDF exists
    if not Path(pdf_file).exists():
        print("PDF file not found!")
        return
    
    # Create output directory
    Path(output_file).parent.mkdir(parents=True, exist_ok=True)
    
    # Extract PDF with structure-based linebreaks
    extract_pdf_with_structure_linebreaks(pdf_file, output_file)
    
    # Show results
    print(f"Extraction completed: {output_file}")
    
    # Quick check
    with open(output_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    if 'pages' in data and data['pages']:
        first_page = list(data['pages'].values())[0]
        if 'text' in first_page:
            text = first_page['text']
            linebreaks = text.count('\n')
            print(f"Linebreaks in first page: {linebreaks}")
            print(f"Sample text:\n{text[:200]}...")


if __name__ == "__main__":
    main()
