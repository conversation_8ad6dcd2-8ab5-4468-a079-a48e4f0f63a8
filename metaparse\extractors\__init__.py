"""
Document extraction modules for the MetaParse package.

This module contains classes and functions for extracting content from documents.
"""

# Import extractor components for easier access
from metaparse.extractors.doc_extractor import DocExtractor, DocCache, DocExtractorConfig

# Define what's available when importing from this module
__all__ = ['DocExtractor', 'DocCache', 'DocExtractorConfig']
