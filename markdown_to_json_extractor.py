#!/usr/bin/env python3
"""
Markdown-to-JSON PDF extractor that preserves linebreaks.

This module uses <PERSON><PERSON>'s markdown export (which has linebreaks) and 
converts it to the required JSON structure with preserved linebreaks.

Author: <PERSON> Jadhav
Date: 2025-01-27
"""

import json
import re
from pathlib import Path
from typing import Dict, Any
from docling.document_converter import DocumentConverter


class MarkdownToJSONExtractor:
    """PDF extractor that uses markdown to preserve linebreaks in JSON."""
    
    def __init__(self):
        """Initialize the PDF extractor."""
        self.converter = DocumentConverter()
    
    def extract_with_linebreaks(self, pdf_path: str) -> Dict[str, Any]:
        """
        Extract text from PDF using markdown to preserve linebreaks.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Dictionary with extracted text containing linebreaks from markdown
        """
        # Convert PDF using Docling
        result = self.converter.convert(pdf_path)
        
        # Get both dictionary structure and markdown text
        data = result.document.export_to_dict()
        markdown_text = result.document.export_to_markdown()
        
        # Update the dictionary with markdown text that has linebreaks
        self._update_text_from_markdown(data, markdown_text)
        
        return data
    
    def _update_text_from_markdown(self, data: Dict[str, Any], markdown_text: str) -> None:
        """
        Update the dictionary text fields with markdown text that has linebreaks.
        
        Args:
            data: Document data dictionary to modify
            markdown_text: Markdown text with linebreaks
        """
        # Clean up markdown text to get plain text with linebreaks
        clean_text = self._clean_markdown_text(markdown_text)
        
        # Update page text with the cleaned markdown text
        if 'pages' in data and data['pages']:
            # For now, put all the markdown text in the first page
            # This preserves the linebreaks from markdown
            first_page_key = list(data['pages'].keys())[0]
            data['pages'][first_page_key]['text'] = clean_text
            
            # If there are multiple pages, we could split the markdown text
            # but for simplicity, we'll use the full text in the first page
    
    def _clean_markdown_text(self, markdown_text: str) -> str:
        """
        Clean markdown text to get plain text while preserving linebreaks.
        
        Args:
            markdown_text: Raw markdown text from Docling
            
        Returns:
            Clean text with preserved linebreaks
        """
        if not markdown_text:
            return ""
        
        # Remove markdown formatting but keep linebreaks
        text = markdown_text
        
        # Remove markdown headers (# ## ###)
        text = re.sub(r'^#{1,6}\s+', '', text, flags=re.MULTILINE)
        
        # Remove markdown bold/italic (**text** *text*)
        text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)
        text = re.sub(r'\*([^*]+)\*', r'\1', text)
        
        # Remove markdown links [text](url)
        text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)
        
        # Remove markdown code blocks ```
        text = re.sub(r'```[^`]*```', '', text, flags=re.DOTALL)
        
        # Remove inline code `text`
        text = re.sub(r'`([^`]+)`', r'\1', text)
        
        # Clean up extra whitespace but preserve single linebreaks
        # Replace multiple spaces with single space
        text = re.sub(r' +', ' ', text)
        
        # Clean up multiple consecutive linebreaks (keep max 2)
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # Strip leading/trailing whitespace
        text = text.strip()
        
        return text


def extract_pdf_with_markdown_linebreaks(pdf_path: str, output_path: str):
    """
    Extract PDF using markdown linebreaks and save to JSON.
    
    Args:
        pdf_path: Path to PDF file
        output_path: Path to save JSON output
    """
    # Extract using markdown
    extractor = MarkdownToJSONExtractor()
    data = extractor.extract_with_linebreaks(pdf_path)
    
    # Save to JSON
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    return data


def main():
    """Main function."""
    
    # Your file paths
    pdf_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\pdf_folder\04760587J.article.002.pdf"
    output_file = r"C:\Users\<USER>\Desktop\Anand\MetaParse\extracted_data_folder\markdown_to_json_output.json"
    
    # Check if PDF exists
    if not Path(pdf_file).exists():
        print("PDF file not found!")
        return
    
    # Create output directory
    Path(output_file).parent.mkdir(parents=True, exist_ok=True)
    
    # Extract PDF using markdown linebreaks
    print("Extracting PDF using markdown linebreaks...")
    data = extract_pdf_with_markdown_linebreaks(pdf_file, output_file)
    
    print(f"Extraction completed: {output_file}")
    
    # Analyze results
    if 'pages' in data and data['pages']:
        first_page = list(data['pages'].values())[0]
        if 'text' in first_page:
            text = first_page['text']
            linebreaks = text.count('\n')
            
            print(f"Pages extracted: {len(data['pages'])}")
            print(f"First page text length: {len(text)} characters")
            print(f"Linebreaks found: {linebreaks}")
            
            if linebreaks > 0:
                print("SUCCESS: Linebreaks preserved from markdown!")
                
                # Show sample with visible linebreaks
                sample = text[:300]
                print(f"\nSample text (first 300 chars):")
                print(f"'{sample}...'")
                
                # Show lines
                lines = text.split('\n')
                print(f"\nFirst 3 lines:")
                for i, line in enumerate(lines[:3], 1):
                    print(f"  Line {i}: '{line.strip()}'")
                    
            else:
                print("ISSUE: No linebreaks found even from markdown")
        else:
            print("No text found in first page")
    else:
        print("No pages found")


if __name__ == "__main__":
    main()
